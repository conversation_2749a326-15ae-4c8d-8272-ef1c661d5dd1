import { z } from 'zod';

// --- Structure Definition ---
// Defines the data fields required for each slide type. Descriptions are concise and direct.

const SlideDataSchema = z.union([
  // Title Slide
  z
    .object({
      subtitle: z.string().describe('Subtitle of the presentation'),
      author: z.string().describe('Author or organization name'),
      date: z.string().describe('Date of the presentation'),
    })
    .strict(),
  // Content Slide
  z
    .object({
      sections: z.array(
        z.object({
          heading: z.string().describe('Section heading'),
          text: z.string().describe('Paragraph content'),
        }),
      ),
    })
    .strict(),
  // Image Slide
  z
    .object({
      image_description: z
        .string()
        .describe('A detailed description of the image, suitable for AI image generation or as alt text'),
      caption: z.string().describe('Caption text for the image'),
      layout: z.enum(['full', 'left', 'right', 'center']).describe('Layout of the image on the slide'),
    })
    .strict(),
  // Chart Slide (ECharts)
  z
    .object({
      chart_type: z
        .enum(['bar', 'line', 'pie', 'scatter', 'area', 'radar', 'funnel', 'gauge', 'heatmap', 'treemap'])
        .describe('The type of chart to render'),
      data_description: z.string().describe("A brief textual description of the chart's data and key insights"),
      sample_data: z
        .array(
          z.object({
            label: z.string(),
            value: z.number(),
            category: z.string().optional().describe('Category for multi-series charts'),
            extra_data: z
              .record(z.union([z.string(), z.number()]))
              .optional()
              .describe('Additional data fields for complex ECharts visualizations'),
          }),
        )
        .describe('Sample data for generating the chart'),
      chart_config: z
        .object({
          title: z.string().optional().describe('Main title for the chart'),
          subtitle: z.string().optional().describe('Subtitle for the chart'),
          x_axis_label: z.string().optional().describe('X-axis label'),
          y_axis_label: z.string().optional().describe('Y-axis label'),
          show_legend: z.boolean().optional().describe('Whether to display the legend'),
          color_scheme: z.array(z.string()).optional().describe('Custom color scheme for the chart'),
        })
        .optional()
        .describe('Additional ECharts configuration options for fine-tuning the visual appearance'),
    })
    .strict(),
  // Quote Slide
  z
    .object({
      quote_text: z.string().describe('The main text of the quote'),
      author: z.string().describe('The source or author of the quote'),
      context: z.string().optional().describe('Optional background or context for the quote'),
    })
    .strict(),
  // List Slide
  z
    .object({
      list_type: z.enum(['bullet', 'numbered', 'checklist']).describe('The type of list'),
      items: z.array(
        z.object({
          text: z.string().describe('The content of the list item'),
          sub_items: z.array(z.string()).optional().describe('Nested sub-items'),
        }),
      ),
    })
    .strict(),
  // Table Slide
  z
    .object({
      headers: z.array(z.string()).describe('Column headers for the table'),
      rows: z.array(z.array(z.string())).describe('Data rows for the table'),
    })
    .strict(),
  // Video Slide
  z
    .object({
      video_description: z.string().describe('A description of the video content for the placeholder'),
      video_url: z.string().describe('The direct URL to the video file (e.g., .mp4)'),
      placeholder_text: z.string().describe('Text to display on the video placeholder'),
      is_iframe: z
        .boolean()
        .describe(
          'Whether the video should be embedded as an iframe. If true, the video_url should be a valid iframe source URL.',
        ),
    })
    .strict(),
  // Code Slide
  z
    .object({
      language: z.string().describe('The programming language of the code (e.g., javascript, python)'),
      code_snippet: z.string().describe('The code snippet itself'),
      explanation: z.string().describe('An explanation of the code snippet'),
    })
    .strict(),
]);

// --- Core Directives ---
// This is the most important part, centralizing all design philosophy, technical requirements, and creative guidance.

export const SlidesOutlineSchema = z.object({
  userPrompt: z
    .string()
    .describe(
      "The user's original request, including content topic, style preferences, color requirements, etc. Preferences stated here should take priority in guiding the theme selection.",
    ),
  outline: z.object({
    title: z.string().describe('The main title of the presentation'),
    slides: z.array(
      z.object({
        slide_number: z.number().describe('The slide number'),
        slide_title: z.string().describe('The title for the current slide'),
        slide_type: z
          .enum(['title', 'content', 'image', 'chart', 'quote', 'list', 'table', 'video', 'code'])
          .describe('The core content type of the slide'),
        slide_data: SlideDataSchema.describe('The specific data structure, determined by the slide_type'),
        description: z.string().describe("A brief summary of the current slide's content"),
      }),
    ),
  }),
  slide_template: z.object({
    theme: z
      .object({
        color_primary: z.string().describe('Primary color (var(--color-primary)) - The main brand or core color.'),
        color_secondary: z
          .string()
          .describe('Secondary color (var(--color-secondary)) - A supporting color to complement the primary.'),
        color_text: z.string().describe('Text color (var(--color-text)) - Ensures high contrast and readability.'),
        color_accent: z
          .string()
          .describe('Accent color (var(--color-accent)) - For highlights, links, and interactive elements.'),
        color_background: z
          .string()
          .describe('Background color (var(--color-background)) - The main background outside the content area.'),
        color_surface: z
          .string()
          .describe('Surface color (var(--color-surface)) - Background for cards, containers, etc.'),
      })
      .describe(
        'Defines the global color theme for the presentation. A modern, harmonious, and aesthetically pleasing color palette (e.g., professional dark or clean light themes) should be chosen based on user preferences and content.',
      ),
    template: z.string().describe(`# HTML Template Creative Mandate

## 🎯 Your Mission
Your role is that of a **world-class presentation designer**. Your goal is to craft a **visually stunning, professional, and modern** single-page HTML slide. The design must be creative, memorable, and impactful.

## 🎨 Aesthetic Principles (Crucial for a beautiful result)
1.  **Visually Stunning & Evocative Design**:
    *   **AVOID** generic, bland, or boring corporate templates.
    *   **AIM FOR** the quality of a keynote from a major tech conference (e.g., Apple, Google I/O) or a top-rated design on Dribbble/Behance.
    *   Embrace bold, elegant, or minimalist aesthetics. The design should feel intentional and sophisticated.

2.  **Typographic Excellence**:
    *   Treat typography as a primary design element. Use modern, readable Google Fonts.
    *   Establish a clear visual hierarchy with font sizes, weights, and styles.
    *   Don't be afraid to use large, impactful headings or sophisticated font pairings.

3.  **The Background as a Masterpiece: Your Primary Decorative Canvas**
    *   **THIS IS THE MOST CRITICAL PRINCIPLE.** The slide's background is **NOT** a passive container. It **IS** the primary visual artwork. Your main creative effort must be focused on making the background a stunning and decorative piece in its own right.
    *   **MANDATORY DECORATIVE TECHNIQUE:** You **MUST** use CSS to create bold, abstract, and irregular shapes directly on the \`<body>\` or its pseudo-elements (\`::before\`, \`::after\`). This is the designated method for adding visual impact and character.
    *   **IMPLEMENTATION STYLES (Choose one or combine):**
        *   **Fluid Organic Forms (Blobs):** Generate large, soft-edged, overlapping liquid shapes. Use stacked \`radial-gradient\`s or extreme, non-uniform \`border-radius\` to create a sense of depth and fluidity. This is great for modern, approachable designs.
        *   **Geometric Shards & Edges:** Use \`clip-path: polygon()\` to create sharp, asymmetrical, and overlapping geometric fragments. This style evokes a feeling of technology, precision, and high energy.
    *   **AVOID AT ALL COSTS:**
        *   A simple, single-color background.
        *   A basic two-color linear gradient.
        *   Any background that looks like a generic, default template.
    *   The purpose of this background art is to create **dynamic composition, visual hierarchy, and a memorable identity** for the slide. Content containers placed on top should be mostly transparent to proudly display the masterpiece underneath.

## 🛠️ Core Technical Directives (Must be followed)
1.  **Body-First Rendering**: The \`<body>\` element is your canvas for all background art. Content layers on top with proper \`z-index\`.
2.  **Iframe-Friendly & Responsive Layout**:
    *   Slide height is determined by content (\`height: auto\`) with a reasonable \`min-height\`.
    *   **STRICTLY FORBIDDEN**: Do not use viewport units (\`vh\`, \`vw\`). They break iframe embedding.
    *   Use generous \`padding\` (e.g., \`3rem\` to \`5rem\`) for internal spacing.
3.  **Theming with CSS Variables**: Strictly use the CSS variables defined in the \`theme\` object for all colors to ensure consistency.

## 📚 Resources & Tech Stack
*   **CSS Framework**: Tailwind CSS
*   **Charting Library**: ECharts (for 'chart' type slides)
*   **Icons**: Font Awesome
*   **Fonts**: Google Fonts
*   Include all resources via CDN links in the HTML \`<head>\`.

## 📊 ECharts Guidelines
*   For 'chart' type slides, you **must** use ECharts to render an interactive, responsive chart.
*   The chart's theme and colors must align beautifully with the overall slide design. Use the \`chart_config.color_scheme\` or derive colors from the main theme.
`),
  }),
});

export const SlideSchema = z.object({
  slide_number: z.number(),
  title: z.string(),
  type: z.string(),
  html_content: z
    .string()
    .describe(
      "A complete, self-contained HTML document. It **must** strictly adhere to all design principles and technical requirements defined in `slide_template.template`. Pay special attention to the 'Body-First' background design, iframe-friendly layout, and the correct implementation of ECharts.",
    ),
});

export const SlidesSchema = z.array(SlideSchema);

export const SlidesArtifactSchema = z.object({
  slides: SlidesSchema.describe(
    'The array of generated slides. Each slide is an independent, well-designed HTML file, collectively forming a complete and stylistically consistent presentation.',
  ),
});

export type SlidesArtifactVO = z.infer<typeof SlidesArtifactSchema>;
export type Slides = z.infer<typeof SlidesSchema>;
export type Slide = z.infer<typeof SlideSchema>;
export type SlidesOutline = z.infer<typeof SlidesOutlineSchema>;
