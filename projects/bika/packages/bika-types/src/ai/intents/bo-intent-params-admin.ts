import { z } from 'zod';
import { AIIntentTypeSchema } from '../bo-intent-types';

export const AIAdminChatTypes = [
  'reasoning-chat',
  'consultant',
  'business-analyst',

  'solution-designer',
  'solution-engineer',
  'error',
] as const;
export const AIAdminChatTypeSchema = z.enum(AIAdminChatTypes);
export type AIAdminChatType = z.infer<typeof AIAdminChatTypeSchema>;

export const AdminIntentParamsSchema = z.object({
  type: z.literal(AIIntentTypeSchema.enum.ADMIN),
  chatType: AIAdminChatTypeSchema.optional(),
  // resources: NodeResourceSchema.array().optional(),
  spaceId: z.string().optional(),
});

export type AdminIntentParams = z.infer<typeof AdminIntentParamsSchema>;
