import { z } from 'zod';
import { CustomAIIntegrationSchema } from '../integration/bo-ai-integrations';
import { NodeResourceTypeSchema, BaseNodeResourceBOSchema } from '../node/base';
import { SkillsetSelectBOSchema } from '../skill/bo';

const AINodeSourceSitemap = z.object({
  type: z.literal('SITEMAP'),
  url: z.string(),
});
const AINodeSourceURL = z.object({
  type: z.literal('URL'),
  url: z.string(),
});
const AINodeSourceNode = z.object({
  type: z.literal('NODE'),
  nodeId: z.string(),
});
export const AINodeSourceBOSchema = z.discriminatedUnion('type', [
  AINodeSourceSitemap,
  AINodeSourceURL,
  AINodeSourceNode,
]);
export type AINodeSourceBO = z.infer<typeof AINodeSourceBOSchema>;
export type AINodeSourceType = AINodeSourceBO['type'];

/**
 * @deprecated
 */
export const AINodeToolBOSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('TOOLSDK'),
    packageKey: z.string(),
    packageVersion: z.string().optional(),

    // filter tools
    includeTools: z.array(z.string()).optional(),
  }),
  z.object({
    type: z.literal('AUTOMATION'),
    automationId: z.string(),
  }),
]);
/**
 * @deprecated
 */
export type AINodeToolBO = z.infer<typeof AINodeToolBOSchema>;

export const AINodeAgentSchema = z.object({
  // AI model selection, configure integration ID or manual configuration
  aiModel: z.string().or(CustomAIIntegrationSchema).optional(),
  prompt: z.string().optional().describe(`The system prompt for the AI agent.`),

  // Select index sources
  // Reference and index data from other nodes
  sources: z.array(AINodeSourceBOSchema).optional(),

  skillsets: z.array(SkillsetSelectBOSchema).optional(),

  // AI skillsets mode
  // AUTO: auto detect skillsets from AI model
  // EDITABLE: allow user to edit skillsets
  // EXTENSIBLE: allow user to add new skillsets
  // READONLY: only show skillsets, not editable
  // Default is EDITABLE, means no skillsets, this agent will be a pure Chatbot without tools
  skillsetsMode: z.enum(['AUTO', 'EDITABLE', 'EXTENSIBLE', 'READONLY']).optional(),

  /**
   * 
  // which tools
  // @deprecated, use skillsets
   */
  tools: z.array(AINodeToolBOSchema).optional(),

  model: z.string().optional(),

  integrationId: z.string().optional(),

  // whether to turn this AI node into a team member?
  asMember: z.boolean().optional(),
});
export type AINodeAgentBO = z.infer<typeof AINodeAgentSchema>;

export const AINodeBOSchema = BaseNodeResourceBOSchema.merge(AINodeAgentSchema).extend({
  resourceType: z.literal(NodeResourceTypeSchema.enum.AI),
});

export type AiNodeBO = z.infer<typeof AINodeBOSchema>;
