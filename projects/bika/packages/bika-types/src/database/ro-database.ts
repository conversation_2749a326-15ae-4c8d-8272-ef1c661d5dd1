import { z } from 'zod';
import {
  DatabaseSingleTextFieldSchema,
  DatabaseLongTextFieldSchema,
  DatabaseAutoNumberFieldSchema,
  DatabaseCheckBoxFieldSchema,
  DatabaseCurrencyFieldSchema,
  DatabaseDateTimeFieldSchema,
  DatabaseNumberFieldSchema,
  DatabasePercentFieldSchema,
  DatabasePhoneFieldSchema,
  DatabaseEmailFieldSchema,
  DatabaseRatingFieldSchema,
  DatabaseSingleSelectFieldSchema,
  DatabaseMultiSelectFieldSchema,
  DatabaseURLFieldSchema,
  DatabaseCreatedTimeFieldSchema,
  DatabaseModifiedTimeFieldSchema,
  DatabaseLinkFieldSchema,
  DatabaseMemberFieldSchema,
  DatabaseFormulaFieldSchema,
  DatabaseAttachmentFieldSchema,
  DatabaseVideoFieldSchema,
  DatabaseVoiceFieldSchema,
  DatabasePhotoFieldSchema,
  DatabaseAPIFieldSchema,
  DatabaseAITextFieldSchema,
  DatabaseAIVoiceFieldSchema,
  DatabaseAIPhotoFieldSchema,
  DatabaseAIVideoFieldSchema,
  DatabaseCutVideoFieldSchema,
  DatabaseJSONFieldSchema,
  DatabaseCreatedByFieldSchema,
  DatabaseModifiedByFieldSchema,
  DatabaseCascaderFieldSchema,
  DatabaseOneWayLinkFieldSchema,
  DatabaseWorkDocFieldSchema,
  DatabaseButtonFieldSchema,
  DatabaseDateRangeFieldSchema,
  DatabaseFieldSchema,
  DatabaseLookupFieldSchema,
} from './bo-field';

const DatabaseLookupFieldROSchema = DatabaseLookupFieldSchema.extend({
  id: z.string(),
  render: z
    .object({
      // Information about the final lookup field and its databaseId
      //
      // When the target field is a Lookup field (Lookup -> Lookup -> ... -> Text),
      // this records the final Text field
      lookupFinalField: DatabaseFieldSchema.and(
        z.object({
          databaseId: z.string(),
        }),
      ),
    })
    // When the linked Link field is deleted (or is no longer a Link type),
    // lookupTargetField will be empty
    .optional(),
});
export type DatabaseLookupFieldRO = z.infer<typeof DatabaseLookupFieldROSchema>;

export const DatabaseFieldROSchema = z
  .discriminatedUnion('type', [
    DatabaseSingleTextFieldSchema,
    DatabaseLongTextFieldSchema,
    DatabaseAutoNumberFieldSchema,
    DatabaseCheckBoxFieldSchema,
    DatabaseCurrencyFieldSchema,
    DatabaseDateTimeFieldSchema,
    DatabaseNumberFieldSchema,
    DatabasePercentFieldSchema,
    DatabasePhoneFieldSchema,
    DatabaseEmailFieldSchema,
    DatabaseRatingFieldSchema,
    DatabaseSingleSelectFieldSchema,
    DatabaseMultiSelectFieldSchema,
    DatabaseURLFieldSchema,
    DatabaseCreatedTimeFieldSchema,
    DatabaseModifiedTimeFieldSchema,
    DatabaseLinkFieldSchema,
    DatabaseLookupFieldROSchema,
    DatabaseMemberFieldSchema,
    DatabaseFormulaFieldSchema,
    DatabaseAttachmentFieldSchema,
    DatabaseVideoFieldSchema,
    DatabaseVoiceFieldSchema,
    DatabasePhotoFieldSchema,
    DatabaseAPIFieldSchema,
    DatabaseAITextFieldSchema,
    DatabaseAIVoiceFieldSchema,
    DatabaseAIPhotoFieldSchema,
    DatabaseAIVideoFieldSchema,
    DatabaseCutVideoFieldSchema,
    DatabaseJSONFieldSchema,
    DatabaseCreatedByFieldSchema,
    DatabaseModifiedByFieldSchema,
    DatabaseCascaderFieldSchema,
    DatabaseOneWayLinkFieldSchema,
    DatabaseWorkDocFieldSchema,
    DatabaseButtonFieldSchema,
    DatabaseDateRangeFieldSchema,
  ])
  .and(
    z.object({
      id: z.string(),
    }),
  );
export type DatabaseFieldRO = z.infer<typeof DatabaseFieldROSchema>;
