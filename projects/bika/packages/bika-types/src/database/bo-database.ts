/**
 * Record {
 *  data: {
 *    fieldA: XXX,
 *    fieldB: YYY,
 *  }
 * }
 */
import { z } from 'zod';
import { DatabaseFieldSchema } from './bo-field';
import { RecordDataSchema } from './bo-record';
import { ViewSchema } from './bo-view';
import { BaseNodeResourceBOSchema, DatabaseNodeType } from '../node/base';

export const DatabaseRecordSchema = z.object({
  id: z.string().optional(),
  templateId: z.string().optional(),
  parentTemplateId: z.string().optional(),
  data: RecordDataSchema,
  values: RecordDataSchema.optional(),
});

// record BO
export type DatabaseRecord = z.infer<typeof DatabaseRecordSchema>;

export const DatabaseTypeSchema = z.enum([
  'TASK', // List, checkable, expandable (has expand button + check button + multi-select button + order adjustment button)
  'DATUM', // Data table, one row of data, expandable (only has expand button + multi-select button + order adjustment button)
  'SYNC', // Sync table
  'EXTERNAL', // External database, connection only, no data sync, like Google Sheet
  'SYSTEM', // System database, Team, Member
  // 'BASE_SYNC',
  // 'BASE_VIKA',
]);
export type DatabaseTypes = z.infer<typeof DatabaseTypeSchema>;

// export const DatabaseCallbackSchema = z.object({
//   event: z.string(),
//   action: ActionSchema,
// });
// export type DatabaseCallback = z.infer<typeof DatabaseCallbackSchema>;

export const DatabaseSchema = BaseNodeResourceBOSchema.extend({
  resourceType: DatabaseNodeType,
  databaseType: DatabaseTypeSchema.optional(),
  views: z.array(ViewSchema).optional(),
  fields: z.array(DatabaseFieldSchema).optional(),

  // Pre Define Records
  records: z.array(DatabaseRecordSchema).optional(),
});

export type Database = z.infer<typeof DatabaseSchema>;
