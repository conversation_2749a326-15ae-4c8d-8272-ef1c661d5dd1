import { type Database, DatabaseSchema } from './bo-database';
import {
  DatabaseEmailFieldSchema,
  URL,
  type DatabaseFieldType,
  DatabaseLongTextFieldSchema,
  DatabasePhoneFieldSchema,
  DatabaseSingleTextFieldSchema,
  Email,
  LongText,
  Percent,
  Phone,
  type DatabaseField,
  SingleText,
  DatabaseURLFieldSchema,
  NumberField,
  DatabaseNumberFieldSchema,
  DatabasePercentFieldSchema,
  Currency,
  DatabaseCurrencyFieldSchema,
  DatabaseRatingFieldSchema,
  AutoNumber,
  DatabaseAutoNumberFieldSchema,
  CheckBox,
  DatabaseCheckBoxFieldSchema,
  DateTime,
  Rating,
  DatabaseDateTimeFieldSchema,
  CreatedTime,
  DatabaseCreatedTimeFieldSchema,
  ModifiedTime,
  DatabaseModifiedTimeFieldSchema,
  DateRange,
  DatabaseDateRangeFieldSchema,
  SingleSelect,
  DatabaseSingleSelectFieldSchema,
  MultiSelect,
  DatabaseMultiSelectFieldSchema,
  Member,
  type DatabaseMemberField,
  CreatedBy,
  type DatabaseCreatedByField,
  ModifiedBy,
  type DatabaseModifiedByField,
  OneWayLink,
  type DatabaseOneWayLinkField,
  Link,
  type DatabaseLinkField,
  Lookup,
  type DatabaseLookupField,
  Formula,
  type DatabaseFormulaField,
  Attachment,
  DatabaseAttachmentFieldSchema,
  JSONField,
  DatabaseJSONFieldSchema,
  AIPhoto,
  AIText,
  AIVideo,
  AIVoice,
  API,
  Button,
  Cascader,
  CutVideo,
  DatabaseAIPhotoFieldSchema,
  DatabaseAITextFieldSchema,
  DatabaseAIVideoFieldSchema,
  DatabaseAIVoiceFieldSchema,
  DatabaseAPIFieldSchema,
  DatabaseButtonFieldSchema,
  DatabaseCascaderFieldSchema,
  DatabaseCutVideoFieldSchema,
  DatabaseVideoFieldSchema,
  DatabaseVoiceFieldSchema,
  DatabaseWorkDocFieldSchema,
  Video,
  Voice,
  WorkDoc,
  DatabasePhotoFieldSchema,
  Photo,
  BasicValueType,
} from './bo-field';
import { type View } from './bo-view';
import { DatabaseCreateBOSchema, DatabaseCreateDTO } from './dto-database';
import { RecordDetailVO } from './vo-record';
import { iString, iStringParse, type Locale } from '../i18n/bo';
import { getResourceBOByTemplateId } from '../utils';

export const FieldSortableConfig: Record<DatabaseFieldType, boolean> = {
  SINGLE_TEXT: true,
  LONG_TEXT: true,
  AUTO_NUMBER: true,
  CHECKBOX: true,
  CURRENCY: true,
  DATETIME: true,
  DATERANGE: true,
  NUMBER: true,
  PERCENT: true,
  PHONE: true,
  EMAIL: true,
  RATING: true,
  SINGLE_SELECT: true,
  MULTI_SELECT: true,
  URL: true,
  CREATED_TIME: true,
  MODIFIED_TIME: true,
  LINK: true,
  LOOKUP: true,
  MEMBER: true,
  FORMULA: true,
  ATTACHMENT: false,
  VIDEO: false,
  VOICE: false,
  PHOTO: false,
  API: false,
  AI_TEXT: false,
  AI_VOICE: false,
  AI_PHOTO: false,
  AI_VIDEO: false,
  CUT_VIDEO: false,
  JSON: false,
  CREATED_BY: true,
  MODIFIED_BY: true,
  CASCADER: true,
  ONE_WAY_LINK: true,
  WORK_DOC: false,
  BUTTON: false,
};

export const FieldBasicValueTypeConfig: Record<DatabaseFieldType, BasicValueType> = {
  SINGLE_TEXT: BasicValueType.String,
  LONG_TEXT: BasicValueType.String,
  AUTO_NUMBER: BasicValueType.Number,
  CHECKBOX: BasicValueType.Boolean,
  CURRENCY: BasicValueType.Number,
  DATETIME: BasicValueType.DateTime,
  DATERANGE: BasicValueType.DateTime,
  NUMBER: BasicValueType.Number,
  PERCENT: BasicValueType.Number,
  PHONE: BasicValueType.String,
  EMAIL: BasicValueType.String,
  RATING: BasicValueType.Number,
  SINGLE_SELECT: BasicValueType.Array,
  MULTI_SELECT: BasicValueType.Array,
  URL: BasicValueType.String,
  CREATED_TIME: BasicValueType.DateTime,
  MODIFIED_TIME: BasicValueType.DateTime,
  LINK: BasicValueType.String,
  LOOKUP: BasicValueType.Array,
  MEMBER: BasicValueType.Array,
  FORMULA: BasicValueType.String,
  ATTACHMENT: BasicValueType.Array,
  VIDEO: BasicValueType.Array,
  VOICE: BasicValueType.Array,
  PHOTO: BasicValueType.Array,
  API: BasicValueType.String,
  AI_TEXT: BasicValueType.String,
  AI_VOICE: BasicValueType.String,
  AI_PHOTO: BasicValueType.String,
  AI_VIDEO: BasicValueType.String,
  CUT_VIDEO: BasicValueType.String,
  JSON: BasicValueType.String,
  CREATED_BY: BasicValueType.String,
  MODIFIED_BY: BasicValueType.String,
  CASCADER: BasicValueType.String,
  ONE_WAY_LINK: BasicValueType.Array,
  WORK_DOC: BasicValueType.String,
  BUTTON: BasicValueType.String,
};

export function defaultDatabaseFieldBO(
  id: { id?: string; templateId?: string },
  create: {
    fieldType: DatabaseFieldType;
    name?: string;
    description?: string;
  },
  locale: Locale,
): DatabaseField | null {
  const t = create.fieldType;

  const field = {
    type: t,
    id: id.id,
    templateId: id.templateId,
    name: create.name,
    description: create.description,
  };

  switch (t) {
    // Text
    case SingleText.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New single text field',
            'zh-CN': '新建单行文本',
            'zh-TW': '新建單行文本',
            ja: '新しい単一行テキスト',
          },
          locale,
        );
      return DatabaseSingleTextFieldSchema.parse({ ...field, name });
    }
    case LongText.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New long text field',
            'zh-CN': '新建多行文本',
            'zh-TW': '新建多行文本',
            ja: '新しい複数行テキスト',
          },
          locale,
        );
      return DatabaseLongTextFieldSchema.parse({ ...field, name });
    }
    case Phone.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New phone field',
            'zh-CN': '新建电话号码',
            'zh-TW': '新建電話號碼',
            ja: '新しい電話番号',
          },
          locale,
        );
      return DatabasePhoneFieldSchema.parse({ ...field, name });
    }
    case Email.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New email field',
            'zh-CN': '新建电子邮件',
            'zh-TW': '新建電子郵件',
            ja: '新しいメールフィールド',
          },
          locale,
        );
      return DatabaseEmailFieldSchema.parse({ ...field, name });
    }
    case URL.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New URL field',
            'zh-CN': '新建网址',
            'zh-TW': '新建網址',
            ja: '新しいURLフィールド',
          },
          locale,
        );
      return DatabaseURLFieldSchema.parse({ ...field, name });
    }

    // Number
    case NumberField.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New number field',
            'zh-CN': '新建数字',
            'zh-TW': '新建數字',
            ja: '新しい数値フィールド',
          },
          locale,
        );
      return DatabaseNumberFieldSchema.parse({ ...field, name });
    }
    case Percent.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New percent field',
            'zh-CN': '新建百分比',
            'zh-TW': '新建百分比',
            ja: '新しいパーセントフィールド',
          },
          locale,
        );
      return DatabasePercentFieldSchema.parse({ ...field, name });
    }
    case Currency.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New currency field',
            'zh-CN': '新建货币',
            'zh-TW': '新建貨幣',
            ja: '新しい通貨フィールド',
          },
          locale,
        );
      return DatabaseCurrencyFieldSchema.parse({ ...field, name });
    }
    case Rating.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New rating field',
            'zh-CN': '新建评分',
            'zh-TW': '新建評分',
            ja: '新しい評価フィールド',
          },
          locale,
        );
      return DatabaseRatingFieldSchema.parse({ ...field, name });
    }
    case AutoNumber.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New auto number field',
            'zh-CN': '新建自动编号',
            'zh-TW': '新建自動編號',
            ja: '新しい自動番号フィールド',
          },
          locale,
        );
      return DatabaseAutoNumberFieldSchema.parse({ ...field, name });
    }

    // Checkbox
    case CheckBox.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New checkbox field',
            'zh-CN': '新建复选框',
            'zh-TW': '新建複選框',
            ja: '新しいチェックボックスフィールド',
          },
          locale,
        );
      return DatabaseCheckBoxFieldSchema.parse({ ...field, name });
    }

    // Datetime
    case DateTime.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New datetime field',
            'zh-CN': '新建日期时间',
            'zh-TW': '新建日期時間',
            ja: '新しい日時フィールド',
          },
          locale,
        );
      return DatabaseDateTimeFieldSchema.parse({ ...field, name });
    }
    case CreatedTime.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New created time field',
            'zh-CN': '新建创建时间',
            'zh-TW': '新建建立時間',
            ja: '新しい作成時間フィールド',
          },
          locale,
        );
      return DatabaseCreatedTimeFieldSchema.parse({ ...field, name });
    }
    case ModifiedTime.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New modified time field',
            'zh-CN': '新建修改时间',
            'zh-TW': '新建修改時間',
            ja: '新しい変更時間フィールド',
          },
          locale,
        );
      return DatabaseModifiedTimeFieldSchema.parse({ ...field, name });
    }
    case DateRange.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New date range field',
            'zh-CN': '新建日期范围',
            'zh-TW': '新建日期範圍',
            ja: '新しい日付範囲フィールド',
          },
          locale,
        );
      return DatabaseDateRangeFieldSchema.parse({ ...field, name });
    }

    // Select
    case SingleSelect.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New single select field',
            'zh-CN': '新建单选',
            'zh-TW': '新建單選',
            ja: '新しい単一選択フィールド',
          },
          locale,
        );
      return DatabaseSingleSelectFieldSchema.parse({ ...field, name });
    }
    case MultiSelect.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New multi select field',
            'zh-CN': '新建多选',
            'zh-TW': '新建多選',
            ja: '新しい複数選択フィールド',
          },
          locale,
        );
      return DatabaseMultiSelectFieldSchema.parse({ ...field, name });
    }
    case Cascader.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New cascader field',
            'zh-CN': '新建级联选择',
            'zh-TW': '新建級聯選擇',
            ja: '新しいカスケードフィールド',
          },
          locale,
        );
      return DatabaseCascaderFieldSchema.parse({ ...field, name });
    }

    // Special
    case Member.value: {
      const name =
        create.name ||
        iStringParse(
          { en: 'New member field', 'zh-CN': '新建成员', 'zh-TW': '新建成員', ja: '新しいメンバーフィールド' },
          locale,
        );
      return {
        ...field,
        type: 'MEMBER',
        name,
        property: {},
      } as DatabaseMemberField;
    }

    case CreatedBy.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New created by field',
            'zh-CN': '新建创建人',
            'zh-TW': '新建建立人',
            ja: '新しい作成者フィールド',
          },
          locale,
        );
      return {
        ...field,
        type: 'CREATED_BY',
        name,
      } as DatabaseCreatedByField;
    }

    case ModifiedBy.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New modified by field',
            'zh-CN': '新建修改人',
            'zh-TW': '新建修改人',
            ja: '新しい変更者フィールド',
          },
          locale,
        );
      return {
        ...field,
        type: 'MODIFIED_BY',
        name,
      } as DatabaseModifiedByField;
    }

    // Link
    case OneWayLink.value: {
      const name: string =
        create.name ||
        iStringParse(
          {
            en: 'New one-way link field',
            'zh-CN': '新建单向关联',
            'zh-TW': '新建單向關聯',
            ja: '新しい片道リンクフィールド',
          },
          locale,
        );
      const oneWayLinkField: DatabaseOneWayLinkField = {
        ...field,
        type: 'ONE_WAY_LINK',
        name,
        property: {
          foreignDatabaseId: '',
        },
      };
      return oneWayLinkField;
    }
    case Link.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New link field',
            'zh-CN': '新建双向关联',
            'zh-TW': '新建雙向關聯',
            ja: '新しいリンクフィールド',
          },
          locale,
        );
      const linkField: DatabaseLinkField = {
        ...field,
        type: 'LINK',
        name,
        property: {
          foreignDatabaseId: '',
          brotherFieldId: '',
        },
      };
      return linkField;
    }

    // Lookup
    case Lookup.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New lookup field',
            'zh-CN': '新建查找引用',
            'zh-TW': '新建查找引用',
            ja: '新しい参照フィールド',
          },
          locale,
        );

      const lookupField: DatabaseLookupField = {
        ...field,
        type: 'LOOKUP',
        name,
        property: {
          databaseId: '',
          lookupTargetFieldId: '',
          relatedLinkFieldId: '',
        },
      };
      return lookupField;
    }

    // Formula
    case Formula.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New formula field',
            'zh-CN': '新建公式',
            'zh-TW': '新建公式',
            ja: '新しい数式フィールド',
          },
          locale,
        );

      const formulaField: DatabaseFormulaField = {
        ...field,
        type: 'FORMULA',
        name,
        property: {
          expression: '',
        },
      };
      return formulaField;
    }

    // File Type
    case Attachment.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New attachment field',
            'zh-CN': '新建附件',
            'zh-TW': '新建附件',
            ja: '新しい添付ファイルフィールド',
          },
          locale,
        );
      return DatabaseAttachmentFieldSchema.parse({ ...field, name });
    }
    case Photo.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New photo field',
            'zh-CN': '新建图片',
            'zh-TW': '新建圖片',
            ja: '新しい写真フィールド',
          },
          locale,
        );
      return DatabasePhotoFieldSchema.parse({ ...field, name });
    }
    case Video.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New video field',
            'zh-CN': '新建视频',
            'zh-TW': '新建視頻',
            ja: '新しいビデオフィールド',
          },
          locale,
        );
      return DatabaseVideoFieldSchema.parse({ ...field, name });
    }
    case Voice.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New voice field',
            'zh-CN': '新建语音',
            'zh-TW': '新建語音',
            ja: '新しい音声フィールド',
          },
          locale,
        );
      return DatabaseVoiceFieldSchema.parse({ ...field, name });
    }
    case CutVideo.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New cut video field',
            'zh-CN': '新建剪辑视频',
            'zh-TW': '新建剪輯視頻',
            ja: '新しいカットビデオフィールド',
          },
          locale,
        );
      return DatabaseCutVideoFieldSchema.parse({ ...field, name });
    }

    // AI
    case AIText.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New AI text field',
            'zh-CN': '新建AI文本',
            'zh-TW': '新建AI文本',
            ja: '新しいAIテキストフィールド',
          },
          locale,
        );
      return DatabaseAITextFieldSchema.parse({
        ...field,
        name,
        type: 'AI_TEXT',
        property: {
          type: 'INTEGRATION',
          integrationId: '',
        },
      });
    }
    case AIPhoto.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New AI photo field',
            'zh-CN': '新建AI图片',
            'zh-TW': '新建AI圖片',
            ja: '新しいAI写真フィールド',
          },
          locale,
        );
      return DatabaseAIPhotoFieldSchema.parse({ ...field, name });
    }
    case AIVideo.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New AI video field',
            'zh-CN': '新建AI视频',
            'zh-TW': '新建AI視頻',
            ja: '新しいAIビデオフィールド',
          },
          locale,
        );
      return DatabaseAIVideoFieldSchema.parse({ ...field, name });
    }
    case AIVoice.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New AI voice field',
            'zh-CN': '新建AI语音',
            'zh-TW': '新建AI語音',
            ja: '新しいAI音声フィールド',
          },
          locale,
        );
      return DatabaseAIVoiceFieldSchema.parse({ ...field, name });
    }

    // Other
    case JSONField.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New JSON field',
            'zh-CN': '新建JSON',
            'zh-TW': '新建JSON',
            ja: '新しいJSONフィールド',
          },
          locale,
        );
      return DatabaseJSONFieldSchema.parse({ ...field, name });
    }
    case API.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New API field',
            'zh-CN': '新建API',
            'zh-TW': '新建API',
            ja: '新しいAPIフィールド',
          },
          locale,
        );
      return DatabaseAPIFieldSchema.parse({ ...field, name });
    }
    case WorkDoc.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New work doc field',
            'zh-CN': '新建工作文档',
            'zh-TW': '新建工作文檔',
            ja: '新しい作業ドキュメントフィールド',
          },
          locale,
        );
      return DatabaseWorkDocFieldSchema.parse({ ...field, name });
    }
    case Button.value: {
      const name =
        create.name ||
        iStringParse(
          {
            en: 'New button field',
            'zh-CN': '新建按钮',
            'zh-TW': '新建按鈕',
            ja: '新しいボタンフィールド',
          },
          locale,
        );
      return DatabaseButtonFieldSchema.parse({ ...field, name });
    }
    default:
      console.error(`unsupported field type default value: ${t}`);
      return null;
  }
}

export function defaultDatabaseCreateBO(locale: Locale): DatabaseCreateDTO {
  return DatabaseCreateBOSchema.parse({
    resourceType: 'DATABASE',
    name: iStringParse(
      { en: 'New database', 'zh-CN': '新数据表', 'zh-TW': '新數據表', ja: '新しいデータベース' },
      locale,
    ),
    description: undefined,
  });
}

export function defaultDatabaseFieldCreateBO(locale?: Locale): DatabaseField[] | undefined {
  const database = getResourceBOByTemplateId<Database>('database');
  const fields = database.fields;
  return fields
    ?.filter((f) => f.type === SingleText.value || f.type === MultiSelect.value || f.type === Attachment.value)
    .map((field) => ({ ...field, id: undefined, templateId: undefined, name: iStringParse(field.name, locale) }));
}

/**
 * 创建默认值的BO
 *
 * @param init
 * @returns
 */
export const defaultDatabaseBO = (
  id: {
    id?: string;
    templateId?: string;
  },
  create: DatabaseCreateDTO,
) =>
  DatabaseSchema.parse({
    resourceType: 'DATABASE',
    databaseType: 'DATUM',
    id: id?.id,
    templateId: id?.templateId,
    name: create.name,
    description: create.description,
    views: [],
    fields: [],
    records: [],
  });

export const defaultDatabaseViewBO = (view: Pick<View, 'type'> & Partial<View>, locale: Locale): View | null => {
  const name: iString | null = ((): iString | null => {
    switch (view.type) {
      case 'TABLE':
        return iStringParse(
          {
            en: 'New table view',
            'zh-CN': '新表格视图',
            'zh-TW': '新表格視圖',
            ja: '新しいテーブルビュー',
          },
          locale,
        );
      case 'KANBAN':
        return iStringParse(
          {
            en: 'New kanban view',
            'zh-CN': '新看板视图',
            'zh-TW': '新看板視圖',
            ja: '新しいカンバンビュー',
          },
          locale,
        );
      case 'GANTT':
        return iStringParse(
          {
            en: 'New gantt view',
            'zh-CN': '新甘特图视图',
            'zh-TW': '新甘特圖視圖',
            ja: '新しいガントビュー',
          },
          locale,
        );
      case 'FORM':
        return iStringParse(
          {
            en: 'New form view',
            'zh-CN': '新表单视图',
            'zh-TW': '新表單視圖',
            ja: '新しいフォームビュー',
          },
          locale,
        );
      default:
        return null;
    }
  })();

  if (name === null) {
    return null;
  }

  return {
    name,
    ...view,
  };
};

export function defaultRecordDetailVO(): RecordDetailVO {
  return {
    revision: 1,
    record: {
      id: 'story',
      databaseId: 'story',
      revision: 1,
      cells: {
        field1: {
          id: 'field1',
          name: 'Field 1',
          data: 'Field 1 Data',
          value: 'value1',
        },
        field2: {
          id: 'field2',
          name: 'Field 2',
          data: 'Field 2 Data',
          value: 'value2',
        },
        field3: {
          id: 'field3',
          name: 'Field 3',
          data: 3,
          value: '31',
        },
        field4: {
          id: 'field4',
          name: 'single_select',
          data: 'option1',
          value: 'option1',
        },
        field5: {
          id: 'field4',
          name: 'created by ',
          data: 'option1',
          value: {
            id: 'user1',
            name: 'A user1',
          },
        },
        field6: {
          id: 'field4',
          name: 'modified by ',
          data: 'option1',
          value: {
            id: 'user1',
            name: 'A user1',
          },
        },
        field7: {
          id: 'field7',
          name: 'Field 7 Long Text',
          data: 'Long TextLong Text Field ery long text field Very long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldLong Text Field ery long text field Very long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text field',
          value: 'Very Long Text',
        },
        field8: {
          id: 'field8',
          name: 'Field 8',
          data: true,
          value: 'true',
        },
        field9: {
          id: 'field9',
          name: 'Field 9',
          data: new Date().toISOString(),
          value: new Date().toISOString(),
        },
        field10: {
          id: 'field10',
          name: 'Field 10',
          data: '11',
          value: '11',
        },
        field11: {
          id: 'field11',
          name: 'Field 11',
          data: 11,
          value: '11',
        },
        field12: {
          id: 'field12',
          name: 'Field 12',
          data: ['option1'],
          value: '1,2',
        },
        field13: {
          id: 'field13',
          name: 'Field 13',
        },
        field14: {
          id: 'field14',
          name: 'Field 14',
          data: 1,
          value: '10%',
        },
        field15: {
          id: 'field15',
          name: 'Field 15',
          data: '<EMAIL>',
          value: '<EMAIL>',
        },
        field16: {
          id: 'field16',
          name: 'Field 15',
          data: 'https://bika.ai',
          value: 'https://bika.ai',
        },
        field17: {
          id: 'field18',
          name: 'Field 17',
          data: '17688',
          value: '176889',
        },
        field19: {
          id: 'field19',
          name: 'Field 17',
          data: {
            docId: '1',
            name: '我的文档我的文档我的文档我的文档我的文档我的文档我的文档',
          },
          value: {
            // @ts-ignore valueGetter for custom renderer
            docId: '1',
            name: '我的文档我的文档我的文档我的文档我的文档我的文档我的文档我的文档我的文档我的文档',
          },
        },
      },
    },
    fields: [
      {
        id: 'field1',
        name: 'Long Long Long Long Long Long Long Long Long Long Long Long Long Field 1',
        description: 'Field 1 Description',
        type: 'SINGLE_TEXT',
        required: true,
        primary: true,
        databaseId: 'story',
        // order: 1,
      },

      {
        id: 'field2',
        name: 'Field 2',
        description: 'Field 2 Description',
        type: 'SINGLE_TEXT',
        required: false,
        primary: false,
        databaseId: 'story',
        // order: 2,
      },
      {
        id: 'field3',
        name: 'Field 3',
        description: 'Field 3 Description',
        type: 'NUMBER',
        required: false,
        primary: false,
        databaseId: 'story',
        property: {
          precision: 2,
          commaStyle: 'thousand',
          symbol: '',
          symbolAlign: 'default',
        },
      },
      {
        id: 'field4',
        name: 'single_select',
        type: 'SINGLE_SELECT',
        databaseId: 'story',
        primary: false,
        property: {
          options: [
            {
              templateId: '2',
              name: 'option2',
              color: 'blue',
            },
            {
              templateId: '1',
              name: 'option1',
              color: 'red',
            },
          ],
        },
      },
      {
        id: 'field5',
        type: 'CREATED_BY',
        name: 'Creator',
        description: 'Creatory field',
        databaseId: 'story',
        primary: false,
      },
      {
        id: 'field6',
        type: 'MODIFIED_BY',
        name: 'Modifier',
        description: 'Modifier field',
        databaseId: 'story',
        primary: false,
      },
      {
        id: 'field7',
        type: 'LONG_TEXT',
        name: 'Long Text',
        description:
          'ery long text field Very long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text fieldVery long text field',
        databaseId: 'story',
        primary: false,
      },
      {
        id: 'field8',
        type: 'CHECKBOX',
        name: 'Checkbox Field',
        description: 'checkbox field',
        databaseId: 'story',
        primary: false,
      },
      {
        id: 'field9',
        type: 'DATETIME',
        name: 'Datetime Field',
        description: 'datetime field',
        databaseId: 'story',
        property: {
          timeZone: 'AUTO',
          includeTime: false,
          dateFormat: 'YYYY-MM-DD',
        },
        primary: false,
      },
      {
        id: 'field10',
        type: 'AUTO_NUMBER',
        name: 'Autonumber Field',
        description: 'auto number field',
        databaseId: 'story',
        property: {
          nextId: 1,
        },
        primary: false,
      },
      {
        id: 'field11',
        type: 'CURRENCY',
        name: 'CURRENCY Field',
        description: 'auto number field',
        databaseId: 'story',
        property: {
          precision: 2,
          commaStyle: ',',
          symbol: '$',
          symbolAlign: 'left',
        },
        primary: false,
      },
      {
        id: 'field12',
        type: 'MULTI_SELECT',
        name: 'MULTI_SELECT Field',
        description: 'auto number field',
        databaseId: 'story',
        property: {
          options: [
            {
              templateId: '2',
              id: 'option2',
              name: 'option2',
              color: 'blue',
            },
            {
              templateId: '1',
              id: 'option1',
              name: 'option1',
              color: 'red',
            },
          ],
        },
        primary: false,
      },
      {
        id: 'field13',
        type: 'DATERANGE',
        name: 'DATERANGE Field',
        description: 'auto number field',
        databaseId: 'story',
        property: {
          timeZone: 'Asia/Tokyo',
          dateFormat: 'DD/MM/YYYY',
          timeFormat: 'hh:mm',
          includeTime: true,
        },
        primary: false,
      },

      {
        id: 'field14',
        type: 'PERCENT',
        name: 'PERCENT Field',
        description: 'percent',
        databaseId: 'story',
        property: {
          precision: 2,
        },
        primary: false,
      },
      {
        id: 'field15',
        type: 'EMAIL',
        name: 'EMAIL Field',
        description: 'email',
        databaseId: 'story',
        property: {},
        primary: false,
      },
      {
        id: 'field16',
        type: 'URL',
        name: 'URL Field',
        description: 'url',
        databaseId: 'story',
        property: {},
        primary: false,
      },
      {
        primary: false,
        id: 'field17',
        type: 'RATING',
        name: 'Rating Field',
        description: 'ratting',
        databaseId: 'story',
        property: {
          icon: {
            type: 'EMOJI',
            emoji: '⭐️',
          },
          max: 5,
        },
      },
      {
        id: 'field19',
        type: 'WORK_DOC',
        name: 'WORK_DOC Field',
        description: 'doc',
        databaseId: 'story',
        property: {},
        primary: false,
      },
      {
        id: 'field18',
        type: 'PHONE',
        name: 'PHONE Field',
        description: 'phone',
        databaseId: 'story',
        property: {},
        primary: false,
      },
    ],
  };
}

export const DefaultCommaSeparator = ',';
