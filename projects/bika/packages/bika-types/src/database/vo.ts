import { NodeResourceType } from '../node/bo';

export * from './vo-database';
export * from './vo-field';
export * from './vo-record';
export * from './vo-view';

export const CONST_PREFIX_AI = 'ichat';
export const CONST_PREFIX_WIZ = 'cha';
export const CONST_PREFIX_AI_MESSAGE = 'msg';
export const CONST_PREFIX_DSB = 'dsb';
export const CONST_PREFIX_DAT = 'dat';
export const CONST_PREFIX_FOLD = 'fold';
export const CONST_PREFIX_NODTPL = 'nodtpl';
export const CONST_PREFIX_AUT = 'ato';
export const CONST_PREFIX_VIEW = 'viw';
export const CONST_PREFIX_ROOT = 'rot';
export const CONST_PREFIX_FORM = 'fom';
export const CONST_PREFIX_MIRROR = 'mir';
export const CONST_PREFIX_DOC = 'doc';
export const CONST_PREFIX_FILE = 'fil';
export const CONST_PREFIX_AI_NODE = 'ain';
export const CONST_PREFIX_AI_PAGE = 'aip';
export const CONST_PREFIX_ACTION = 'act';
export const CONST_PREFIX_TRIGGER = 'trg';
export const CONST_PREFIX_FIELD = 'fld';
export const CONST_PREFIX_RECORD = 'rec';
export const CONST_PREFIX_TEMPLATE = 'tpl';
export const CONST_PREFIX_WIDGET = 'wdt';
export const CONST_PREFIX_ATTACHMENT = 'att';
export const CONST_PREFIX_OPTION = 'opt';
// export const CONST_PREFIX_FIELD_LOOKUP = 'fldlk';
export const CONST_PREFIX_SPACE = 'spc';

export const CONST_RESOURCE_IDS = [
  CONST_PREFIX_DSB,
  CONST_PREFIX_DAT,
  CONST_PREFIX_FOLD,
  CONST_PREFIX_AUT,
  CONST_PREFIX_FORM,
  CONST_PREFIX_MIRROR,
  CONST_PREFIX_NODTPL,
  CONST_PREFIX_DOC,
];

export const PREFIX_MAP: Record<
  NodeResourceType | 'TRIGGER' | 'ACTION' | 'WIDGET' | 'FIELD' | 'VIEW' | 'RECORD',
  string
> = {
  FOLDER: CONST_PREFIX_FOLD,
  DATABASE: CONST_PREFIX_DAT,
  AUTOMATION: CONST_PREFIX_AUT,
  DASHBOARD: CONST_PREFIX_DSB,
  FORM: CONST_PREFIX_FORM,
  MIRROR: CONST_PREFIX_MIRROR,
  AI: CONST_PREFIX_AI_NODE,
  ROOT: CONST_PREFIX_ROOT,
  TEMPLATE: CONST_PREFIX_TEMPLATE,
  PAGE: CONST_PREFIX_AI_PAGE,
  DOCUMENT: CONST_PREFIX_DOC,
  FILE: CONST_PREFIX_FILE,
  EMBED: '',
  VIEW: CONST_PREFIX_VIEW,
  DATAPAGE: '',
  CANVAS: '',
  ALIAS: '',
  REPORT_TEMPLATE: '',
  TRIGGER: CONST_PREFIX_TRIGGER,
  ACTION: CONST_PREFIX_ACTION,
  WIDGET: CONST_PREFIX_WIDGET,
  FIELD: CONST_PREFIX_FIELD,
  RECORD: CONST_PREFIX_RECORD,
};
