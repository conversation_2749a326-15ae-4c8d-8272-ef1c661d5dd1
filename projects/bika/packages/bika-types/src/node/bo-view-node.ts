import { z } from 'zod';
import { DatabaseViewTypeSchema } from '../database/bo-view';
import { iStringSchema } from '../system';

export const ViewNodeCreateOperationBOSchema = z.object({
  databaseId: z.string(),
  type: DatabaseViewTypeSchema,
  name: iStringSchema,
  parentId: z.string().optional(),
  preNodeId: z.string().optional(),
  nextNodeId: z.string().optional(),
  description: iStringSchema.optional(),
  templateId: z.string().optional(),
});

export type ViewNodeCreateOperationBO = z.infer<typeof ViewNodeCreateOperationBOSchema>;

// export const ViewNodeSchema = ViewSchema.merge(
//   BaseNodeResourceBOSchema.pick({ resourceType: true, permissions: true }),
// ).extend({
//   resourceType: ViewNodeType,
// });
// export type ViewNode = z.infer<typeof ViewNodeSchema>;
