import { z } from 'zod';
import { NodeMoveBOSchema, NodeUpdateBOSchema } from './base';
import { MirrorTypeSchema } from './bo-mirror';
import { AINodeUpdateDTOSchema, AIPageNodeUpdateDTOSchema } from '../ai/dto';
import { AutomationUpdateBOSchema } from '../automation/dto-automation';
import { DashboardUpdateBOSchema } from '../dashboard/dto-dashboard';
import { ViewSchema } from '../database/bo-view';
import { DatabaseUpdateBOSchema } from '../database/dto-database';
import { DocumentUpdateDTOSchema, FileUpdateDTOSchema } from '../document/dto';
import { FormMetadataSchema } from '../form/bo-form';
import { iStringSchema } from '../i18n/bo';
import { AvatarLogoSchema } from '../system/avatar';

export const FolderUpdateBOSchema = NodeUpdateBOSchema.extend({
  resourceType: z.literal('FOLDER'),
  cover: AvatarLogoSchema.optional(),
  readme: iStringSchema.optional(),
});

export type FolderUpdateBO = z.infer<typeof FolderUpdateBOSchema>;

export const FormUpdateBOSchema = NodeUpdateBOSchema.extend({
  resourceType: z.literal('FORM'),
  name: iStringSchema.optional(),
  cover: AvatarLogoSchema.optional(),
  description: iStringSchema.optional(),
  metadata: FormMetadataSchema.optional(),
  databaseId: z.string().optional(),
  brandLogo: AvatarLogoSchema.optional(),
  viewId: z.string().optional(),
});

export type FormUpdateBO = z.infer<typeof FormUpdateBOSchema>;

// export const ViewNodeUpdateBOSchema = NodeUpdateBOSchema.extend({
//   resourceType: z.literal('VIEW'),
//   name: iStringSchema.optional(),
// });

// export type ViewNodeUpdateBO = z.infer<typeof ViewNodeUpdateBOSchema>;

export const MirrorUpdateBOSchema = NodeUpdateBOSchema.extend({
  resourceType: z.literal('MIRROR'),
  name: iStringSchema.optional(),
  description: iStringSchema.optional(),
  databaseId: z.string().optional(),
  viewId: z.string().optional(),
  resourceId: z.string().optional(),
  view: ViewSchema.optional(),
  mirrorType: MirrorTypeSchema.optional(),
});

export type MirrorUpdateBO = z.infer<typeof MirrorUpdateBOSchema>;

// Not directly for API, wrapped by DTO, so it's BO
export const UpdateResourceDTOSchema = z.discriminatedUnion('resourceType', [
  FolderUpdateBOSchema, // Both template folder and folder use this
  AutomationUpdateBOSchema,
  FormUpdateBOSchema,
  // ViewNodeUpdateBOSchema,
  DashboardUpdateBOSchema,
  DatabaseUpdateBOSchema,
  DocumentUpdateDTOSchema,
  FileUpdateDTOSchema,
  MirrorUpdateBOSchema,
  AINodeUpdateDTOSchema,
  AIPageNodeUpdateDTOSchema,
]);

/**
 * Used for Update Resource BO, usually close to Resource BO
 */
export type UpdateResourceDTO = z.infer<typeof UpdateResourceDTOSchema>;
export type UpdateResourceDTOResourceType = UpdateResourceDTO['resourceType'];

export const NodeUpdateDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string().describe('Node resource ID'),
  data: UpdateResourceDTOSchema.describe('Update parameters'),
});

export type NodeUpdateDTOReq = z.infer<typeof NodeUpdateDTOSchema>;
export type NodeUpdateDTOReqWithoutSpaceId = Omit<NodeUpdateDTOReq, 'spaceId'>;

export const NodeMoveDTOSchema = z.object({
  spaceId: z.string(),
  id: z.string(),
  data: NodeMoveBOSchema,
});
export type NodeMoveDTOReq = z.infer<typeof NodeMoveDTOSchema>;
export type NodeMoveDTOReqWithoutSpaceId = Omit<NodeMoveDTOReq, 'spaceId'>;
