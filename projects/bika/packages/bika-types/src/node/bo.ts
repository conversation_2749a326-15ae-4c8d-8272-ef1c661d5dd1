import { z } from 'zod';
import { BaseNodeResourceBOSchema, FolderNodeType } from './base';
import { CodePageBOSchema } from './bo-code-page';
import { MirrorBOSchema } from './bo-mirror';
import { AINodeBOSchema } from '../ai/bo-ai-node';
import { AiPageNodeBOSchema } from '../ai/bo-ai-page';
import { AutomationSchema } from '../automation/bo-automation';
// import { CampaignSchema } from '../campaign/campaigns';
import { DashboardSchema } from '../dashboard/bo-dashboard';
import { DatabaseSchema } from '../database/bo-database';
import { DocumentBOSchema, FileNodeBOSchema } from '../document/bo';
import { FormBOSchema } from '../form/bo-form';
import { iStringSchema } from '../i18n/bo';
import { ReportTemplateSchema } from '../report/bo-reports';
import { AvatarLogoSchema } from '../system/avatar';

export * from './base';
export * from './bo-nodes';
export * from './bo-view-node';
export * from './bo-web-page';
export * from './bo-mirror';
export * from './bo-node-icon';

// Recursive
const FolderBaseSchema = BaseNodeResourceBOSchema.extend({
  resourceType: FolderNodeType,
  name: iStringSchema,
  cover: AvatarLogoSchema.optional(),
  readme: iStringSchema.optional(),

  // Whether to display how many resources are below? Count all? Or only count direct child nodes?
  showNumber: z.enum(['TOTAL', 'SIBLING']).optional(),
});

// non-recursive resources
export const NonRecursiveResourceSchema = z.union([
  DocumentBOSchema,
  FileNodeBOSchema,
  // ViewNodeSchema,
  MirrorBOSchema, // union cause can not use z.discriminatedUnion
  FormBOSchema, // union cause can not use z.discriminatedUnion
  ReportTemplateSchema,
  AutomationSchema,
  DatabaseSchema,
  CodePageBOSchema,
  DashboardSchema,
  AINodeBOSchema,
  AiPageNodeBOSchema,
]);

export type Folder = z.infer<typeof FolderBaseSchema> & {
  children?: NodeResource[];
};
export type FolderZodInput = z.infer<typeof FolderBaseSchema> & {
  children?: NodeResourceZodInput[];
};
export type FolderZodOutput = z.infer<typeof FolderBaseSchema> & {
  children?: NodeResourceZodOutput[];
};

export const FolderSchema: z.ZodType<FolderZodOutput, z.ZodTypeDef, FolderZodInput> = FolderBaseSchema.extend({
  children: z.lazy(() => NodeResourceSchema.array().optional()),
});

export const NodeResourceSchema = z.union([FolderSchema, NonRecursiveResourceSchema]);

export type NodeResource = z.infer<typeof NodeResourceSchema>;
export type NodeResourceZodInput = z.input<typeof NodeResourceSchema>;
export type NodeResourceZodOutput = z.output<typeof NodeResourceSchema>;
