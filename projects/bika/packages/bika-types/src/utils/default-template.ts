import { CustomTemplate } from '../template/bo-custom-template';

export const defaultTemplate: CustomTemplate = {
  templateId: 'default-template',
  schemaVersion: 'v1',
  visibility: 'PUBLIC',
  category: 'official',
  cover: {
    type: 'COLOR',
    color: 'BLUE',
  },
  name: { en: 'New Folder', 'zh-CN': '新的未命名文件夹', 'zh-TW': '新的未命名文件夾', ja: '新しいフォルダ' },
  version: '1.0.0',
  resources: [
    {
      resourceType: 'DOCUMENT',
      templateId: 'document',
      name: 'New Document',
      markdown: `# Hello World!
Markdown text here
`,
    },
    {
      resourceType: 'DATABASE',
      databaseType: 'DATUM',
      name: {
        en: 'New database',
        'zh-CN': '新数据表',
        'zh-TW': '新數據表',
        ja: '新しいデータベース',
      },
      templateId: 'database',
      views: [
        {
          templateId: 'table_view',
          type: 'TABLE',
          name: {
            en: 'new table view',
            'zh-CN': '新表格视图',
            'zh-TW': '新表格視圖',
            ja: '新しいテーブルビュー',
          },
        },
        {
          templateId: 'kanban_view',
          type: 'KANBAN',
          name: {
            en: 'new kanban view',
            'zh-CN': '新看板视图',
            'zh-TW': '新看板視圖',
            ja: '新しいかんばんビュー',
          },
        },
        {
          templateId: 'gallery_view',
          type: 'GALLERY',
          name: {
            en: 'new gallery view',
            'zh-CN': '新画廊视图',
            'zh-TW': '新畫廊視圖',
            ja: '新しいギャラリービュー',
          },
        },
      ],
      fields: [
        {
          templateId: 'single_text',
          type: 'SINGLE_TEXT',
          name: {
            en: 'Single Line Text',
            'zh-CN': '单行文本',
            'zh-TW': '單行文本',
            ja: 'シングルテキスト',
          },
        },
        {
          templateId: 'long_text',
          type: 'LONG_TEXT',
          name: {
            en: 'Multi-line Text',
            'zh-CN': '多行文本',
            'zh-TW': '單行文本',
            ja: 'ロングテキスト',
          },
        },
        {
          templateId: 'auto_number',
          type: 'AUTO_NUMBER',
          name: {
            en: 'Auto Number',
            'zh-CN': '自动编号',
            'zh-TW': '自動編號',
            ja: '自動番号付け',
          },
          property: {
            nextId: 1,
          },
        },
        {
          templateId: 'checkbox',
          type: 'CHECKBOX',
          name: {
            en: 'Checkbox',
            'zh-CN': '复选框',
            'zh-TW': '複選框',
            ja: 'チェックボックス',
          },
        },
        {
          templateId: 'currency',
          type: 'CURRENCY',
          name: {
            en: 'Currency',
            'zh-CN': '货币',
            'zh-TW': '貨幣',
            ja: '通貨の種類',
          },
          property: {
            precision: 2,
            commaStyle: 'thousand',
            symbol: '$',
            symbolAlign: 'left',
          },
        },
        {
          templateId: 'datetime',
          type: 'DATETIME',
          name: {
            en: 'Date and Time',
            'zh-CN': '日期和时间',
            'zh-TW': '日期和時間',
            ja: '日時',
          },
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
        },
        {
          templateId: 'daterange',
          type: 'DATERANGE',
          name: {
            en: 'Date Range',
            'zh-CN': '日期范围',
            'zh-TW': '日期範圍',
            ja: '日付範囲',
          },
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
        },
        {
          templateId: 'number',
          type: 'NUMBER',
          name: {
            en: 'Number',
            'zh-CN': '数字',
            'zh-TW': '數字',
            ja: '数字',
          },
          property: {
            precision: 0,
            commaStyle: 'thousand',
            symbol: '',
            symbolAlign: 'default',
          },
        },
        {
          templateId: 'percent',
          type: 'PERCENT',
          name: {
            en: 'Percent',
            'zh-CN': '百分比',
            'zh-TW': '百分比',
            ja: 'パーセント',
          },
          property: {
            precision: 2,
            commaStyle: 'thousand',
            symbol: '%',
            symbolAlign: 'right',
          },
        },
        {
          templateId: 'phone',
          type: 'PHONE',
          name: {
            en: 'Phone',
            'zh-CN': '电话',
            'zh-TW': '電話',
            ja: '電話番号',
          },
        },
        {
          templateId: 'email',
          type: 'EMAIL',
          name: {
            en: 'Email',
            'zh-CN': '电子邮件',
            'zh-TW': '電子郵件',
            ja: 'メールアドレス',
          },
        },
        {
          templateId: 'rating',
          type: 'RATING',
          name: {
            en: 'Rating',
            'zh-CN': '评级',
            'zh-TW': '評級',
            ja: '評価',
          },
          property: {
            icon: {
              type: 'EMOJI',
              emoji: '⭐️',
            },
            max: 5,
          },
        },
        {
          templateId: 'single_select',
          type: 'SINGLE_SELECT',
          name: {
            en: 'Single Select',
            'zh-CN': '单选',
            'zh-TW': '單選',
            ja: '単一選択',
          },
          property: {
            options: [
              {
                id: '1',
                name: 'Option 1',
                color: 'indigo2',
              },
              {
                id: '2',
                name: 'Option 2',
                color: 'red5',
              },
            ],
            defaultValue: '',
          },
        },
        {
          templateId: 'multi_select',
          type: 'MULTI_SELECT',
          name: {
            en: 'Multi Select',
            'zh-CN': '多选',
            'zh-TW': '多選',
            ja: '複数選択',
          },
          property: {
            options: [
              {
                id: '1',
                name: 'Option 1',
              },
              {
                id: '2',
                name: 'Option 2',
              },
            ],
            defaultValue: [],
          },
        },
        {
          templateId: 'url',
          type: 'URL',
          name: {
            en: 'URL',
            'zh-CN': '网址',
            'zh-TW': '網址',
            ja: 'URL',
          },
        },
        {
          templateId: 'created_time',
          type: 'CREATED_TIME',
          name: {
            en: 'Created Time',
            'zh-CN': '创建时间',
            'zh-TW': '建立時間',
            ja: '作成時間',
          },
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: true,
          },
        },
        {
          templateId: 'modified_time',
          type: 'MODIFIED_TIME',
          name: {
            en: 'Modified Time',
            'zh-CN': '修改时间',
            'zh-TW': '修改時間',
            ja: '修正時間',
          },
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: true,
          },
        },
        {
          templateId: 'link',
          type: 'LINK',
          name: {
            en: 'Link',
            'zh-CN': '关联',
            'zh-TW': '關聯',
            ja: 'リンク',
          },
          property: {
            foreignDatabaseTemplateId: 'database-link',
            brotherFieldTemplateId: 'database-link:single_text',
          },
        },
        {
          templateId: 'lookup',
          type: 'LOOKUP',
          name: {
            en: 'Lookup',
            'zh-CN': '查找',
            'zh-TW': '查詢',
            ja: 'ルックアップ',
          },
          property: {
            databaseTemplateId: 'folder:database',
            relatedLinkFieldTemplateId: 'folder:database:link',
            lookupTargetFieldTemplateId: 'folder:database-link:auto_number',
          },
        },
        {
          templateId: 'member',
          type: 'MEMBER',
          name: {
            en: 'Member',
            'zh-CN': '成员',
            'zh-TW': '成員',
            ja: 'メンバー',
          },
          property: {}, // todo
        },
        {
          templateId: 'formula',
          type: 'FORMULA',
          name: {
            en: 'Formula',
            'zh-CN': '公式',
            'zh-TW': '公式',
            ja: 'フォーミュラ',
          },
          property: {
            expressionTemplate: '{number}',
          },
        },
        {
          templateId: 'attachment',
          type: 'ATTACHMENT',
          name: {
            en: 'Attachment',
            'zh-CN': '附件',
            'zh-TW': '附件',
            ja: '添付ファイル',
          },
        },
      ],
      records: [
        {
          templateId: 'database_record_1',
          data: {
            single_text: 'record1',
            long_text: 'record1',
            auto_number: 1,
            checkbox: true,
            currency: 1,
            datetime: '1970-01-01T00:00:00.000Z',
            daterange: '1970-01-01T00:00:00.000Z/1970-01-02T00:00:00.000Z',
            number: 123,
            percent: 0.5,
            phone: '1234567890',
            email: '<EMAIL>',
            rating: 3,
            single_select: 'Option 1',
            multi_select: ['Option 1', 'Option 2'],
            url: 'https://example.com',
            created_time: '1970-01-01T00:00:00.000Z',
            modified_time: '1970-01-02T00:00:00.000Z',
          },
          values: {
            single_text: 'record1',
            long_text: 'record1',
            auto_number: 1,
            checkbox: true,
            currency: 1,
            datetime: '1970-01-01T00:00:00.000Z',
            daterange: '1970-01-01T00:00:00.000Z/1970-01-02T00:00:00.000Z',
            number: 123,
            percent: 0.5,
            phone: '1234567890',
            email: '<EMAIL>',
            rating: 3,
            single_select: 'Option 1',
            multi_select: ['Option 1', 'Option 2'],
            url: 'https://example.com',
            created_time: '1970-01-01T00:00:00.000Z',
            modified_time: '1970-01-02T00:00:00.000Z',
          },
        },
        {
          templateId: 'database_record_2',
          data: {
            single_text: 'record2',
            long_text: 'record2',
            auto_number: 2,
            checkbox: false,
            currency: 1,
            datetime: '1970-01-01T00:00:00.000Z',
            daterange: '1970-01-01T00:00:00.000Z/1970-01-02T00:00:00.000Z',
            number: 123,
            percent: 0.5,
            phone: '1234567890',
            email: '<EMAIL>',
            rating: 3,
            single_select: 'Option 1',
            multi_select: ['Option 1', 'Option 2'],
            url: 'https://example.com',
            created_time: '1970-01-01T00:00:00.000Z',
            modified_time: '1970-01-02T00:00:00.000Z',
          },
          values: {
            single_text: 'record2',
            long_text: 'record2',
            auto_number: 2,
            checkbox: false,
            currency: 1,
            datetime: '1970-01-01T00:00:00.000Z',
            daterange: '1970-01-01T00:00:00.000Z/1970-01-02T00:00:00.000Z',
            number: 123,
            percent: 0.5,
            phone: '1234567890',
            email: '<EMAIL>',
            rating: 3,
            single_select: 'Option 1',
            multi_select: ['Option 1', 'Option 2'],
            url: 'https://example.com',
            created_time: '1970-01-01T00:00:00.000Z',
            modified_time: '1970-01-02T00:00:00.000Z',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      databaseType: 'DATUM',
      templateId: 'database-link',
      name: { en: 'New database', 'zh-CN': '新数据表', 'zh-TW': '新數據表', ja: '新しいデータベース' },
      views: [
        {
          templateId: 'table_view',
          type: 'TABLE',
          name: {
            en: 'new table view',
            'zh-CN': '新表格视图',
            'zh-TW': '新表格視圖',
            ja: '新しいテーブルビュー',
          },
        },
      ],
      fields: [
        {
          templateId: 'auto_number',
          type: 'AUTO_NUMBER',
          name: {
            en: 'Auto Number',
            'zh-CN': '自动编号',
            'zh-TW': '自動編號',
            ja: '自動番号付け',
          },
          property: {
            nextId: 1,
          },
        },
        {
          templateId: 'single_text',
          type: 'SINGLE_TEXT',
          name: {
            en: 'Single Line Text',
            'zh-CN': '单行文本',
            'zh-TW': '單行文本',
            ja: 'シングルテキスト',
          },
        },
      ],
    },
    {
      resourceType: 'MIRROR',
      mirrorType: 'DATABASE_VIEW',
      templateId: 'mirror',
      name: {
        en: 'New Unnamed Mirror',
        'zh-CN': '新的未命名镜像',
        'zh-TW': '新的未命名鏡像',
        ja: '新しい未命名ミラー',
      },
      databaseTemplateId: 'database',
      viewTemplateId: 'table_view',
    },
    {
      resourceType: 'FORM',
      templateId: 'form',
      formType: 'DATABASE',
      name: {
        en: 'From',
        'zh-CN': '表单',
        'zh-TW': '表單',
        ja: 'フォーム',
      },
      databaseTemplateId: 'database',
      metadata: {
        type: 'VIEW',
        viewTemplateId: 'table_view',
      },
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'automation',
      name: {
        en: 'New automation',
        'zh-CN': '新的自动化流程',
        'zh-TW': '新的自動化流程',
        ja: '新しい自動化',
      },
      triggers: [
        {
          templateId: 'record_created_trigger',
          triggerType: 'RECORD_CREATED',
          input: {
            type: 'DATABASE',
            databaseTemplateId: 'database',
          },
        },
      ],
      actions: [
        {
          templateId: 'webhook_action',
          actionType: 'WEBHOOK',
          input: {
            type: 'WEBHOOK',
            method: 'GET',
            url: '',
            headers: [],
          },
        },
        {
          templateId: 'run_script_action',
          actionType: 'RUN_SCRIPT',
          input: {
            type: 'SCRIPT',
            language: 'python',
            script: 'hello word !',
          },
        },
      ],
    },
    {
      resourceType: 'DASHBOARD',
      templateId: 'dashboard',
      name: {
        'zh-CN': '仪表盘',
        'zh-TW': '儀表盤',
        en: 'Dashboard',
        ja: 'ダッシュボード',
      },
      widgets: [
        {
          id: 'chart_widget',
          templateId: 'line_chart_widget',
          type: 'CHART',
          name: {
            'zh-CN': '折线图小组件',
            'zh-TW': '折线图小组件',
            en: 'Line Chart Widget',
            ja: '折れ線グラフウィジェット',
          },
          datasource: {
            type: 'CUSTOM',
            xAxis: {
              type: 'category',
              data: ['A', 'B', 'C', 'D', 'E', 'F'],
            },
            yAxis: {
              type: 'value',
            },
            series: [
              {
                type: 'line',
                data: [5, 20, 36, 10, 10, 20],
              },
            ],
          },
        },
        {
          id: 'text_widget',
          type: 'TEXT',
          name: {
            en: 'Text Widget',
            'zh-CN': '文本小组件',
            'zh-TW': '文本小组件',
            ja: 'テキストウィジェット',
          },
          datasource: {
            type: 'CUSTOM',
            text: {
              // use 'hello bika' as default text
              en: 'hello bika',
              'zh-CN': '你好，Bika',
              'zh-TW': '你好，Bika',
              ja: 'こんにちは、Bika',
            },
          },
        },
        {
          id: 'number_widget',
          type: 'NUMBER',
          name: {
            en: 'Number Widget',
            'zh-CN': '数字小组件',
            'zh-TW': '數字小组件',
            ja: '数字ウィジェット',
          },
          targetValue: 1,
          summaryDescription: 'summaryDescription',
          datasource: {
            type: 'CUSTOM',
            number: 123,
          },
        },
        // {
        //   type: 'PROGRESS_BAR',
        //   name: 'Progress Bar Widget',
        //   title: 'Progress Bar',
        //   used: 25,
        //   usedLabel: 'Used',
        //   unusedLabel: 'Unused',
        //   total: 100,
        //   description: 'This widget displays a progress bar.',
        // },
        {
          id: 'pivot_table_widget',
          type: 'PIVOT_TABLE',
          name: 'Pivot Table Widget',
          datasource: {
            type: 'DATABASE',
            databaseTemplateId: 'database',
            viewTemplateId: 'table_view',
            fields: {
              rows: [],
              columns: [],
              values: [],
            },
          },
        },
        {
          id: 'embed_widget',
          type: 'EMBED',
          name: 'Embed Widget',
          description: 'This widget embeds external content.',
          url: 'https://bika.ai/zh-CN',
        },
        // {
        //   type: 'ICONS',
        //   name: 'Icons Widget',
        //   icons: [
        //     { icon: { type: 'COLOR', color: '' }, name: 'CRM', url: '' },
        //     { icon: { type: 'COLOR', color: '' }, name: 'OKR', url: '' },
        //     { icon: { type: 'URL', url: '' }, name: '', url: '' },
        //   ],
        //   description: 'This widget displays icons.',
        // },
        // {
        //   type: 'LIST',
        //   name: 'List Widget',
        //   items: [
        //     {
        //       name: `Hey,you haven't filled out the morning meeting notes yet. Come quickly and fill out your plan for today!`,
        //       id: '2',
        //       url: '',
        //       createdAt: new Date().toISOString(),
        //       tags: ['PENDING'],
        //       recipients: [
        //         {
        //           id: '2',
        //           name: 'Cindy',
        //           type: 'Member',
        //           email: '',
        //           avatar: { type: 'COLOR', color: '#7b67ee' },
        //           isGuest: false,
        //         },
        //       ],
        //       dot: true,
        //     },
        //     {
        //       id: '1',
        //       name: 'Daily Standup Guide #2: Invite members to join the space',
        //       url: '',
        //       createdAt: new Date().toISOString(),
        //       tags: ['PENDING'],
        //       recipients: [
        //         {
        //           id: '1',
        //           name: 'Cindy',
        //           type: 'Member',
        //           email: '',
        //           avatar: { type: 'COLOR', color: '#7b67ee' },
        //           isGuest: false,
        //         },
        //       ],
        //       dot: true,
        //     },
        //     {
        //       id: '2',
        //       name: `Hey,you haven't filled out the morning meeting notes yet. Come quickly and fill out your plan for today!`,
        //       url: '',
        //       recipients: [
        //         {
        //           id: '2',
        //           name: 'Cindy',
        //           type: 'Member',
        //           email: '',
        //           avatar: { type: 'COLOR', color: '#7b67ee' },
        //           isGuest: false,
        //         },
        //       ],
        //       createdAt: new Date().toISOString(),
        //       tags: ['DUE'],
        //     },
        //     {
        //       id: '3',
        //       name: 'Daily Standup Guide #1: Enable Daily Standup Automation Reminder',
        //       url: '',
        //       recipients: [
        //         {
        //           id: '3',
        //           name: 'Cindy',
        //           type: 'Member',
        //           email: '',
        //           avatar: { type: 'COLOR', color: '#7b67ee' },
        //           isGuest: false,
        //         },
        //       ],
        //       createdAt: new Date().toISOString(),
        //       tags: ['COMPLETED'],
        //     },
        //   ],
        // },
      ],
    },
  ],
};
