import assert from 'assert';
import { iStringParse } from 'basenext/i18n/i-string';
import _ from 'lodash';
import { generateNanoID } from 'sharelib/nano-id';
import { Action, Automation } from '../automation/bo';
import { AutomationVO } from '../automation/vo';
import { Dashboard } from '../dashboard/bo-dashboard';
import { EChartRenderVO } from '../dashboard/bo-datasource';
import {
  ChartWidgetBO,
  EmbedWidgetBO,
  NumberWidgetBO,
  PivotTableWidgetBO,
  TextWidget,
  WidgetBO,
} from '../dashboard/bo-widgets';
import {
  ChartWidgetVO,
  DashboardVO,
  DatasourceRenderVO,
  EmbedWidgetVO,
  NumberWidgetRenderVO,
  NumberWidgetVO,
  PivotTableWidgetVO,
  TextWidgetVO,
  WidgetVO,
} from '../dashboard/vo-dashboard';
import {
  Database,
  DatabaseField,
  DatabaseFieldType,
  DatabaseRecord,
  LinkFieldProperty,
  DatabaseFieldProperty,
  View,
  CellValue,
  SingleSelectFieldProperty,
  MultiSelectFieldProperty,
  ViewFilter,
} from '../database/bo';
import {
  PREFIX_MAP,
  DatabaseVO,
  FieldVO,
  BaseDatabaseVO,
  FieldVOSchema,
  BaseDatabaseVOSchema,
  ViewSimpleVO,
  ViewFieldVO,
  CellRenderVO,
  RecordRenderVO,
  CellValueVO,
  RecordPaginationVO,
  ViewVO,
} from '../database/vo';
import { DatabaseFormBO, FormBo, FormMetadata } from '../form/bo-form';
import {
  DatabaseViewMirrorBO,
  Folder,
  MirrorBO,
  NodeResource,
  NodeResourceMirrorBO,
  NodeResourceType,
  ViewMirrorBO,
} from '../node/bo';
import { ResourceVO, BORenderOpts, FolderDetailVO, NodeTreeVO, NodeDetailVO, FormVO, MirrorVO } from '../node/vo';
import { AvatarLogo } from '../system';
import { formatDateRangeCellValue, formatDateTimeCellValue } from '../system/formatting';

// =============================== Type Definitions ===============================

type GetInstanceFn = <T extends IBOInstance>(key?: string) => T | null;

interface IBOInstance {
  instanceId: string; // if the processor be created, the id must be set, if not, the processor not be created
  templateId?: string;
  id?: string;
}

interface IFieldInstance extends IBOInstance {
  property: DatabaseFieldProperty;
  type: DatabaseFieldType;
  primary: boolean;
}

interface IRecordInstance extends IBOInstance {
  title: string;
  getCellData(fieldInstanceId: string): CellValue;
  getCellValue(fieldInstanceId: string): CellValue;
}

interface IDatabaseInstance extends IBOInstance {
  fields: IFieldInstance[];
  findField(key?: string): IFieldInstance | null;
  views: IBOInstance[];
  findView(key?: string): IBOInstance | null;
  records: IRecordInstance[];
  findRecord(key?: string): IRecordInstance | null;
  bo: Database;
}

interface IAutomationInstance extends IBOInstance {
  actions: IBOInstance[];
  triggers: IBOInstance[];
  findAction(key?: string): IBOInstance | null;
  findTrigger(key?: string): IBOInstance | null;
}

interface IDashboardInstance extends IBOInstance {
  widgets: IBOInstance[];
  findWidget(key?: string): IBOInstance | null;
}

// =============================== Processor Interfaces ===============================

interface IBOProcessor<T, V> {
  /**
   * handle the relation instance, init every instance
   * important: this function will change the bo
   * @param getInstance - get the instance by the key
   */
  processRelatedInstances(getInstance: GetInstanceFn): T;
  toVO(opts: BORenderOpts): V;
}

interface INodeResourceProcessor<T extends NodeResource, V extends ResourceVO> extends IBOProcessor<T, V> {
  toVO(opts: BORenderOpts): V;
}

interface IViewProcessor extends IBOProcessor<View, void> {
  processRelatedInstances(getInstance: GetInstanceFn): View;
  toSimpleVO(opts: BORenderOpts): ViewSimpleVO;
  toVO(opts: BORenderOpts): void;
  getColumns(opts: BORenderOpts): (database: Database) => ViewFieldVO[];
}

interface IRecordProcessor extends IBOProcessor<DatabaseRecord, (database: Database) => RecordRenderVO> {
  toVO(opts: BORenderOpts): (database: Database) => RecordRenderVO;
}

interface IDatabaseProcessor extends INodeResourceProcessor<Database, DatabaseVO> {
  processRelatedInstances(getInstance: GetInstanceFn): Database;
  toVO(opts: BORenderOpts): DatabaseVO;
  toSimpleVO(opts: BORenderOpts): BaseDatabaseVO;
  getRecords(opts: BORenderOpts): RecordPaginationVO;
}

interface IAutomationProcessor extends INodeResourceProcessor<Automation, AutomationVO> {
  processRelatedInstances(getInstance: GetInstanceFn): Automation;
  toVO(opts: BORenderOpts): AutomationVO;
}

interface IFormProcessor extends INodeResourceProcessor<FormBo & { database?: Database }, FormVO> {
  processRelatedInstances(getInstance: GetInstanceFn): FormBo & { database?: Database };
  toVO(opts: BORenderOpts): FormVO;
}

interface IMirrorProcessor extends INodeResourceProcessor<MirrorBO & { database?: Database }, MirrorVO> {
  processRelatedInstances(getInstance: GetInstanceFn): MirrorBO & { database?: Database };
  toVO(opts: BORenderOpts): MirrorVO;
}

/**
 * Widget processor
 * widget vo is same with widget bo, but have the value for render
 * 1. process related instances
 * 2. process datasource value
 * 3. to VO
 */
interface IWidgetProcessor extends IBOProcessor<WidgetBO & { value?: DatasourceRenderVO }, WidgetVO> {
  processRelatedInstances(getInstance: GetInstanceFn): WidgetBO & { value?: DatasourceRenderVO };
  toVO(opts: BORenderOpts): WidgetVO;
}

interface IDashboardProcessor extends INodeResourceProcessor<Dashboard, DashboardVO> {
  processRelatedInstances(getInstance: GetInstanceFn): Dashboard;
  toVO(opts: BORenderOpts): DashboardVO;
}

interface IFolderProcessor extends IBOProcessor<Folder, FolderDetailVO> {
  processRelatedInstances(getInstance: GetInstanceFn): Folder;
  toVO(opts: BORenderOpts): FolderDetailVO;
}

// =============================== Utility Functions ===============================

/**
 * Generate instance ID with prefix
 */
function getInstanceId(type: keyof typeof PREFIX_MAP) {
  return (id?: string, newInstance: boolean = true): string => {
    if (newInstance) {
      return generateNanoID(PREFIX_MAP[type]);
    }
    if (!id) {
      throw new Error(`Instance id is required for ${type}`);
    }
    return id;
  };
}

/**
 * Set resource instance ID
 */
function setResourceInstanceId(getInstance: GetInstanceFn) {
  return <T extends { id?: string; templateId?: string }>(obj: T): T & { id: string } => {
    const instance = getInstance<IBOInstance>(obj.id || obj.templateId);
    if (instance) {
      _.set(obj, 'id', instance.instanceId);
    }
    return obj as T & { id: string };
  };
}

/**
 * Set resource ID
 * @param getInstance - get the instance by the key
 * @returns - set the resource instance ID to the object
 */
function setResourceId(getInstance: GetInstanceFn) {
  return <
    T extends
      | { resourceId?: string; resourceTemplateId?: string }
      | { formId?: string; formTemplateId?: string }
      | { databaseId?: string; databaseTemplateId?: string }
      | { foreignDatabaseId?: string; foreignDatabaseTemplateId?: string },
  >(
    obj: T,
  ): T & { [K in keyof T]: string } => {
    const resourceIdMappings = [
      { key: 'resourceId', templateKey: 'resourceTemplateId' },
      { key: 'formId', templateKey: 'formTemplateId' },
      { key: 'databaseId', templateKey: 'databaseTemplateId' },
      { key: 'foreignDatabaseId', templateKey: 'foreignDatabaseTemplateId' },
    ];

    for (const mapping of resourceIdMappings) {
      if (mapping.key in obj || mapping.templateKey in obj) {
        const resourceId = obj[mapping.key as keyof T] as string | undefined;
        const resourceTemplateId = obj[mapping.templateKey as keyof T] as string | undefined;
        const resource = getInstance<IBOInstance>(resourceId || resourceTemplateId);
        if (resource) {
          _.set(obj, mapping.key, resource.instanceId);
        }
        break;
      }
    }

    return obj as T & { [K in keyof T]: string };
  };
}

/**
 * Set field ID for various field-related objects
 */
function setFieldId(getInstance: GetInstanceFn) {
  return <
    T extends
      | { id?: string; templateId?: string }
      | { fieldId?: string; fieldTemplateId?: string }
      | { kanbanGroupingFieldId?: string; kanbanGroupingFieldTemplateId?: string }
      | { coverFieldId?: string; coverFieldTemplateId?: string }
      | { relatedLinkFieldId?: string; relatedLinkFieldTemplateId?: string }
      | { lookupTargetFieldId?: string; lookupTargetFieldTemplateId?: string }
      | { brotherFieldId?: string; brotherFieldTemplateId?: string }
      | { dimension?: string; dimensionTemplateId?: string },
  >(
    databaseId: string,
    obj: T,
  ): T & { [K in keyof T]: string } => {
    const database = getInstance<IDatabaseInstance>(databaseId);

    const fieldIdMappings = [
      { key: 'id', templateKey: 'templateId' },
      { key: 'fieldId', templateKey: 'fieldTemplateId' },
      { key: 'kanbanGroupingFieldId', templateKey: 'kanbanGroupingFieldTemplateId' },
      { key: 'coverFieldId', templateKey: 'coverFieldTemplateId' },
      { key: 'relatedLinkFieldId', templateKey: 'relatedLinkFieldTemplateId' },
      { key: 'lookupTargetFieldId', templateKey: 'lookupTargetFieldTemplateId' },
      { key: 'brotherFieldId', templateKey: 'brotherFieldTemplateId' },
      { key: 'dimension', templateKey: 'dimensionTemplateId' },
    ];

    for (const mapping of fieldIdMappings) {
      if (mapping.key in obj || mapping.templateKey in obj) {
        const fieldId = obj[mapping.key as keyof T] as string | undefined;
        const fieldTemplateId = obj[mapping.templateKey as keyof T] as string | undefined;
        const field = database?.findField(fieldId || fieldTemplateId);
        if (field) {
          _.set(obj, mapping.key, field.instanceId);
        }
        break;
      }
    }

    return obj as T & { [K in keyof T]: string };
  };
}

/**
 * Set view ID
 */
function setViewId(getInstance: GetInstanceFn) {
  return <T extends { id?: string; templateId?: string } | { viewId?: string; viewTemplateId?: string }>(
    databaseId: string,
    obj: T,
  ): T & { id: string } => {
    const database = getInstance<IDatabaseInstance>(databaseId);
    if ('id' in obj || 'templateId' in obj) {
      const view = database?.findView(obj.id || obj.templateId);
      _.set(obj, 'id', view?.instanceId);
    }
    if ('viewId' in obj || 'viewTemplateId' in obj) {
      const view = database?.findView(obj.viewId || obj.viewTemplateId);
      _.set(obj, 'viewId', view?.instanceId);
    }
    return obj as T & { id: string };
  };
}

/**
 * Set record ID
 */
function setRecordId(getInstance: GetInstanceFn) {
  return <T extends { id?: string; templateId?: string }>(databaseId: string, obj: T): T & { id: string } => {
    const database = getInstance<IDatabaseInstance>(databaseId);
    if ('id' in obj || 'templateId' in obj) {
      const record = database?.findRecord(obj.id || obj.templateId);
      _.set(obj, 'id', record?.instanceId);
    }
    return obj as T & { id: string };
  };
}

/**
 * Process record data and set related link record IDs
 */
function setRecord(getInstance: GetInstanceFn) {
  return (databaseId: string, record: DatabaseRecord): DatabaseRecord => {
    const database = getInstance<IDatabaseInstance>(databaseId);
    setRecordId(getInstance)(databaseId, record);

    // Process record data
    Object.keys(record.data).forEach((key) => {
      const field = database?.findField(key);
      const value = record.data[key];

      if (field) {
        _.set(record.data, field.instanceId, value);
        if (key !== field.instanceId) {
          _.unset(record.data, key);
        }

        // Handle link field types
        if ((field.type === 'LINK' || field.type === 'ONE_WAY_LINK') && value && Array.isArray(value)) {
          const property = field.property as LinkFieldProperty;
          const foreignDatabase = getInstance<IDatabaseInstance>(
            property.foreignDatabaseId || property.foreignDatabaseTemplateId,
          );

          if (foreignDatabase) {
            const newValues: string[] = [];
            const newTitles: string[] = [];

            for (const v of value) {
              const foreignRecord = foreignDatabase.findRecord(v as string | undefined);
              if (foreignRecord) {
                newValues.push(foreignRecord.instanceId);
                newTitles.push(foreignRecord.title);
              }
            }

            _.set(record.data, field.instanceId, newValues);
            _.set(record, ['values', field.instanceId], newTitles);
          }
        }
      }
    });

    return record;
  };
}

/**
 * Set action ID
 */
function setActionId(getInstance: GetInstanceFn) {
  return <T extends { id?: string; templateId?: string }>(automationId: string, obj: T): T & { id: string } => {
    const automation = getInstance<IAutomationInstance>(automationId);
    if ('id' in obj || 'templateId' in obj) {
      const action = automation?.findAction(obj.id || obj.templateId);
      _.set(obj, 'id', action?.instanceId);
    }
    return obj as T & { id: string };
  };
}

/**
 * Set trigger ID
 */
function setTriggerId(getInstance: GetInstanceFn) {
  return <T extends { id?: string; templateId?: string }>(automationId: string, obj: T): T & { id: string } => {
    const automation = getInstance<IAutomationInstance>(automationId);
    if ('id' in obj || 'templateId' in obj) {
      const trigger = automation?.findTrigger(obj.id || obj.templateId);
      _.set(obj, 'id', trigger?.instanceId);
    }
    return obj as T & { id: string };
  };
}

/**
 * Set widget ID
 */
function setWidgetId(getInstance: GetInstanceFn) {
  return <T extends { id?: string; templateId?: string | null }>(dashboardId: string, obj: T): T & { id: string } => {
    const dashboard = getInstance<IDashboardInstance>(dashboardId);
    if ('id' in obj || 'templateId' in obj) {
      const widget = dashboard?.findWidget(obj.id || obj.templateId || undefined);
      _.set(obj, 'id', widget?.instanceId);
    }
    return obj as T & { id: string };
  };
}

function setAutomationInput(automationId: string, getInstance: GetInstanceFn) {
  return <
    T extends
      | ({ databaseId?: string; databaseTemplateId?: string } & {
          filters?: ViewFilter;
          viewId?: string;
          viewTemplateId?: string;
          fieldId?: string;
          fieldTemplateId?: string;
        })
      | { formId?: string; formTemplateId?: string }
      | ({ dashboardId?: string; dashboardTemplateId?: string } & {
          widgetId?: string;
          widgetTemplateId?: string;
        })
      | { actionId?: string; actionTemplateId?: string },
  >(
    input: T,
  ): T => {
    if ('databaseId' in input || 'databaseTemplateId' in input) {
      const database = getInstance<IDatabaseInstance>(input.databaseId || input.databaseTemplateId);
      if (database) {
        _.set(input, 'databaseId', database.instanceId);
        input.filters?.conds?.forEach((condition) => {
          const field = database?.findField(condition.fieldId || condition.fieldTemplateId);
          if (field) {
            _.set(condition, 'fieldId', field.instanceId);
          }
        });
        const view = database?.findView(input.viewId || input.viewTemplateId);
        if (view) {
          _.set(input, 'viewId', view.instanceId);
        }
        const field = database?.findField(input.fieldId || input.fieldTemplateId);
        if (field) {
          _.set(input, 'fieldId', field.instanceId);
        }
      }
    }

    if ('formId' in input || 'formTemplateId' in input) {
      const form = getInstance<IBOInstance>(input.formId || input.formTemplateId);
      if (form) {
        _.set(input, 'formId', form.instanceId);
      }
    }

    if ('dashboardId' in input || 'dashboardTemplateId' in input) {
      const dashboard = getInstance<IDashboardInstance>(input.dashboardId || input.dashboardTemplateId);
      if (dashboard) {
        _.set(input, 'dashboardId', dashboard.instanceId);
      }
      const widget = dashboard?.findWidget(input.widgetId || input.widgetTemplateId);
      if (widget) {
        _.set(input, 'widgetId', widget.instanceId);
      }
    }

    if ('actionId' in input || 'actionTemplateId' in input) {
      const automation = getInstance<IAutomationInstance>(automationId);
      if (automation) {
        const action = automation.findAction(input.actionId || input.actionTemplateId);
        if (action) {
          _.set(input, 'actionId', action.instanceId);
        }
      }
    }

    return input as T;
  };
}

function setAutomationInputVariables(automationId: string, getInstance: GetInstanceFn, databaseIds: string[]) {
  const databases = databaseIds.map((id) => getInstance<IDatabaseInstance>(id)).filter((database) => database !== null);

  const findField = (fieldId: string) => {
    for (const database of databases) {
      const field = database.findField(fieldId);
      if (field) {
        return field;
      }
    }
    return null;
  };

  const automation = getInstance<IAutomationInstance>(automationId);

  return (input: Record<string, unknown>): Record<string, unknown> => {
    const loopInput = (data: Record<string, unknown>): void => {
      for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          const value = data[key];
          if (typeof value === 'string') {
            // 匹配被<%= %> 包裹并通过.分割的字符串
            const templateRegex = /<%= ([^%>]*\.[^%>]*) %>/g;
            let newValue = value;
            if (templateRegex.test(value)) {
              // 只要包含模板字符串就处理
              newValue = value.replace(templateRegex, (_match, path) => {
                const pathSegments = path.split('.');
                const replaceValues: string[] = [];
                console.log('automation variable match', pathSegments);
                for (const segment of pathSegments) {
                  const action = automation?.findAction(segment);
                  if (action) {
                    replaceValues.push(action.instanceId);
                    continue;
                  }
                  const field = findField(segment);
                  if (field) {
                    replaceValues.push(field.instanceId);
                    continue;
                  }
                  replaceValues.push(segment);
                }
                // 其他情况原样返回
                return `<%= ${replaceValues.join('.')} %>`;
              });
              _.set(data, key, newValue);
            }
          } else if (typeof value === 'object' && value !== null) {
            if (Array.isArray(value)) {
              for (const item of value) {
                if (typeof item === 'object' && item !== null) {
                  loopInput(item as Record<string, unknown>);
                }
              }
            } else {
              loopInput(value as Record<string, unknown>);
            }
          }
        }
      }
    };

    loopInput(input);
    return input;
  };
}

function getChartWidgetRenderValue(getInstance: GetInstanceFn) {
  return (widget: ChartWidgetBO): EChartRenderVO => {
    const { datasource, settings } = widget;
    if (datasource.type === 'CUSTOM') {
      return { xAxis: datasource.xAxis, yAxis: datasource.yAxis, series: datasource.series };
    }
    const chartValue: EChartRenderVO = {
      xAxis: { type: 'category', data: ['example1', 'example2', 'example3'] },
      yAxis: { type: 'value' },
      series: [{ type: 'line', data: [1, 2, 3] }],
    };
    if (datasource.type !== 'DATABASE') {
      return chartValue;
    }
    const { metrics, metricsType, chartType, dimension, databaseId } = datasource;

    const database = getInstance<IDatabaseInstance>(databaseId);
    if (!database) {
      return chartValue;
    }

    const records = database.records;

    const getDimensionValue = (record: IRecordInstance): string[] => {
      if (!dimension) {
        return [];
      }
      const dimensionValue = record.getCellData(dimension);
      if (!dimensionValue) {
        return [];
      }
      if (typeof dimensionValue === 'string') {
        return [dimensionValue];
      }
      if (Array.isArray(dimensionValue)) {
        if (!settings?.isSepareted) {
          return [dimensionValue.join(',')];
        }
        return dimensionValue as string[];
      }
      if (typeof dimensionValue === 'string') {
        return [dimensionValue];
      }
      return [];
    };

    if (metricsType === 'COUNT_RECORDS') {
      const dimensionRecords = records.reduce(
        (acc, record) => {
          const dimensionValues = getDimensionValue(record);
          dimensionValues.forEach((v) => {
            acc[v] = (acc[v] || 0) + 1;
          });
          return acc;
        },
        {} as Record<string, number>,
      );

      return {
        xAxis: { type: 'category', data: Object.keys(dimensionRecords) },
        yAxis: { type: 'value' },
        series: [{ type: 'line', data: Object.values(dimensionRecords) }],
      };
    }

    if (metricsType === 'AGGREGATION_BY_FIELD' && metrics) {
      const { aggregationType, fieldId } = metrics;
      if (!fieldId) {
        return chartValue;
      }
      const callRecords = records.map((record) => {
        const dimensionValue = getDimensionValue(record);
        const metricValue = record.getCellData(fieldId);
        return {
          dimensionValue,
          metricValue: typeof metricValue === 'number' ? metricValue : 0,
        };
      });
      let values: Record<string, number> = {};
      switch (aggregationType) {
        case 'SUM':
          values = callRecords.reduce(
            (acc, record) => {
              record.dimensionValue.forEach((v) => {
                acc[v] = (acc[v] || 0) + record.metricValue;
              });
              return acc;
            },
            {} as Record<string, number>,
          );
          break;
        case 'MIN':
          values = callRecords.reduce(
            (acc, record) => {
              record.dimensionValue.forEach((v) => {
                acc[v] = Math.min(acc[v] || 0, record.metricValue);
              });
              return acc;
            },
            {} as Record<string, number>,
          );
          break;
        case 'MAX':
          values = callRecords.reduce(
            (acc, record) => {
              record.dimensionValue.forEach((v) => {
                acc[v] = Math.max(acc[v] || 0, record.metricValue);
              });
              return acc;
            },
            {} as Record<string, number>,
          );
          break;
        case 'AVG':
          {
            const dementionCount: Record<string, number> = {};
            values = callRecords.reduce(
              (acc, record) => {
                record.dimensionValue.forEach((v) => {
                  acc[v] = (acc[v] || 0) + record.metricValue;
                  dementionCount[v] = (dementionCount[v] || 0) + 1;
                });
                return acc;
              },
              {} as Record<string, number>,
            );
            values = Object.fromEntries(
              Object.entries(values).map(([v, value]) => [v, value / (dementionCount[v] || 1)]),
            );
          }
          break;
        default:
          break;
      }
      if (Object.keys(values).length > 0) {
        return {
          xAxis: { type: 'category', data: Object.keys(values) },
          yAxis: { type: 'value' },
          series: [{ type: chartType, data: Object.values(values) }],
        };
      }
    }
    return chartValue;
  };
}

function getNumberWidgetRenderValue(getInstance: GetInstanceFn) {
  return (widget: NumberWidgetBO): NumberWidgetRenderVO => {
    const { datasource } = widget;
    const targetValue = widget.targetValue ? String(widget.targetValue) : undefined;
    let value = 'NaN';
    if (datasource.type === 'CUSTOM') {
      value = String(datasource.number);
      return { value, targetValue };
    }
    if (datasource.type === 'DATABASE') {
      const { metrics, metricsType, databaseId } = datasource;
      const database = getInstance<IDatabaseInstance>(databaseId);
      if (!database) {
        return { value, targetValue };
      }
      const records = database.records;

      if (metricsType === 'COUNT_RECORDS') {
        value = database.records.length.toString();
        return { value, targetValue };
      }

      if (metricsType === 'AGGREGATION_BY_FIELD' && metrics) {
        const { fieldId } = metrics;
        if (!fieldId) {
          return { value, targetValue };
        }

        const getNumberValue = (record: IRecordInstance): number => {
          const data = record.getCellData(fieldId);
          return typeof data === 'number' ? data : 0;
        };

        const values = records.map(getNumberValue);

        switch (metrics?.aggregationType) {
          case 'SUM':
            value = values.reduce((a, b) => a + b, 0).toString();
            break;
          case 'MIN': {
            const min = Math.min(...values);
            value = Number.isFinite(min) ? min.toString() : '0';
            break;
          }
          case 'MAX': {
            const max = Math.max(...values);
            value = Number.isFinite(max) ? max.toString() : '0';
            break;
          }
          case 'AVG': {
            const sum = values.reduce((a, b) => a + b, 0);
            value = values.length > 0 ? (sum / values.length).toString() : '0';
            break;
          }
          case 'NOT_FILLED':
            value = values.filter((v) => v === 0).length.toString();
            break;
          case 'FILLED':
            value = values.filter((v) => v !== 0).length.toString();
            break;
          default:
            break;
        }
      }
    }
    return { value, targetValue };
  };
}

// datasource render value
function setWidgetValue(getInstance: GetInstanceFn) {
  return (widget: WidgetBO): WidgetBO & { _value?: DatasourceRenderVO } => {
    switch (widget.type) {
      case 'CHART':
        _.set(widget, '_value', getChartWidgetRenderValue(getInstance)(widget));
        break;
      case 'NUMBER':
        _.set(widget, '_value', getNumberWidgetRenderValue(getInstance)(widget));
        break;
      default:
        break;
    }
    return widget;
  };
}

// =============================== Instance Factory ===============================

/**
 * Create default node instance
 */
function createDefaultNodeInstance(resource: NodeResource, newInstance: boolean): IBOInstance {
  return {
    instanceId: getInstanceId(resource.resourceType as keyof typeof PREFIX_MAP)(resource.id, newInstance),
    id: resource.id,
    templateId: resource.templateId,
  };
}

/**
 * Create database instance with fields, views, and records
 */
function createDatabaseInstance(database: NodeResource, newInstance: boolean): IDatabaseInstance {
  const { id, templateId, fields, views, records } = database as Database;

  const fieldsInstance =
    fields?.map((f) => ({
      instanceId: getInstanceId('FIELD')(f.id, newInstance),
      templateId: f.templateId,
      id: f.id,
      property: f.property as DatabaseFieldProperty,
      type: f.type,
      primary: f.primary || false,
    })) || [];

  const viewsInstance =
    views?.map((v) => ({
      instanceId: getInstanceId('VIEW')(v.id, newInstance),
      templateId: v.templateId,
      id: v.id,
    })) || [];

  const primaryField = fieldsInstance.find((f) => f.primary);
  const getTitle = (record: DatabaseRecord) => {
    if (primaryField) {
      if (primaryField.id && record.data[primaryField.id]) {
        return record.data[primaryField.id] as string;
      }
      if (primaryField.templateId && record.data[primaryField.templateId]) {
        return record.data[primaryField.templateId] as string;
      }
    }
    return 'Err!';
  };

  const getCellData = (record: DatabaseRecord, fieldInstanceId: string, key: 'data' | 'values') => {
    if (record.data[fieldInstanceId]) {
      return record.data[fieldInstanceId];
    }
    let value = _.get(record, [key, fieldInstanceId]);
    if (value) {
      return value;
    }
    const field = fieldsInstance.find((f) => f.instanceId === fieldInstanceId);
    if (field && field.id) {
      if (field.id) {
        value = _.get(record, [key, field.id]);
        if (value) {
          return value;
        }
      }
      if (field.templateId) {
        value = _.get(record, [key, field.templateId]);
        if (value) {
          return value;
        }
      }
    }
    return null;
  };

  const recordsInstance =
    records?.map((r) => ({
      instanceId: getInstanceId('RECORD')(r.id, newInstance),
      templateId: r.templateId,
      id: r.id,
      title: getTitle(r),
      getCellData: (fieldInstanceId: string) => getCellData(r, fieldInstanceId, 'data'),
      getCellValue: (fieldInstanceId: string) => getCellData(r, fieldInstanceId, 'values'),
    })) || [];

  return {
    instanceId: getInstanceId('DATABASE')(id, newInstance),
    id,
    templateId,
    fields: fieldsInstance,
    views: viewsInstance,
    records: recordsInstance,
    findField: (key?: string) =>
      fieldsInstance.find((f) => f.id === key || f.templateId === key || f.instanceId === key) || null,
    findView: (key?: string) =>
      viewsInstance.find((v) => v.id === key || v.templateId === key || v.instanceId === key) || null,
    findRecord: (key?: string) =>
      recordsInstance.find((r) => r.id === key || r.templateId === key || r.instanceId === key) || null,
    bo: database as Database,
  };
}

/**
 * Create automation instance with actions and triggers
 */
function createAutomationInstance(automation: NodeResource, newInstance: boolean): IAutomationInstance {
  const { id, templateId, actions, triggers } = automation as Automation;

  const actionInstances: IBOInstance[] = [];

  const loopActions = (items: Action[]) => {
    for (const action of items) {
      actionInstances.push({
        instanceId: getInstanceId('ACTION')(action.id, newInstance),
        templateId: action.templateId,
        id: action.id,
      });
      if ('actions' in action) {
        loopActions(action.actions || []);
      }
    }
  };

  loopActions(actions || []);

  const triggerInstances =
    triggers?.map((trigger) => ({
      instanceId: getInstanceId('TRIGGER')(trigger.id, newInstance),
      templateId: trigger.templateId,
      id: trigger.id,
    })) || [];

  return {
    instanceId: getInstanceId('AUTOMATION')(id, newInstance),
    id,
    templateId,
    actions: actionInstances,
    triggers: triggerInstances,
    findAction: (key?: string) => actionInstances.find((a) => a.id === key || a.templateId === key) || null,
    findTrigger: (key?: string) => triggerInstances.find((t) => t.id === key || t.templateId === key) || null,
  };
}

/**
 * Create dashboard instance with widgets
 */
function createDashboardInstance(dashboard: NodeResource, newInstance: boolean): IDashboardInstance {
  const { id, templateId, widgets } = dashboard as Dashboard;

  const widgetsInstance =
    widgets?.map((widget) => ({
      instanceId: getInstanceId('WIDGET')(widget.id, newInstance),
      templateId: widget.templateId || undefined,
      id: widget.id,
    })) || [];

  return {
    instanceId: getInstanceId('DASHBOARD')(id, newInstance),
    id,
    templateId,
    widgets: widgetsInstance,
    findWidget: (key?: string) => widgetsInstance.find((w) => w.id === key || w.templateId === key) || null,
  };
}

const _ResourceInstanceMap: Record<
  Exclude<NodeResourceType, 'FOLDER'>,
  (resource: NodeResource, newInstance: boolean) => IBOInstance
> = {
  DATABASE: createDatabaseInstance,
  AUTOMATION: createAutomationInstance,
  DASHBOARD: createDashboardInstance,
  FORM: createDefaultNodeInstance,
  // VIEW: createDefaultNodeInstance,
  DATAPAGE: createDefaultNodeInstance,
  CANVAS: createDefaultNodeInstance,
  EMBED: createDefaultNodeInstance,
  ALIAS: createDefaultNodeInstance,
  DOCUMENT: createDefaultNodeInstance,
  FILE: createDefaultNodeInstance,
  REPORT_TEMPLATE: createDefaultNodeInstance,
  AI: createDefaultNodeInstance,
  ROOT: createDefaultNodeInstance,
  TEMPLATE: createDefaultNodeInstance,
  PAGE: createDefaultNodeInstance,
  MIRROR: createDefaultNodeInstance,
};

/**
 * Create resource instance factory function
 */
function createResourceInstance(resources: NodeResource[], newInstance: boolean): GetInstanceFn {
  const instanceMap = new Map<string, IBOInstance>();
  // 创建反向索引映射，用于通过 id 或 templateId 快速查找
  const idToInstanceIdMap = new Map<string, string>();
  const templateIdToInstanceIdMap = new Map<string, string>();

  const processFolder = (folder: Folder) => {
    const instanceId = getInstanceId('FOLDER')(folder.id, newInstance);
    const folderInstance: IBOInstance = {
      instanceId,
      id: folder.id,
      templateId: folder.templateId,
    };

    instanceMap.set(instanceId, folderInstance);

    // 建立反向索引
    if (folderInstance.id) {
      idToInstanceIdMap.set(folderInstance.id, instanceId);
    }
    if (folderInstance.templateId) {
      templateIdToInstanceIdMap.set(folderInstance.templateId, instanceId);
    }

    folder.children?.forEach((child) => {
      if (child.resourceType === 'FOLDER') {
        processFolder(child as Folder);
      } else {
        const instance = _ResourceInstanceMap[child.resourceType](child as NodeResource, newInstance);
        instanceMap.set(instance.instanceId, instance);

        // 建立反向索引
        if (instance.id) {
          idToInstanceIdMap.set(instance.id, instance.instanceId);
        }
        if (instance.templateId) {
          templateIdToInstanceIdMap.set(instance.templateId, instance.instanceId);
        }
      }
    });
  };

  for (const resource of resources) {
    if (resource.resourceType === 'FOLDER') {
      processFolder(resource as Folder);
    } else {
      const instance = _ResourceInstanceMap[resource.resourceType](resource as NodeResource, newInstance);
      instanceMap.set(instance.instanceId, instance);

      // 建立反向索引
      if (instance.id) {
        idToInstanceIdMap.set(instance.id, instance.instanceId);
      }
      if (instance.templateId) {
        templateIdToInstanceIdMap.set(instance.templateId, instance.instanceId);
      }
    }
  }

  return <T extends IBOInstance>(key?: string): T | null => {
    if (!key) {
      return null;
    }

    // Try to find by instanceId first
    if (instanceMap.has(key)) {
      return instanceMap.get(key) as T;
    }

    // Try to find by id
    if (idToInstanceIdMap.has(key)) {
      return instanceMap.get(idToInstanceIdMap.get(key) as string) as T;
    }

    // Try to find by templateId
    if (templateIdToInstanceIdMap.has(key)) {
      return instanceMap.get(templateIdToInstanceIdMap.get(key) as string) as T;
    }

    // Fallback: search through all instances
    for (const instance of instanceMap.values()) {
      if (instance.id === key || instance.templateId === key) {
        return instance as T;
      }
    }

    return null;
  };
}

// =============================== Field Processor Factories ===============================

/**
 * Create default field processor
 */
function createDefaultFieldProcessor(databaseId: string, field: DatabaseField): IBOProcessor<DatabaseField, FieldVO> {
  return {
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      setFieldId(getInstance)(databaseId, field);
      return field;
    },
    toVO: (opts: BORenderOpts) => {
      const { locale } = opts || {};
      const { data, success, error } = FieldVOSchema.safeParse({
        id: field.id,
        templateId: field.templateId,
        databaseId,
        name: iStringParse(field.name, locale),
        description: iStringParse(field.description, locale),
        type: field.type,
        property: field.property,
        primary: field.primary!,
        required: field.required,
        privilege: field.privilege,
      });

      if (!success) {
        throw new Error(`Field ${iStringParse(field.name, locale)} is not valid: ${error.message}`);
      }
      return data;
    },
  };
}

/**
 * Create link field processor
 */
function createLinkFieldProcessor(databaseId: string, field: DatabaseField): IBOProcessor<DatabaseField, FieldVO> {
  const defaultProcessor = createDefaultFieldProcessor(databaseId, field);
  return {
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      defaultProcessor.processRelatedInstances(getInstance);
      assert(field.type === 'LINK', 'Field type must be LINK');
      const { foreignDatabaseId } = setResourceId(getInstance)(field.property);
      if (foreignDatabaseId) {
        setFieldId(getInstance)(foreignDatabaseId, field.property);
      }
      return field;
    },
    toVO: defaultProcessor.toVO,
  };
}

/**
 * Create one-way link field processor
 */
function createOneWayLinkFieldProcessor(
  databaseId: string,
  field: DatabaseField,
): IBOProcessor<DatabaseField, FieldVO> {
  const defaultProcessor = createDefaultFieldProcessor(databaseId, field);
  return {
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      defaultProcessor.processRelatedInstances(getInstance);
      assert(field.type === 'ONE_WAY_LINK', 'Field type must be ONE_WAY_LINK');
      setResourceId(getInstance)(field.property);
      return field;
    },
    toVO: defaultProcessor.toVO,
  };
}

/**
 * Create lookup field processor
 */
function createLookupFieldProcessor(databaseId: string, field: DatabaseField): IBOProcessor<DatabaseField, FieldVO> {
  const defaultProcessor = createDefaultFieldProcessor(databaseId, field);
  return {
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      defaultProcessor.processRelatedInstances(getInstance);
      assert(field.type === 'LOOKUP', 'Field type must be LOOKUP');
      _.set(field, 'property.databaseId', databaseId);

      setFieldId(getInstance)(databaseId, field.property);
      const database = getInstance<IDatabaseInstance>(databaseId);
      const relatedLinkField = database?.findField(field.property.relatedLinkFieldId);

      if (relatedLinkField?.property) {
        const property = relatedLinkField.property as LinkFieldProperty;
        const foreignDatabase = getInstance<IDatabaseInstance>(
          property.foreignDatabaseId || property.foreignDatabaseTemplateId,
        );
        if (foreignDatabase) {
          setFieldId(getInstance)(foreignDatabase.instanceId, field.property);
        }
      }
      return field;
    },
    toVO: defaultProcessor.toVO,
  };
}

const _DatabaseFieldProcessorMap: Record<
  DatabaseFieldType,
  (databaseId: string, field: DatabaseField) => IBOProcessor<DatabaseField, FieldVO>
> = {
  // Basic field types
  SINGLE_TEXT: createDefaultFieldProcessor,
  LONG_TEXT: createDefaultFieldProcessor,
  URL: createDefaultFieldProcessor,
  PHONE: createDefaultFieldProcessor,
  EMAIL: createDefaultFieldProcessor,
  CHECKBOX: createDefaultFieldProcessor,
  DATETIME: createDefaultFieldProcessor,
  DATERANGE: createDefaultFieldProcessor,
  CREATED_TIME: createDefaultFieldProcessor,
  MODIFIED_TIME: createDefaultFieldProcessor,
  ATTACHMENT: createDefaultFieldProcessor,
  SINGLE_SELECT: createDefaultFieldProcessor,
  MULTI_SELECT: createDefaultFieldProcessor,
  NUMBER: createDefaultFieldProcessor,
  CURRENCY: createDefaultFieldProcessor,
  PERCENT: createDefaultFieldProcessor,
  RATING: createDefaultFieldProcessor,
  BUTTON: createDefaultFieldProcessor,
  MEMBER: createDefaultFieldProcessor,
  AUTO_NUMBER: createDefaultFieldProcessor,
  CREATED_BY: createDefaultFieldProcessor,
  MODIFIED_BY: createDefaultFieldProcessor,
  FORMULA: createDefaultFieldProcessor,
  VIDEO: createDefaultFieldProcessor,
  VOICE: createDefaultFieldProcessor,
  PHOTO: createDefaultFieldProcessor,
  API: createDefaultFieldProcessor,
  AI_PHOTO: createDefaultFieldProcessor,
  AI_TEXT: createDefaultFieldProcessor,
  AI_VIDEO: createDefaultFieldProcessor,
  AI_VOICE: createDefaultFieldProcessor,
  CUT_VIDEO: createDefaultFieldProcessor,
  JSON: createDefaultFieldProcessor,
  CASCADER: createDefaultFieldProcessor,
  WORK_DOC: createDefaultFieldProcessor,
  // Special field types
  LINK: createLinkFieldProcessor,
  ONE_WAY_LINK: createOneWayLinkFieldProcessor,
  LOOKUP: createLookupFieldProcessor,
};

// =============================== Record and View Processor Factories ===============================

/**
 * Create record processor
 */
function createRecordProcessor(databaseId: string, record: DatabaseRecord): IRecordProcessor {
  return {
    toVO: (opts: BORenderOpts) => (database: Database) => {
      assert(record.id, 'Record id is required. please use processRelatedInstances to set the id');
      const cells: Record<string, CellRenderVO> = {};

      for (const field of database.fields || []) {
        const fieldId = field.id!;
        let dataValue = record.data[fieldId];
        let value: CellValue = record.values?.[fieldId] || null;

        // Handle time-related fields
        if (
          field.type === 'CREATED_TIME' ||
          field.type === 'MODIFIED_TIME' ||
          field.type === 'DATETIME' ||
          field.type === 'DATERANGE'
        ) {
          const property = {
            ...field.property,
            timeZone:
              !field.property.timeZone || field.property.timeZone === 'AUTO' ? opts?.timeZone : field.property.timeZone,
          };

          if (field.type === 'CREATED_TIME' || field.type === 'MODIFIED_TIME') {
            dataValue = new Date().toISOString();
            value = formatDateTimeCellValue(dataValue, property);
          }
          if (field.type === 'DATETIME' && dataValue) {
            value = formatDateTimeCellValue(dataValue as string, property);
          }
          if (field.type === 'DATERANGE' && dataValue) {
            value = formatDateRangeCellValue(dataValue as string, property);
          }
        }

        // Handle select fields
        if (
          (field.type === 'SINGLE_SELECT' || field.type === 'MULTI_SELECT') &&
          dataValue &&
          Array.isArray(dataValue)
        ) {
          const { options } = field.property as SingleSelectFieldProperty | MultiSelectFieldProperty;
          if (options) {
            value = dataValue
              .map((optionId) => {
                const option = options.find((o) => o.id === optionId || o.templateId === optionId);
                return option?.name;
              })
              .filter((i) => i) as string[];
          }
        }

        if (!dataValue && !value) {
          continue;
        }

        cells[fieldId] = {
          id: fieldId,
          data: dataValue,
          value: value as CellValueVO,
        };
      }

      return {
        id: record.id!,
        databaseId,
        revision: 0,
        cells,
        commentCount: 0,
        groupCount: 0,
      };
    },

    processRelatedInstances: (getInstance: GetInstanceFn) => {
      setRecord(getInstance)(databaseId, record);
      return record;
    },
  };
}

/**
 * Create view processor
 */
function createViewProcessor(databaseId: string, view: View): IViewProcessor {
  return {
    toVO: (_opts: BORenderOpts) => {},

    processRelatedInstances: (getInstance: GetInstanceFn) => {
      setViewId(getInstance)(databaseId, view);
      if (view.extra) {
        setFieldId(getInstance)(databaseId, view.extra);
      }
      view.fields?.forEach((field) => {
        setFieldId(getInstance)(databaseId, field);
      });
      view.filters?.conditions?.forEach((condition) => {
        setFieldId(getInstance)(databaseId, condition);
      });
      view.sorts?.forEach((sort) => {
        setFieldId(getInstance)(databaseId, sort);
      });
      return view;
    },

    toSimpleVO: (opts: BORenderOpts) => {
      assert(view.id, 'View id is required. please use processRelatedInstances to set the id');
      return {
        id: view.id,
        name: iStringParse(view.name, opts?.locale),
        description: iStringParse(view.description, opts?.locale),
        type: view.type,
      };
    },

    getColumns: (opts: BORenderOpts) => (database: Database) => {
      if (!view.fields) {
        return database.fields?.map((f) => _DatabaseFieldProcessorMap[f.type](databaseId, f).toVO(opts)) || [];
      }

      const columns: ViewFieldVO[] = [];
      const fieldMap = _.keyBy(database.fields, 'id');

      // Add view fields
      for (const f of view.fields || []) {
        assert(f.id, 'view field id is required, please use processRelatedInstances to set the id');
        const field = fieldMap[f.id];
        if (field) {
          columns.push({
            ..._DatabaseFieldProcessorMap[field.type](databaseId, field).toVO(opts),
            hidden: f.hidden,
          });
        }
      }

      // Add remaining database fields
      const columnsMap = _.keyBy(columns, 'id');
      for (const field of database.fields || []) {
        assert(field.id, 'database field id is required, please use processRelatedInstances to set the id');
        if (!columnsMap[field.id]) {
          columns.push({
            ..._DatabaseFieldProcessorMap[field.type](databaseId, field).toVO(opts),
            hidden: false,
          });
        }
      }

      return columns;
    },
  };
}

// =============================== Resource Processor Factories ===============================

/**
 * Create default resource processor
 */
function createDefaultResourceProcessor(resource: NodeResource): IBOProcessor<NodeResource, ResourceVO> {
  return {
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      setResourceInstanceId(getInstance)(resource);
      return resource;
    },
    toVO: (opts: BORenderOpts): ResourceVO => {
      const { locale } = opts || {};
      assert(resource.id, 'Resource id is required. please use processRelatedInstances to set the id');
      return {
        id: resource.id,
        name: iStringParse(resource.name, locale) || '',
        type: resource.resourceType,
      } as ResourceVO;
    },
  };
}

/**
 * Create database processor
 */
function createDatabaseProcessor(database: NodeResource): IDatabaseProcessor {
  const { fields, views, records } = database as Database;

  const getRecords = (opts: BORenderOpts): RecordPaginationVO => ({
    total: records?.length || 0,
    rows: records?.map((record) => createRecordProcessor(database.id!, record).toVO(opts)(database as Database)) || [],
  });

  const getPreviewId = (viewId: string) => {
    const previewViewIndex = views?.findIndex((v: View) => v.id === viewId) || -1;
    if (previewViewIndex === -1) {
      return null;
    }
    return views?.[previewViewIndex + 1]?.id || null;
  };

  return {
    getRecords,

    processRelatedInstances: (getInstance: GetInstanceFn) => {
      const { id: databaseId } = setResourceInstanceId(getInstance)(database);
      fields?.forEach((field) => {
        _DatabaseFieldProcessorMap[field.type](databaseId, field).processRelatedInstances(getInstance);
      });
      views?.forEach((view) => {
        createViewProcessor(databaseId, view).processRelatedInstances(getInstance);
      });
      records?.forEach((record) => {
        createRecordProcessor(databaseId, record).processRelatedInstances(getInstance);
      });
      return database as Database;
    },

    toSimpleVO: (opts: BORenderOpts) => {
      const { locale } = opts || {};
      return BaseDatabaseVOSchema.parse({
        id: database.id,
        name: iStringParse(database.name, locale),
        description: iStringParse(database.description, locale),
        spaceId: '',
      });
    },

    toVO: (opts: BORenderOpts): DatabaseVO => {
      const { locale } = opts || {};
      return {
        id: database.id!,
        name: iStringParse(database.name, locale),
        description: iStringParse(database.description, locale),
        spaceId: '',
        views:
          views?.map((view, index) => {
            const viewProcessor = createViewProcessor(database.id!, view);
            return {
              ...viewProcessor.toSimpleVO(opts),
              databaseId: database.id!,
              previewId: getPreviewId(view.id!),
              columns: viewProcessor
                .getColumns(opts)(database as Database)
                .filter((c) => !c.hidden),
              records: index === 0 ? getRecords(opts) : [],
            };
          }) || [],
      };
    },
  };
}

/**
 * Create automation processor
 */
function createAutomationProcessor(automation: NodeResource): IAutomationProcessor {
  const { actions, triggers } = automation as Automation;

  return {
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      const { id: automationId } = setResourceInstanceId(getInstance)(automation);
      const resolveDatabaseIds: string[] = [];

      const processAction = (action: Action) => {
        setActionId(getInstance)(automationId, action);
        const { databaseId } = setAutomationInput(automationId, getInstance)(action.input);
        if (databaseId) {
          resolveDatabaseIds.push(databaseId);
        }
        setAutomationInputVariables(automationId, getInstance, resolveDatabaseIds)(action.input);
      };

      actions?.forEach((action) => {
        processAction(action);
        // loop action
        if ('actions' in action) {
          action.actions?.forEach((subAction) => {
            processAction(subAction);
          });
        }
      });

      triggers?.forEach((trigger) => {
        setTriggerId(getInstance)(automationId, trigger);
        setAutomationInput(automationId, getInstance)(trigger.input);
      });
      return automation as Automation;
    },

    toVO: (opts: BORenderOpts): AutomationVO => {
      const { locale } = opts || {};
      assert(automation.id, 'Automation id is required. please use processRelatedInstances to set the id');
      return {
        id: automation.id,
        name: iStringParse(automation.name, locale),
        description: iStringParse(automation.description, locale),
        isActive: false,
        isVerified: false,
        actions:
          actions?.map((action) => ({
            id: action.id!,
            isVerified: false,
            description: iStringParse(action.description, locale),
            type: action.actionType,
            bo: action,
          })) || [],
        triggers:
          triggers?.map((trigger) => ({
            id: trigger.id!,
            isVerified: false,
            description: iStringParse(trigger.description, locale),
            type: trigger.triggerType,
            bo: trigger,
          })) || [],
      };
    },
  };
}

/**
 * Create form processor
 */
function createFormProcessor(form: NodeResource & { database?: Database }): IFormProcessor {
  return {
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      setResourceInstanceId(getInstance)(form);
      const { formType } = form as FormBo;
      if (formType === 'DATABASE' || !formType) {
        const { databaseId } = setResourceId(getInstance)(form as DatabaseFormBO);
        if (databaseId) {
          const { metadata } = form as DatabaseFormBO;
          if (metadata?.type === 'VIEW') {
            setViewId(getInstance)(databaseId, metadata);
          }
          if (metadata?.type === 'FIELD') {
            const { fields } = metadata;
            fields?.forEach((field) => {
              setFieldId(getInstance)(databaseId, field);
            });
          }
          const database = getInstance<IDatabaseInstance>(databaseId);
          _.set(form, 'database', database?.bo);
        }
      }
      if (form.resourceType === 'AUTOMATION') {
        throw new Error('Automation form is not supported');
      }
      return form as FormBo;
    },
    toVO: (opts: BORenderOpts): FormVO => {
      const { locale } = opts || {};
      assert(form.id, 'Form id is required. please use processRelatedInstances to set the id');
      const { database, metadata, icon, cover, brandLogo } = form as DatabaseFormBO & { database?: Database };
      const databaseProcessor = database ? createDatabaseProcessor(database) : undefined;

      const { views, fields, id: databaseId } = database || {};
      const getFields = (): ViewFieldVO[] => {
        if (metadata?.type === 'FIELD' && metadata.fields && databaseId) {
          const fieldMap = _.keyBy(fields, 'id');
          return metadata.fields?.reduce((acc, { fieldId }) => {
            const field = fieldMap[fieldId || ''];
            if (field) {
              const fieldProcessor = _DatabaseFieldProcessorMap[field.type](databaseId, field);
              acc.push(fieldProcessor.toVO(opts));
            }
            return acc;
          }, [] as ViewFieldVO[]);
        }
        if (metadata?.type === 'VIEW' && databaseId) {
          const view = views?.find((v) => v.id === metadata.viewId);

          if (view) {
            const viewProcessor = createViewProcessor(databaseId, view);
            return viewProcessor
              .getColumns(opts)(database as Database)
              .filter((c) => !c.hidden);
          }
        }
        return [];
      };

      const getView = (): ViewSimpleVO => {
        if (metadata?.type === 'VIEW' && databaseId) {
          const view = views?.find((v) => v.id === metadata.viewId);
          if (view) {
            const viewProcessor = createViewProcessor(databaseId, view);
            return viewProcessor.toSimpleVO(opts);
          }
        }
        return {} as ViewSimpleVO;
      };

      const getDatabase = (): BaseDatabaseVO => databaseProcessor?.toSimpleVO(opts) || ({} as BaseDatabaseVO);

      return {
        id: form.id,
        name: iStringParse(form.name, locale),
        description: iStringParse(form.description, locale),
        metadata: metadata as FormMetadata,
        icon: icon as AvatarLogo,
        cover: cover as AvatarLogo,
        brandLogo: brandLogo as AvatarLogo,
        databaseId: database?.id || '',
        database: getDatabase(),
        fields: getFields(),
        view: getView(),
      };
    },
  };
}

const _WidgetProcessorMap: Record<
  WidgetBO['type'],
  (dashboardId: string, widget: WidgetBO & { _value?: DatasourceRenderVO }) => IWidgetProcessor
> = {
  CHART: (_dashboardId, widget) => ({
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      const { datasource } = widget as ChartWidgetBO;
      if (datasource.type === 'DATABASE') {
        const { databaseId } = setResourceId(getInstance)(datasource);
        if (databaseId) {
          setViewId(getInstance)(databaseId, datasource);
          // dimension
          setFieldId(getInstance)(databaseId, datasource);
          if (datasource.metrics) {
            // metrics
            setFieldId(getInstance)(databaseId, datasource.metrics);
          }

          // datasource render value
          setWidgetValue(getInstance)(widget);
        }
      }
      return widget;
    },
    toVO: (opts: BORenderOpts): ChartWidgetVO => {
      const { locale } = opts || {};
      assert(widget.id, 'Widget id is required. please use processRelatedInstances to set the id');
      return {
        id: widget.id,
        name: iStringParse(widget.name, locale),
        description: iStringParse(widget.description, locale),
        type: 'CHART',
        datasource: (widget as ChartWidgetBO).datasource,
        settings: (widget as ChartWidgetBO).settings,
        value: widget._value as EChartRenderVO,
      };
    },
  }),
  NUMBER: (_dashboardId, widget) => ({
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      const { datasource } = widget as NumberWidgetBO;
      if (datasource.type === 'DATABASE') {
        const { databaseId } = setResourceId(getInstance)(datasource);
        if (databaseId) {
          if (databaseId) {
            setViewId(getInstance)(databaseId, datasource);
            if (datasource.metrics) {
              // metrics
              setFieldId(getInstance)(databaseId, datasource.metrics);
            }
            // datasource render value
            setWidgetValue(getInstance)(widget);
          }
        }
      }
      return widget;
    },
    toVO: (opts: BORenderOpts): NumberWidgetVO => {
      const { locale } = opts || {};
      assert(widget.id, 'Widget id is required. please use processRelatedInstances to set the id');
      return {
        id: widget.id,
        name: iStringParse(widget.name, locale),
        description: iStringParse(widget.description, locale),
        type: 'NUMBER',
        datasource: (widget as NumberWidgetBO).datasource,
        value: widget._value as NumberWidgetRenderVO,
      };
    },
  }),
  TEXT: (_dashboardId, widget) => ({
    processRelatedInstances: (_getInstance: GetInstanceFn) => widget,
    toVO: (opts: BORenderOpts): TextWidgetVO => {
      const { locale } = opts || {};
      assert(widget.id, 'Widget id is required. please use processRelatedInstances to set the id');
      return {
        id: widget.id,
        name: iStringParse(widget.name, locale),
        description: iStringParse(widget.description, locale),
        type: 'TEXT',
        datasource: (widget as TextWidget).datasource,
      };
    },
  }),
  PIVOT_TABLE: (_dashboardId, widget) => ({
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      const { datasource } = widget as PivotTableWidgetBO;
      if (datasource.type === 'DATABASE') {
        const { databaseId } = setResourceId(getInstance)(datasource);
        if (databaseId) {
          setViewId(getInstance)(databaseId, datasource);
          const { rows, columns, values } = datasource.fields;
          rows.forEach((row) => {
            setFieldId(getInstance)(databaseId, row);
          });
          columns.forEach((column) => {
            setFieldId(getInstance)(databaseId, column);
          });
          values.forEach((value) => {
            if (value !== 'COUNT_RECORDS') {
              setFieldId(getInstance)(databaseId, value);
            }
          });
        }
      }
      return widget;
    },
    toVO: (opts: BORenderOpts): PivotTableWidgetVO => {
      const { locale } = opts || {};
      assert(widget.id, 'Widget id is required. please use processRelatedInstances to set the id');
      return {
        id: widget.id,
        name: iStringParse(widget.name, locale),
        type: 'PIVOT_TABLE',
        description: iStringParse(widget.description, locale),
        datasource: (widget as PivotTableWidgetBO).datasource,
      };
    },
  }),
  EMBED: (_dashboardId, widget) => ({
    processRelatedInstances: (_getInstance: GetInstanceFn) => widget,
    toVO: (opts: BORenderOpts): EmbedWidgetVO => {
      const { locale } = opts || {};
      assert(widget.id, 'Widget id is required. please use processRelatedInstances to set the id');
      return {
        id: widget.id,
        name: iStringParse(widget.name, locale),
        type: 'EMBED',
        description: iStringParse(widget.description, locale),
        url: (widget as EmbedWidgetBO).url,
      };
    },
  }),
};

/**
 * Create dashboard processor
 */
function createDashboardProcessor(dashboard: NodeResource): IDashboardProcessor {
  return {
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      const { id: dashboardId } = setResourceInstanceId(getInstance)(dashboard);
      (dashboard as Dashboard).widgets?.forEach((widget) => {
        setWidgetId(getInstance)(dashboardId, widget);
        _WidgetProcessorMap[widget.type](dashboardId, widget).processRelatedInstances(getInstance);
      });
      return dashboard as Dashboard;
    },

    toVO: (opts: BORenderOpts): DashboardVO => {
      const { locale } = opts || {};
      const { widgets, name, id, description, templateId } = dashboard as Dashboard;
      assert(id, 'Dashboard id is required. please use processRelatedInstances to set the id');
      return {
        id,
        name: iStringParse(name, locale),
        description: iStringParse(description, locale),
        templateId,
        widgets: widgets?.map((widget) => _WidgetProcessorMap[widget.type](id, widget).toVO(opts)),
      };
    },
  };
}

function createMirrorProcessor(resource: NodeResource & { database?: Database }): IMirrorProcessor {
  return {
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      setResourceInstanceId(getInstance)(resource);
      const { mirrorType } = resource as MirrorBO;
      if (mirrorType === 'NODE_RESOURCE') {
        setResourceId(getInstance)(resource as NodeResourceMirrorBO);
      }
      if (mirrorType === 'DATABASE_VIEW') {
        const { databaseId } = setResourceId(getInstance)(resource as DatabaseViewMirrorBO);
        if (databaseId) {
          setViewId(getInstance)(databaseId, resource);
        }
        const database = getInstance<IDatabaseInstance>(databaseId);
        _.set(resource, 'database', database?.bo);
      }
      if (mirrorType === 'VIEW') {
        const { databaseId } = setResourceId(getInstance)(resource as ViewMirrorBO);
        if (databaseId) {
          const { view } = resource as ViewMirrorBO;
          view.fields?.forEach((field) => {
            setFieldId(getInstance)(databaseId, field);
          });
        }
        const database = getInstance<IDatabaseInstance>(databaseId);
        _.set(resource, 'database', database?.bo);
      }
      return resource as MirrorBO & { database?: Database };
    },
    toVO: (opts: BORenderOpts): MirrorVO => {
      const { locale } = opts || {};
      const { mirrorType, id } = resource as MirrorBO;
      assert(id, 'Mirror id is required. please use processRelatedInstances to set the id');
      const database = _.get(resource, 'database') as Database;
      const databaseVO = database ? createDatabaseProcessor(database).toSimpleVO(opts) : ({} as BaseDatabaseVO);

      const getViewVO = (): ViewVO => {
        const view = database?.views?.find((v) => v.id === _.get(resource, 'viewId'));
        const viewProcessor = view && database ? createViewProcessor(database.id!, view) : undefined;
        if (viewProcessor) {
          return {
            ...viewProcessor.toSimpleVO(opts),
            columns: viewProcessor.getColumns(opts)(database as Database),
          } as ViewVO;
        }
        return {} as ViewVO;
      };
      const viewVO = getViewVO();

      return {
        id,
        spaceId: databaseVO.spaceId,
        name: iStringParse(resource.name, locale),
        databaseId: databaseVO.id,
        database: databaseVO,
        viewId: viewVO.id,
        view: viewVO,
        mirrorType,
      };
    },
  };
}

const _ResourceProcessorMap: Record<
  Exclude<NodeResourceType, 'FOLDER'>,
  (resource: NodeResource) => IBOProcessor<NodeResource, ResourceVO>
> = {
  DATABASE: createDatabaseProcessor,
  AUTOMATION: createAutomationProcessor,
  DASHBOARD: createDashboardProcessor,
  FORM: createFormProcessor,
  MIRROR: createMirrorProcessor,
  // default resource processor
  // VIEW: createDefaultResourceProcessor,
  DATAPAGE: createDefaultResourceProcessor,
  CANVAS: createDefaultResourceProcessor,
  EMBED: createDefaultResourceProcessor,
  ALIAS: createDefaultResourceProcessor,
  DOCUMENT: createDefaultResourceProcessor,
  FILE: createDefaultResourceProcessor,
  REPORT_TEMPLATE: createDefaultResourceProcessor,
  AI: createDefaultResourceProcessor,
  ROOT: createDefaultResourceProcessor,
  TEMPLATE: createDefaultResourceProcessor,
  PAGE: createDefaultResourceProcessor,
};

/**
 * Create folder processor
 */
function createFolderProcessor(folder: Folder): IFolderProcessor {
  const processFolderRelatedInstances = (subFolder: Folder, getInstance: GetInstanceFn) => {
    setResourceInstanceId(getInstance)(subFolder);
    folder.children?.forEach((child) => {
      if (child.resourceType === 'FOLDER') {
        processFolderRelatedInstances(child as Folder, getInstance);
      } else {
        const processor = _ResourceProcessorMap[child.resourceType](child);
        processor.processRelatedInstances(getInstance);
      }
    });
  };

  const createTreeVO = (resource: NodeResource, opts: BORenderOpts): NodeTreeVO => {
    const { id, name, resourceType, description, templateId, icon } = resource;
    const { locale } = opts || {};
    assert(id, 'Resource id is required, please use processRelatedInstances to set the id');
    return {
      id,
      type: resourceType,
      name: iStringParse(name, locale),
      description: iStringParse(description, locale),
      sharing: false,
      hasShareLock: false,
      hasPermissions: false,
      templateId,
      icon,
    };
  };

  const processFolderToVO = (subFolder: Folder, opts: BORenderOpts): FolderDetailVO => {
    const { children, readme } = subFolder;
    const vo: FolderDetailVO = {
      ...createTreeVO(subFolder, opts),
      scope: 'SPACE',
      readme,
      children: [],
    };

    for (const child of children || []) {
      if (child.resourceType === 'FOLDER') {
        vo.children!.push(processFolderToVO(child as Folder, opts) as NodeDetailVO);
      } else {
        const resourceVO: ResourceVO = _ResourceProcessorMap[child.resourceType](child).toVO(opts);
        vo.children!.push({
          ...createTreeVO(child, opts),
          scope: 'SPACE',
          resource: resourceVO,
        });
      }
    }
    return vo;
  };

  return {
    processRelatedInstances: (getInstance: GetInstanceFn) => {
      processFolderRelatedInstances(folder, getInstance);
      return folder;
    },
    toVO: (opts: BORenderOpts) => processFolderToVO(folder, opts),
  };
}

/**
 * Create resource processor factory
 */
function createResourceProcessor(resource: NodeResource): IBOProcessor<NodeResource, ResourceVO> | IFolderProcessor {
  if (resource.resourceType === 'FOLDER') {
    return createFolderProcessor(resource as Folder);
  }
  return _ResourceProcessorMap[resource.resourceType](resource);
}

// =============================== Export ===============================

export { createResourceInstance, createResourceProcessor };
