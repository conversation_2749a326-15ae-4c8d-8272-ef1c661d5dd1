export interface ResponseVO<T> {
  success: boolean;
  code: number;
  message: string;
  data: T;
}

export interface IHttpClient {
  post<T, BODY = unknown>(url: string, body?: BODY): Promise<ResponseVO<T>>;
  put<T, BODY = unknown>(url: string, body?: BODY): Promise<ResponseVO<T>>;
  patch<T, BODY = unknown>(url: string, body?: BODY): Promise<ResponseVO<T>>;
  delete<T>(url: string): Promise<ResponseVO<T>>;
  get<T>(url: string): Promise<ResponseVO<T>>;
}

export interface IToolSDKAIArgs {
  apiKey: string;
  baseURL?: string;
}

export interface IToolSDKAccountApiClientArgs {
  accountToken: string;
  baseURL?: string;
}

export type ICreateApiClient = IToolSDKAIArgs | IToolSDKAccountApiClientArgs | IHttpClient;

export interface IConsumerKeyParams {
  consumerKey: string;
}
export type IAccountAnyActionInstanceParams = IConsumerKeyParams | { instanceId: string };
export type IAccountActionInstanceParams = {
  actionKey: string;
} & IAccountAnyActionInstanceParams;

export interface ToolParameters {
  type: 'object';
  properties: Record<string, unknown>;
  required: string[];
  additionalProperties: boolean;
}

export interface OpenAITool {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: ToolParameters;
  };
}
