import { Account } from './account';
import { DEFAULT_BASE_URL } from './const';
import { Developer } from './developer';
import { HttpClient } from './http';
import { Configuration } from './package/configuration';
import { ConfigurationInstance } from './package/configuration-instance';
import { Package } from './package/package';
import { PackageInstance } from './package/package-instance';
import { IToolSDKAIArgs, IHttpClient } from './types';
import { PackagePaginationQuery } from '../types/dto';

/**
 * ToolSDKAI.
 *
 * Use api key to authenticate.
 */
export class ToolSDKApiClient {
  private _httpClient: IHttpClient;

  constructor(opts: IToolSDKAIArgs | IHttpClient) {
    if ('apiKey' in opts) {
      const { apiKey, baseURL } = opts;
      this._httpClient = new HttpClient(baseURL || DEFAULT_BASE_URL, apiKey, true);
      return;
    }
    this._httpClient = opts;
  }

  get developer() {
    return new Developer(this._httpClient);
  }

  account(accountKey: string) {
    return new Account(this._httpClient, accountKey);
  }

  package(packageKey: string, versionOrEnvs?: string | Record<string, string>, envs?: Record<string, string>): Package {
    if (versionOrEnvs && typeof versionOrEnvs === 'object') {
      return new Package(this._httpClient, packageKey, undefined, versionOrEnvs);
    }

    return new Package(this._httpClient, packageKey, versionOrEnvs, envs);
  }

  get packages() {
    return {
      pages: async (params?: PackagePaginationQuery) => Package.pages(this._httpClient, params),
      get: (key: string, version?: string, envs?: Record<string, string>) => this.package(key, version, envs).info(),
      my: () => this.developer.myPackages(),
    };
  }

  packageInstance(instanceId: string) {
    return new PackageInstance(this._httpClient, instanceId);
  }

  configuration(packageKey: string, packageVersion?: string) {
    return new Configuration(this._httpClient, packageKey, packageVersion);
  }

  configurationInstance(instanceId: string) {
    return new ConfigurationInstance(this._httpClient, instanceId);
  }
}
