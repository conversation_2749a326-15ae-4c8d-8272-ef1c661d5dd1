// import { updateToolCallResult } from '@ai-sdk/ui-utils';
import type { DataStreamWriter } from 'ai';
// import { Locale } from '@bika/contents/i18n/config';
import _ from 'lodash';
import { generateNanoID } from 'sharelib/nano-id';
import { DataStreamUtils } from '@bika/domains/ai/server/ai-chat/data-stream-utils';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { ResolutionResultInfo } from '@bika/domains/ai/server/types';
// import { SpaceSO } from '@bika/domains/space/server/space-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import {
  BuildAppIntentParams,
  AIIntentParams,
  type AIMessageBO,
  // type AIMessagePartSource,
  // BikaWorkflowAIToolSchema,
} from '@bika/types/ai/bo';
import { ResolutionMessage, AIResolveVO, AIMessageVO } from '@bika/types/ai/vo';
import { iStringParse } from '@bika/types/system';
import { AIIntentResolver } from './resolvers';
import { AIModelOptions } from '../ai/server/types';

export class BuildAppAIIntentResolver extends AIIntentResolver<BuildAppIntentParams> {
  async onInit(): Promise<boolean> {
    return true;
  }

  async getPrologue(): Promise<ResolutionMessage | undefined> {
    return undefined;
    // return {
    //   parts: [
    //     {
    //       type: 'text',
    //       text: 'Hi Hi, I am your Business AI Agents assistant. I can help you to create a new Business AI Agent. What jobs would you like to be done?',
    //     },
    //   ],
    //   text: {
    //     en: 'Hi Hi, I am your Business AI Agents assistant. I can help you to create a new Business AI Agent. What jobs would you like to be done?',
    //     'zh-CN': '你好你好，我是你的企业AI智能体助手。我可以帮你创建一个新的企业AI智能体。你想要做什么工作？',
    //     ja: 'こんにちはこんにちは、私はあなたのビジネスAIエージェントアシスタントです。新しいビジネスAIエージェントを作成するのを手伝います。何をしたいですか？',
    //     'zh-TW': '你好你好，我是你的商業AI智能體助手。我可以幫你創建一個新的商業AI智能體。你想要做什麼工作？',
    //   },
    //   skillsets: ['ai-consulting'],
    //   prompts: this.getPrompts('en'),
    // };
  }

  /**
   * 填充资源，
   * search模式，找到最近似的，取第一个
   *
   * AI模式，AI schema + 近似的参考，喂给AI
   *
   * @param messageText
   * @param mode
   */
  // private async fillInResources(messageText: string, mode: 'SEARCH' | 'AI', user: UserSO) {
  //   if (isInUnitTest() || mode === 'SEARCH') {
  //     // 搜索，找到最近似的
  //     const searchTemplates = await StoreTemplateSO.searchTemplatesWithESAdvanced(messageText);
  //     if (searchTemplates.length > 0) {
  //       const storeTemplates = await Promise.all(
  //         searchTemplates.map((templatePO) => StoreTemplateSO.initWithModel(templatePO)),
  //       );
  //       // 找到了，返回给用户确认
  //       const storeTemplateSO = storeTemplates[0]; // 只返回第一个
  //       const customTemplate = await storeTemplateSO.getTemplate();
  //       this.intentParams.name = customTemplate.name;
  //       this.intentParams.description = customTemplate.description;
  //       this.intentParams.resources = customTemplate.resources;

  //       const sources: AISource[] = storeTemplates.map((storeTemplate) => ({
  //         id: storeTemplate.templateId,
  //         sourceType: 'url',
  //         url: `/template/${storeTemplate.templateId}`,
  //         title: iStringParse(storeTemplate.name as iString, user.locale),
  //       }));

  //       this.intentParams.sources = sources;
  //     } else {
  //       // 找不到相似的，使用默认模板
  //       console.warn(`No similar template found, use default template: ${messageText}`);

  //       this.intentParams.name = ExampleTemplate.name;
  //       this.intentParams.description = ExampleTemplate.description;
  //       this.intentParams.resources = ExampleTemplate.resources;
  //     }
  //   } else {
  //     assert(mode === 'AI');
  //     assert(this.intentParams.prompt);

  //     // AI模式，AI schema + 近似的参考，喂给AI
  //     const result = await AIAppsBuilderSO.build(this.intentParams.prompt, undefined, undefined);

  //     if (result.aiApp) {
  //       this.intentParams.name = result.aiApp.name;
  //       this.intentParams.description = result.aiApp.description;
  //       this.intentParams.resources = result.aiApp.resources;
  //     } else {
  //       console.error(`AI generate template failed, use default template: ${messageText}`);
  //       this.intentParams.name = ExampleTemplate.name;
  //       this.intentParams.description = ExampleTemplate.description;
  //       this.intentParams.resources = ExampleTemplate.resources;
  //     }
  //   }
  // }

  /**
   *  老的方法了， 手工构造了 AIMessage，会被 纯 AI 版本替代
   *
   * @param userInfo
   * @returns
   */
  // private buildAppDeprecated(userInfo: { userId: string; locale: Locale }): ResolutionResultInfo {
  //   const sourceParts: AIMessagePartSource[] = this.intentParams!.sources!.map((source) => ({
  //     type: 'source',
  //     source: {
  //       id: source.id,
  //       sourceType: source.sourceType,
  //       url: source.url,
  //       title: iStringParse(source.title, userInfo.locale),
  //     },
  //   }));
  //   const finalConfirm: ResolutionResultInfo = {
  //     intentParam: this.intentParams,
  //     status: 'NEEDS_CONFIRMATION',
  //     usage: {
  //       model: undefined,
  //       promptTokens: 0,
  //       completionTokens: 0,
  //       totalTokens: 0,
  //       costCredit: 0,
  //     },
  //     message: {
  //       parts: [
  //         ...sourceParts,
  //         {
  //           type: 'reasoning',
  //           reasoning: iStringParse({
  //             en: `According to your demand, I am thinking... I think the name of you workflow can be ${iStringParse(this.intentParams.name, userInfo.locale)}. ${iStringParse(this.intentParams.description, userInfo.locale)}`,
  //             ja: `あなたの要求に基づいて、私は考えています... 私はあなたのワークフローの名前を${iStringParse(this.intentParams.name, userInfo.locale)}と考えています。${iStringParse(this.intentParams.description, userInfo.locale)}`,
  //             'zh-CN': `根据你的需求，我在思考... 我认为你的工作流程的名字可以是${iStringParse(this.intentParams.name, userInfo.locale)}。${iStringParse(this.intentParams.description, userInfo.locale)}`,
  //             'zh-TW': `根據你的需求，我在思考... 我認為你的工作流程的名字可以是${iStringParse(this.intentParams.name, userInfo.locale)}。${iStringParse(this.intentParams.description, userInfo.locale)}`,
  //           }),
  //           details: [],
  //         },
  //         {
  //           type: 'text',
  //           text: 'Take a look! Here is the AI automation app workflow you want to create, do you want to create it?',
  //         },
  //         {
  //           type: 'tool-invocation',
  //           toolInvocation: {
  //             state: 'result',
  //             toolCallId: '1',
  //             toolName: 'ai-consulting-deprecated',
  //             args: {
  //               consultingType: 'consultant',
  //               content: `
  // > 我很早就有一个意识：一个公司可以没有市场部、没有行政部，甚至可以没有财务部，但绝对不能没有销售部。
  // 为什么？因为我们是商业公司啊，商业公司就是以盈利为目的的，而盈利的核心就是销售。没有销售，哪来的收入？没有收入，公司怎么生存？所以，销售是每个公司的命脉，客户是每个公司的根基。

  // ## 客户管理

  // 说到客户管理，这是每个公司的基本功，也是基本盘。无论公司规模大小，都离不开这一点。就连老板，也得是公司的头号销售。那么，我们怎么才能做好客户管理呢？首先，得做好两件事：

  // - 第一，建立客户名录，也就是客户库，把客户的基本信息、联系方式、需求痛点都记录清楚；
  // - 第二，做好客户跟进，包括拜访记录、沟通内容、下一步计划等等。如果公司不止一个人负责客户，那还得做好任务分配和协同，避免撞车或者遗漏。

  // 现在，AI技术这么火，我们能不能借助AI的力量，让客户管理更高效、更智能呢？比如说：

  // - 用AI帮我们沉淀客户数据，把散落在各个角落的客户信息都集中起来，形成完整的客户画像；
  // - 用AI提醒我们该做什么，比如该给哪个客户发邮件了，该跟进哪个潜在客户了，甚至还能给出沟通建议；
  // - 用AI保护客户资源，这是公司最核心的资产，必须确保安全，同时又能让销售团队高效协作，共同把蛋糕做大。

  // ## 销售规模扩大

  // 毫无疑问，当然可以。接下来，我们就来看看这样一个能够帮助企业实实在在赚钱的企业AI智能体要怎么打造。`,
  //             },
  //             result: {},
  //           },
  //         },
  //         {
  //           type: 'tool-invocation',
  //           toolInvocation: {
  //             state: 'result',
  //             toolCallId: '2',
  //             toolName: 'ai-consulting-deprecated',
  //             args: {
  //               consultingType: 'business-analyst',
  //               content: `
  // 2.1.1 需求分析

  // 那围绕客户管理的核心需求，我们可以设计一个企业AI智能体，让它成为销售团队的超级助手。这个智能体大致上需要具备以下功能和能力：

  // 1. 客户沉淀：让数据“活”起来

  // - 数据整合与清洗：AI智能体可以从市场活动、线上平台、线下销售等多个渠道自动抓取客户信息，并对这些数据进行清洗和整合，确保客户名录和客户库中的数据准确、完整、格式统一。
  // - 客户分类与标签：根据客户的行业、规模、地域等属性，以及购买频率、互动历史等行为，AI智能体可以自动为客户打标签，帮助企业更精准地了解客户群体，为后续的营销和服务提供依据。

  // 2. 辅助客户关系建立：让沟通更高效

  // - 沟通提醒与建议：在销售人员与客户沟通前，AI智能体可以根据客户的历史信息和当前状态，提供个性化的提醒和建议。比如，客户更喜欢邮件还是电话沟通？最近关注了哪些产品？针对不同场景，AI还能给出具体的话术建议，帮助销售人员更好地与客户建立联系。
  // - 客户反馈分析：AI智能体可以实时分析客户在沟通、交易过程中的反馈信息，包括邮件、电话、在线聊天记录等，提取关键信息和情绪倾向，及时反馈给销售人员，帮助他们调整沟通策略，巩固客户关系。

  // 3. 客户资源数据保护：让数据更安全

  // - 访问权限管理：AI智能体可以帮助企业建立完善的客户数据访问权限体系，根据员工的职位和职责，自动分配相应的数据访问级别，确保只有授权人员才能访问特定客户数据，防止数据泄露。
  // - 数据加密与备份：AI智能体会对客户数据进行加密存储，并定期自动备份，确保数据在传输和存储过程中的安全性。即使遇到意外情况，也能快速恢复数据，保障业务的连续性。

  // 4. 销售规模扩大：让团队更高效

  // 通过AI智能体的高效客户沉淀和关系管理，企业可以积累丰富的潜在客户资源，为销售拓展提供更多机会。同时，AI还能帮助销售人员提升沟通质量，提高客户满意度和忠诚度，促进复购和口碑传播。更重要的是，AI智能体可以协助企业制定科学的销售策略，推动销售规模的持续扩大。

  // 5. 销售报告汇总：让管理更清晰

  // AI智能体可以自动生成每周销售报告，汇总销售数据、客户反馈、任务完成情况等关键信息，帮助管理层快速了解销售进展，及时调整策略。`,
  //             },
  //             result: {},
  //           },
  //         },
  //         {
  //           type: 'tool-invocation',
  //           toolInvocation: {
  //             state: 'result',
  //             toolCallId: '3',
  //             toolName: 'ai-consulting-deprecated',
  //             args: {
  //               consultingType: 'designer',
  //               content: `
  // 2.1.2 智能体设计

  // 很好，既然我们已经根据需求列举好了B2B AI CRM智能体大概需要的功能，那么，接下来就进入具体的设计和搭建环节。

  // 1. 部件的架构设计

  // 好了，那基于以上的需求，我们首先想想用企业 AI 智能体的方法，它会用到哪一些部件。看着要实现的功能不少，但落地到具体的搭建中，你只需要以下几个资源组件：

  // - 多维表格-联系人：用于记录客户企业中联系人的详细信息，包括姓名、职位、部门、联系方式等。
  // - 多维表格-客户公司：用于存储客户企业的基本信息，如公司名称、规模、地址和线索来源。
  // - 多维表格-拜访记录：用于跟踪销售人员对客户企业的访问记录。
  // - 镜像-我的客户：每个业务人员通过这个镜像仅可看到自己的客户信息，作为数据隔离。
  // - 镜像-我的拜访记录：每个业务人员通过这个镜像仅可看到自己的拜访记录，作为数据隔离。
  // - 自动化流程-自动发送销售周报：每周自动汇总销售团队的拜访记录，生成详尽的销售周报，并精准发送给销售经理。
  // - 自动化流程-每日销售任务派发：每日自动根据客户资源、销售策略和团队成员的工作负载，合理派发拜访客户的任务给销售团队。
  // - 第三方集成-OpenAI：用于生成个性化的沟通建议、邮件内容以及销售报告分析。

  // 2. 多维表格-联系人

  // 流程中的第一步，你需要在多维表格中新建一张数据表，它将作为你的联系人列表而存在，你将在这里管理你所有的客户企业对接沟通人的数据。

  // 这张表格中的基本字段不能少于以下几个：

  // - 姓名：文本字段，联系人的姓名，可用于邮件正文中作为数据变量引用，让每封发出去的邮件都有一对一的对话感。
  // - 公司：关联字段，关联客户公司表格中的公司信息。
  // - 邮箱：电子邮箱字段，联系人的邮箱地址，在自动化流程中作为收件人邮箱地址变量数据引用。
  // - 电话号码：文本字段，联系人的电话号码。
  // - 部门：文本字段，联系人所属部门。
  // - 职位：文本字段，联系人的职位。
  // - 创建时间：日期字段，记录联系人信息的创建时间。
  // - 拜访记录：关联字段，关联拜访记录表格中的拜访信息。
  // - 销售负责人：引用字段，引用客户公司表格中的销售负责人信息。

  // 具体成形的表格效果示例，如图2-2所示。

  // 3. 多维表格-客户公司

  // 接下来，你需要在多维表格中新建另一张数据表，它将作为你的客户公司列表而存在。这张表格将与联系人表格和拜访记录表格关联，形成完整的客户管理体系。

  // 这张表格中的基本字段数不能少于以下几个：

  // - 公司名称：文本字段，客户公司的名称。
  // - 线索来源：文本字段，客户公司的线索来源。
  // - 描述：文本字段，客户公司的简要描述。
  // - 公司规模：文本字段，客户公司的规模。
  // - 公司所在地址：文本字段，客户公司的地址。
  // - 创建时间：日期字段，记录客户公司信息的创建时间。
  // - 联系人：关联字段，关联联系人表格中的联系人信息。
  // - 拜访记录：关联字段，关联拜访记录表格中的拜访信息。
  // - 销售负责人：引用字段，记录负责该客户的销售人员。

  // 具体成形的表格效果示例，如图2-3所示。

  // 4. 多维表格-拜访记录

  // 最后你还需要一张数据表，它将作为你的拜访记录列表而存在。这张表格将与客户公司表格和联系人表格关联，形成完整的客户拜访管理体系。

  // 这张表格中的基本字段数不能少于以下几个：

  // - 拜访记录：公式字段，组合拜访时间、公司和客户信息。
  // - 客户公司：关联字段，关联客户公司表格中的公司信息。
  // - 客户名称：关联字段，关联联系人表格中的客户信息。
  // - 内容摘要：文本字段，记录拜访的主要内容摘要。
  // - 跟进人：引用字段，引用客户公司表格中的销售负责人信息。
  // - 拜访时间：日期字段，记录拜访的具体时间。
  // - 创建人：文本字段，记录拜访记录的创建人。
  // - 创建时间：日期字段，记录拜访记录的创建时间。

  // 具体成形的表格效果示例，如图2-6所示。

  // 另外，你还可以新建一个表单视图，命名为本周拜访，设置筛选条件：拜访时间等于本周内。方便我们在后续的自动化流程中进行记录的批量查找。

  // 5. 镜像-我的客户

  // 在完成了数据表的录入与整理后，我们需要为每个销售人员生成专属的客户视图。通过新建一个基于客户公司表的镜像视图，设置当跟进人为当前用户时，就能生成该销售人员的客户视图。确保每个销售人员只能查看和管理自己的客户信息，保障数据的安全性和隐私性。

  // 6. 镜像-我的拜访记录

  // 同样地，我们需要为每个销售人员生成专属的拜访记录视图。通过新建一个基于拜访记录表的镜像视图，当跟进人为当前用户时，自动生成该销售人员的拜访记录视图，确保每个销售人员只能查看和管理自己的拜访记录，保障数据的安全性和隐私性。

  // 7. 第三方集成-OpenAI

  // 在搭建自动化流程之前，我们还需要准备好OpenAI的第三方集成，方便借用它的文本生成能力，帮助我们基于以上的数据智能生成销售周报。

  // OpenAI是一家领先的人工智能研究机构，提供先进的自然语言处理（NLP）和生成式AI模型，ChatGPT相信大家已经很熟悉了。其API服务可以帮助开发者快速集成强大的文本生成、对话、代码生成等功能到自己的应用中。OpenAI的模型具有以下特点：

  // - 多领域支持：适用于对话、内容创作、代码生成、翻译等多种场景。
  // - 高性能：基于大规模预训练模型（如GPT-4），生成内容质量高，语义理解能力强。
  // - 易用性：提供简洁的API接口，支持快速集成，适合开发者和企业使用。

  // 无论是构建智能客服、内容生成工具，还是代码助手，OpenAI都能提供强大的支持。

  // 那我们要如何集成OpenAI呢？以下是集成OpenAI API的详细步骤：

  // （1）获取API密钥

  // - 注册账号：前往OpenAI官网（https://openai.com）注册账号并登录。
  // - 创建API密钥：在开发者中心或API管理页面中，创建一个新的API密钥。
  // - 保存密钥：将生成的API密钥妥善保存，后续用于身份验证。

  // （2）在多维表格中集成 OpenAI

  // 这时，你可以直接返回多维表格平台中，找到它的第三方集成入口，开始搭建推特的集成。这里，我以Bika.ai为例，来展现这个步骤应该怎么做：

  // - 登录 Bika：使用你的 Bika 帐号登录，并进入目标空间站。
  // - 导航到集成页面：在界面左上方点击空间站名称，在下拉菜单中点击“设置”按钮，然后打开“第三方集成”标签页。
  // - 添加新集成：在集成列表中找到 “OpenAI” ，点击 “连接” 按钮打开配置界面。
  // - 按照配置页面的要求，依次给新集成命名或者备注，输入从OpenAI获取的 API秘钥后确认。在弹出的授权页面中，点击“Authorize app”就可以完成集成了。

  // 8. 自动化流程-自动发送销售周报

  // 前期的工作都做完了之后，我们也来到了关键的自动化流程搭建。首先，我们需要搭建一个自动发送销售周报的自动化流程，每周自动汇总销售团队的拜访记录，生成详尽的销售周报，并发送给销售经理，节省了大量的分析汇总时间。

  // （1） 触发器：定时触发

  // 触发器决定了自动化流程的执行时机。在这个场景里，我们选择定时触发器，每周在设定的时间（如周一早上10:00）自动启动流程，生成上周的销售周报，方便进行例会的复盘。

  // （2）执行器：查找成员

  // 然后在执行器的选择中，我们首先需要的是【查找成员】，通过它我们可以获取销售团队的成员名单。

  // （3）执行器：查找记录

  // 找到了销售团队的成员之后，我们就需要查找所有成员在本周内的拜访记录，这时候需要的执行器是【查找记录】，找到拜访记录表格的本周拜访视图即可。

  // （4）执行器：OpenAI-生成文本

  // 接下里，我们就可以将这些查找到的拜访记录丢给AI，让AI帮助我们分析这些数据，形成分析的报告。这个时候，你选择【OpenAI-生成文本】的执行器，选择我们在上面已经搭建好的第三方集成，或者手动填写OpenAI的API秘钥，选择适合的语言模型。

  // 然后在提示（prompt）里填写你希望AI如何利用你在前面的步骤中获得的数据，得出什么样的分析。这部分可以由你自己来把握。最主要的还是为了在prompt中利用到本周拜访记录和团队成员名单的信息。

  // （5）执行器：发送报告

  // 最后，将AI为你分析生成的报告文本直接或者二次丰富编辑后给指定的人员或者整个销售团队成员，这个自动化流程就完成了。在这里，我们利用的是【发送报告】的执行器，然后指定收件人、编写报告的主题、编辑报告正文。编辑报告正文里，你可以直接引用上一步生成的AI报告文本，或者再加上你的一些补充内容，比如附上这一周的拜访记录列表。

  // 至此，这个自动化流程就搭建完毕了。

  // 9. 自动化流程-每周销售任务排发

  // 接下来，我们需要搭建这个智能体的第二个自动化流程，每天自动派发拜访客户的任务给销售团队，保证每个成员的拜访任务量。这个就比较简单了，只需要一个触发器和一个执行器。

  // （1）触发器：定时触发

  // 触发器上，我们依然选择定时触发。你可以在每天早上开始上班时触发，比如早上9点，提醒刚到岗位的团队成员，一投入工作就有一个明确的目标。

  // （2）执行器：创建智能任务

  // 然后，在执行器上，我们选择【创建智能任务】，因为任务还分了很多不同的类型，比如创建记录、更新记录、提交表单等，让接受任务的人需要执行某个特定的动作才能算是任务完成。

  // 在这个自动化流程里，我们选择创建记录这个任务，让销售人员在拜访记录表上新增一条拜访记录才算是任务完成。然后，我们就可以在设置面板里填写任务名称、任务描述、分配人员，选择拜访记录表和对应的视图。

  // 这一切都配置好后，选择保存，这个自动化流程就搭建好了。这也意味着，这个企业AI智能体也搭建好了。`,
  //             },
  //             result: {},
  //           },
  //         },
  //         {
  //           type: 'tool-invocation',
  //           toolInvocation: {
  //             state: 'result',
  //             toolCallId: '4',
  //             toolName: 'ai-consulting-deprecated',
  //             args: {
  //               consultingType: 'engineer',
  //               // roleName: iStringParse('Devin - Engineer'),
  //               // roleState: iStringParse('外坑中...'),
  //               title: iStringParse(this.intentParams!.name!, userInfo.locale),
  //               resources: this.intentParams!.resources!,
  //             },
  //             result: {
  //               text: 'Here is Engineer Devin',
  //             },
  //           },
  //         },
  //         {
  //           type: 'tool-invocation',
  //           toolInvocation: {
  //             state: 'result',
  //             toolCallId: '4',
  //             toolName: 'ai-consulting-deprecated',
  //             args: {
  //               consultingType: 'marketer',
  //               content: 'This is HTML',
  //             },
  //             result: {},
  //           },
  //         },
  //         {
  //           type: 'tool-invocation',
  //           toolInvocation: {
  //             state: 'result',
  //             toolCallId: '4',
  //             toolName: 'form',
  //             args: {
  //               type: 'FLOW',
  //               title: this.intentParams!.name!,
  //               resources: this.intentParams!.resources!,
  //               hiddenInput: true,
  //             },
  //             result: {},
  //           },
  //         },
  //       ],
  //       // sources: this.intentParams!.sources,
  //       // reasoning: ,
  //       text: {
  //         en: 'Take a look! Here is the AI automation app workflow you want to create, do you want to create it?',
  //         'zh-CN': '看看！这是你想要创建的AI自动化应用工作流程，你想要创建它吗？',
  //         ja: '見て！これが作成したいAI自動化アプリケーションのワークフローです、作成しますか？',
  //         'zh-TW': '看看！這是你想要創建的AI自動化應用工作流程，你想要創建它嗎？',
  //       },
  //       skillsets: [{ kind: 'toolsdk', key: 'bika-app-builder' }],
  //       // tools: [

  //       // ],
  //     },
  //   };
  //   return finalConfirm;
  // }

  async doResolve(
    resolver: AIResolveVO,
    user: UserSO,
    chatHistories: AIMessageVO[],
    dataStreamWriter?: DataStreamWriter,
  ): Promise<ResolutionResultInfo> {
    // 这里判断用户的输入，由AI判断用户的输入是否满足条件，如果满足条件，则进入回复状态
    // 是否字符串过短？比如少于5个字符？提示让它长长一点？
    if (resolver.type === 'MESSAGE' && resolver.message.length < 5) {
      const text = {
        en: 'Your words are too simple, can you be more detailed?',
        'zh-CN': '你说的太简单了，能不能详细一点？',
        ja: 'あなたはあまりにも単純です、もっと詳しくしてもらえますか？',
        'zh-TW': '你說的太簡單了，能不能詳細一點？',
      };
      // const ui = this.getDefaultPromptsIntentUI();
      const msg: ResolutionMessage = {
        // role: 'assistant',
        parts: [
          {
            type: 'text',
            text: iStringParse(text, user.locale),
          },
        ],
        // text,
        // ui,
        skillsets: [{ kind: 'preset', key: 'bika-app-builder' }],
        prompts: this.getPrompts(user.locale),
      };
      if (dataStreamWriter) {
        DataStreamUtils.writeMessage(dataStreamWriter, msg, user.locale);
      }

      return {
        intentParam: this.intentParams,
        status: 'DIALOG',
        message: msg,
        usage: {
          model: undefined,
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: 0,
          costCredit: 0,
        },
      };
    }

    // if (this._intentSO.resolutionStatus === 'NEEDS_DISAMBIGUATION' && !this.intentParams.spaceId) {
    //   assert(resolver.type === 'TOOL');
    //   assert(resolver.toolInvocation.state === 'result');
    //   // assert(resolver.uiResolve.type === 'CHOICES');

    //   const userSpaces = await user.findSpaces();
    //   const selectIndex = (resolver.toolInvocation.result as { index: number }).index;
    //   const selectSpace = userSpaces[selectIndex];
    //   this.intentParams.spaceId = selectSpace.id;
    //   // 选择完空间站后，message 里，增加一个 tool result做标记，取出上一条 message，拿到 args 和 ID
    //   const lastFormMessage = chatHistories[chatHistories.length - 1];
    //   assert(
    //     lastFormMessage.role === 'assistant' &&
    //       lastFormMessage.parts[1].type === 'tool-invocation' &&
    //       lastFormMessage.parts[1].toolInvocation.toolName === 'form',
    //   );

    //   // updateToolCallResult({
    //   //   messages: chatHistories as UIMessage[],
    //   //   toolCallId: lastFormMessage.parts[1].toolInvocation.toolCallId,
    //   //   toolResult: {
    //   //     index: selectIndex,
    //   //   },
    //   // });

    //   // chatHistories.push({
    //   //   id: generateNanoID(),
    //   //   role: 'assistant',
    //   //   parts: [
    //   //     {
    //   //       type: 'tool-invocation',
    //   //       toolInvocation: {
    //   //         state: 'result',
    //   //         toolCallId: lastFormMessage.parts[1].toolInvocation.toolCallId,
    //   //         toolName: lastFormMessage.parts[1].toolInvocation.toolName,
    //   //         args: lastFormMessage.parts[1].toolInvocation.args,
    //   //         result: {
    //   //           index: selectIndex,
    //   //         },
    //   //       },
    //   //     },
    //   //   ],
    //   // });

    //   // const agentResolution = this.buildAppDeprecated(userInfo);
    //   // if (dataStreamWriter) {
    //   //   DataStreamUtils.writeMessage(dataStreamWriter, agentResolution.message, userInfo.locale);
    //   // }
    //   const {
    //     message: agentMessage,
    //     usage,
    //     prompt,
    //     options,
    //   } = await BuildAppAIIntentResolver.aiConsultingQuickStream(
    //     chatHistories,
    //     resolver,
    //     user,
    //     this.intentParams.spaceId,
    //     { dataStreamWriter },
    //     this.intentParams.needApproval,
    //   );
    //   const finalConfirm: ResolutionResultInfo = {
    //     intentParam: this.intentParams,
    //     status: 'NEEDS_CONFIRMATION',
    //     message: agentMessage,
    //     usage,
    //     prompt,
    //     options,
    //   };
    //   return finalConfirm;
    // }

    // if (this.intentSO.resolutionStatus === 'NEEDS_CONFIRMATION') {
    //   assert(resolver.type === 'UI');
    //   assert(resolver.uiResolve.type === 'FLOW');
    //   if (resolver.uiResolve.response === 'YES') {
    //     // 这里，创建！！
    //     assert(this.intentParams.spaceId);
    //     const spaceSO = await SpaceSO.init(this.intentParams.spaceId!);
    //     const rootFolder = await spaceSO.getRootFolder();
    //     const folderId = await rootFolder.createChildren(user, [
    //       {
    //         resourceType: 'FOLDER',
    //         name: this.intentParams.name!,
    //         children: this.intentParams.resources,
    //       },
    //     ]);
    //     const msg: ResolutionMessage = {
    //       // role: 'assistant',
    //       parts: [
    //         {
    //           type: 'text',
    //           text: `Created! Go to the space to see it! [Go to space](/space/${this.intentParams.spaceId}/node/${folderId})`,
    //         },
    //       ],
    //       text: {
    //         en: `Created! Go to the space to see it! [Go to space](/space/${this.intentParams.spaceId}/node/${folderId})`,
    //         'zh-CN': `创建成功！去空间站看看吧！[去空间](/space/${this.intentParams.spaceId}/node/${folderId})`,
    //         'zh-TW': `創建成功！去空間站看看吧！[去空間](/space/${this.intentParams.spaceId}/node/${folderId})`,
    //         ja: `作成しました！スペースに行って見てください！[スペースへ](/space/${this.intentParams.spaceId}/node/${folderId})`,
    //       },
    //       skillsets: [{ kind: 'preset', key: 'bika-app-builder' }],
    //     };
    //     if (dataStreamWriter) {
    //       DataStreamUtils.writeMessage(dataStreamWriter, msg, user.locale);
    //     }
    //     return {
    //       intentParam: this.intentParams,
    //       status: 'SUCCESS',
    //       message: msg,
    //       usage: {
    //         model: undefined,
    //         promptTokens: 0,
    //         completionTokens: 0,
    //         totalTokens: 0,
    //         costCredit: 0,
    //       },
    //     };
    //   }
    //   // 暂时不使用AI，AGAIN和NO，都会返回主界面
    //   if (resolver.uiResolve.response === 'NO' || resolver.uiResolve.response === 'AGAIN') {
    //     // 不认同这个架构图？回到聊天阶段！
    //     this.intentParams.resources = undefined;
    //     const msg: ResolutionMessage = {
    //       // role: 'assistant',
    //       parts: [
    //         {
    //           type: 'text',
    //           text: 'Not satisfied? Can you describe it in more detail?',
    //         },
    //       ],
    //       text: {
    //         'zh-CN': '不满意？你再详细描述一下？',
    //         en: 'Not satisfied? Can you describe it in more detail?',
    //         'zh-TW': '不滿意？你再詳細描述一下？',
    //         ja: '不満足ですか？もっと詳しく説明してもらえますか？',
    //       },
    //       prompts: this.getPrompts(user.locale),
    //       skillsets: [{ kind: 'preset', key: 'bika-app-builder' }],
    //     };
    //     if (dataStreamWriter) {
    //       DataStreamUtils.writeMessage(dataStreamWriter, msg, user.locale);
    //     }
    //     return {
    //       intentParam: this.intentParams,
    //       status: 'DIALOG',
    //       message: msg,
    //       usage: {
    //         model: undefined,
    //         promptTokens: 0,
    //         completionTokens: 0,
    //         totalTokens: 0,
    //         costCredit: 0,
    //       },
    //     };
    //   }

    //   // 这里不会走到
    //   assert(resolver.uiResolve.response === 'AGAIN');
    //   await this.fillInResources(this.intentParams.prompt!, 'AI', user);
    //   // 这里开始AI生成！
    //   // const agentResolution = this.buildAppDeprecated(userInfo);
    //   // if (dataStreamWriter) {
    //   //   DataStreamUtils.writeMessage(dataStreamWriter, agentResolution.message, userInfo.locale);
    //   // }
    //   const {
    //     message: agentMessage,
    //     usage,
    //     prompt,
    //     options,
    //   } = await BuildAppAIIntentResolver.aiConsultingQuickStream(
    //     chatHistories,
    //     resolver.message || '',
    //     user,
    //     { dataStreamWriter },
    //     this.intentParams.plannerNeedApproval,
    //   );
    //   const finalConfirm: ResolutionResultInfo = {
    //     intentParam: this.intentParams,
    //     status: 'NEEDS_CONFIRMATION',
    //     message: agentMessage,
    //     usage,
    //     prompt,
    //     options,
    //   };
    //   return finalConfirm;
    // }

    // 上面的检验完成，开始AI生成模板...
    // assert(resolver.type === 'MESSAGE');
    // this.intentParams.prompt = resolver.message; // 保存prompt信息
    // await this.fillInResources(resolver.message, 'SEARCH', user);

    // if (!this.intentParams.spaceId) {
    //   // 没有填space ID，需要用户填写
    //   const spaces = await user.findSpaces();
    //   const choices = spaces.map((space) => ({
    //     key: space.id,
    //     render: space.name,
    //   }));

    //   const msg: ResolutionMessage = {
    //     // role: 'assistant',
    //     parts: [
    //       {
    //         type: 'text',
    //         text: iStringParse(
    //           {
    //             en: 'Which space do you want to create the app in?',
    //             'zh-CN': '你想在哪个空间创建这个应用？',
    //             'zh-TW': '你想在哪個空間創建這個應用？',
    //             ja: 'どのスペースでアプリを作成しますか？',
    //           },
    //           user.locale,
    //         ),
    //       },
    //       {
    //         type: 'tool-invocation',
    //         toolInvocation: {
    //           state: 'call', // 这里不要返回result，客户端 的最后一条消息是 assistant，会调用 2 次重复
    //           toolCallId: generateNanoID('custom-tool-'),
    //           toolName: 'form',
    //           args: {
    //             type: 'CHOICES',
    //             choices,
    //           },
    //           // result: 'asking',
    //         },
    //       },
    //     ],
    //     // text:,
    //     // tools: [
    //     // ],
    //     skillsets: [{ kind: 'preset', key: 'bika-app-builder' }],
    //   };
    //   if (dataStreamWriter) {
    //     DataStreamUtils.writeMessage(dataStreamWriter, msg, user.locale);
    //   }
    //   return {
    //     intentParam: this.intentParams,
    //     status: 'NEEDS_DISAMBIGUATION',
    //     message: msg,
    //     usage: {
    //       model: undefined,
    //       promptTokens: 0,
    //       completionTokens: 0,
    //       totalTokens: 0,
    //       costCredit: 0,
    //     },
    //   };
    // }

    // const res = this.buildAppDeprecated(userInfo);
    // if (dataStreamWriter) {
    //   DataStreamUtils.writeMessage(dataStreamWriter, res.message, userInfo.locale);
    // }
    // return res;
    const {
      message: agentMessage,
      usage,
      prompt,
      options,
    } = await BuildAppAIIntentResolver.aiConsultingQuickStream(
      chatHistories,
      resolver,
      user,
      this.intentParams.spaceId,
      { dataStreamWriter },
      this.intentParams.needApproval,
    );
    const finalConfirm: ResolutionResultInfo = {
      intentParam: this.intentParams,
      status: 'NEEDS_CONFIRMATION',
      message: agentMessage,
      usage,
      prompt,
      options,
    };
    return finalConfirm;
  }

  async doComplete(): Promise<{ nextIntent: AIIntentParams | null }> {
    return {
      nextIntent: null,
    };
  }

  public static async aiConsultingQuickStream(
    chatHistories: AIMessageBO[],
    _resolver: AIResolveVO,
    user: UserSO,
    spaceId: string | undefined,
    options?: Pick<AIModelOptions, 'dataStreamWriter' | 'onStepFinish' | 'onChunk'>,
    needApproval?: { engineer?: boolean; planner?: boolean },
  ) {
    // let searchTemplates = await StoreTemplateSO.searchTemplatesWithESAdvanced(userPrompt);
    // if (searchTemplates.length === 0) {
    //   console.warn('[WARN] 没有找到相关模板，使用随机模板, 搜索语句', userPrompt);
    //   searchTemplates = await StoreTemplateSO.getRandomTemplatesPO();
    // }

    // const templateId = searchTemplates[0]?.templateId;
    // console.log('[DEBUG] 使用模板ID:', templateId, '搜索语句:', userPrompt);
    return AISO.streamChat(
      // chatHistories,
      {
        user,
        system: `## Role
You are a Bika.ai Agentic App Builder and AI Agent Builder.

Language: ${user.locale}
spaceId: ${spaceId}

## Rules

- Before tools execution, say some words to user.
- Tools Execution must obey the "Tools Executions".

## Tools Execution Order (MANDATORY)

You MUST execute exactly 3 tools in this specific order - NO EXCEPTIONS:

1. **FIRST**: Execute 'bika-ai-app-builder-planner' tool
2. **SECOND**: Execute 'bika-ai-app-builder-engineer' tool  
3. **THIRD**: Execute 'bika-ai-app-builder-installer' tool

## Critical Rules:
- **ALL 3 tools MUST be executed** - missing any tool is NOT allowed
- **Order is STRICTLY enforced** - you cannot skip or reorder tools
- **No tool can be executed twice** - each tool runs exactly once
- **After each tool completes, immediately proceed to the next tool**
- **Do not add any other tools or modify this sequence**

## Execution Flow:
1. Execute 'bika-ai-app-builder-planner' → Wait for completion
2. Execute 'bika-ai-app-builder-engineer' → Wait for completion  
3. Execute 'bika-ai-app-builder-installer' → Wait for completion
4. End conversation after all 3 tools complete

After all tools are executed, the conversation will be ended, don't answer user anything more, just tell him/her the conversation is ended.`,
        // prompt: userPrompt,
        messages: chatHistories,
        skillsets: [
          {
            kind: 'preset',
            key: 'bika-app-builder',
            needApprovals: _.compact([
              // PLANNER 不需要审批:
              needApproval?.planner === true ? 'bika-ai-app-builder-planner' : undefined,
              needApproval?.engineer === true ? 'bika-ai-app-builder-engineer' : undefined,
              'bika-ai-app-builder-installer',
            ]),
          },
        ],
        maxSteps: 20,
      },
      options || {},
      {},
    );
  }
}
