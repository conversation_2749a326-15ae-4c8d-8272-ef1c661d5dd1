import assert from 'assert';
import type { DataStreamWriter } from 'ai';
import { generateNanoID } from 'sharelib/nano-id';
import { CreateResourceArrayPrompt } from '@bika/contents/config/server/ai-prompts/ai-consulting/utils';
import { DataStreamUtils } from '@bika/domains/ai/server/ai-chat/data-stream-utils';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { ResolutionResultInfo } from '@bika/domains/ai/server/types';
import type { UserSO } from '@bika/domains/user/server/user-so';
import { AdminIntentParams, AIIntentParams, AIMessageBO } from '@bika/types/ai/bo';
import { ResolutionMessage, AIResolveVO } from '@bika/types/ai/vo';
import { CONST_PREFIX_AI_MESSAGE } from '@bika/types/database/vo';
import { NodeResourceType } from '@bika/types/node/bo';
import { AIIntentResolver } from './resolvers';

export class AdminAIIntentResolver extends AIIntentResolver<AdminIntentParams> {
  get operater() {
    return {
      flow_preview: 'see flow',
      create: 'create',
      json_preview: 'to json',
      yes: 'yes',
      no: 'no',
    };
  }

  async onInit(): Promise<boolean> {
    return true;
  }

  async getPrologue(): Promise<ResolutionMessage> {
    return {
      // role: 'assistant',
      parts: [
        {
          type: 'text',
          text: 'Hello hello I am admin',
        },
      ],
      text: 'Hello hello I am admin',
      prompts: await this.getPrompts('en'),
    };
  }

  async doResolve(
    resolver: AIResolveVO,
    user: UserSO,
    chatHistories: AIMessageBO[],
    dataStreamWriter?: DataStreamWriter,
  ): Promise<ResolutionResultInfo> {
    assert(resolver.type === 'MESSAGE');
    if (resolver.option === 'node-resource-engineer') {
      await this.multiTurnResourceChat(resolver, user, chatHistories, dataStreamWriter);
      // for await (const chunk of this.multiTurnResourceChat(resolver, _userInfo, _chatHistories)) {
      //   dataStreamWriter?.write(chunk);
      // }
    }

    const { message, usage } = await AISO.streamChat(
      {
        user,
        messages: [
          ...chatHistories,
          {
            id: generateNanoID('aium-'),
            role: 'user',
            parts: [
              {
                type: 'text',
                text: resolver.message,
              },
            ],
          },
        ],
      },
      { dataStreamWriter },
      {},
    );

    return {
      intentParam: this.intentParams,
      status: 'DIALOG',
      message,
      usage,
    };
  }

  private async multiTurnResourceChat(
    resolver: AIResolveVO & { type: 'MESSAGE' },
    user: UserSO,
    chatHistories: AIMessageBO[],
    dataStreamWriter?: DataStreamWriter,
  ) {
    // const prologue = await this.getPrologue();
    // todo 切割用户消息，需要区分是否是创建同一个资源
    // const messages = chatHistories
    //   .filter((msg) => msg.text && msg.text !== prologue.text)
    //   .map(
    //     (msg) =>
    //       ({
    //         role: msg.role,
    //         content: iStringParse(msg.text, userInfo.locale),
    //       }) as CoreMessage,
    //   );
    if (this.intentToPreviewResourceFlow(chatHistories)) {
      await this.previewResourceFlow(resolver, chatHistories, user, dataStreamWriter);
    } else {
      // 资源推理
      await this.resourceReasoningChat(resolver, chatHistories, user, dataStreamWriter);
    }
  }

  private async previewResourceFlow(
    resolver: AIResolveVO & { type: 'MESSAGE' },
    chatHistories: AIMessageBO[],
    user: UserSO,
    dataStreamWriter?: DataStreamWriter,
  ) {
    // 在chatHistories中找最后一个有UI的message,并且UI的type为PROMPT,并且prompts包含flow_preview
    const lastAssistantMessage = chatHistories
      .reverse()
      .find((msg) => msg.ui?.type === 'PROMPT' && msg.ui?.prompts?.includes(this.operater.flow_preview));
    assert(lastAssistantMessage?.text && typeof lastAssistantMessage.text === 'string');
    // 在这个message的content 中匹配出包涵的所有资源类型 资源类型有 DATABASE, AUTOMATION,
    // todo more resource type support
    const resourceTypes = new Set(lastAssistantMessage.text.match(/DATABASE|AUTOMATION/g));
    // this.intentParams.resources = [];
    // 根据资源类型，生成对应的资源
    for (const resourceType of resourceTypes) {
      await this.resourcesObjectStream(
        user,
        resourceType as NodeResourceType,
        lastAssistantMessage.text,
        dataStreamWriter,
      );
    }

    for await (const chunk of DataStreamUtils.intentUI(resolver, {
      type: 'FLOW',
      resources: [],
      title: 'Resource Flow',
      hiddenButtons: ['CONFIRM', 'AGAIN', 'CANCEL'],
    })) {
      dataStreamWriter?.write(chunk);
    }
  }

  private async resourceReasoningChat(
    resolver: AIResolveVO & { type: 'MESSAGE' },
    messages: AIMessageBO[],
    user: UserSO,
    dataStreamWriter?: DataStreamWriter,
  ): Promise<ResolutionResultInfo> {
    // 使用 ResourceResoningPrompt 作为系统提示
    // const chatMessages: AIMessageBO[] = this.messageFilter(messages, ResourceResoningPrompt);

    const { message, usage } = await AISO.streamChat(
      // messages,
      {
        user,
        messages: [
          ...messages,
          {
            id: generateNanoID('aium-'),
            role: 'user',
            parts: [
              {
                type: 'text',
                text: resolver.message,
              },
            ],
          },
        ],
        // messages: chatMessages,
      },
      {
        dataStreamWriter,
      },
      {},
    );

    // for await (const chunk of DataStreamUtils.textStream(textStream)) {
    //   dataStreamWriter?.write(chunk);
    // }

    // const resultText = await text;
    // let ui: PromptAIIntentUIVO | undefined;
    // // 如果text 50 字符，则提示
    // if (resultText.length > 100) {
    //   ui = {
    //     type: 'PROMPT',
    //     prompts: [this.operater.flow_preview],
    //   };
    //   for await (const chunk of DataStreamUtils.intentUI(resolver, ui)) {
    //     dataStreamWriter?.write(chunk);
    //   }
    // }
    return {
      intentParam: this.intentParams,
      status: 'DIALOG',
      message,
      usage,
    };
  }

  private async resourcesObjectStream(
    user: UserSO,
    resourceType: NodeResourceType,
    prompt: string,
    dataStreamWriter?: DataStreamWriter,
  ) {
    const reasoning = `get resource: ${resourceType}...`;

    dataStreamWriter?.write(`g:"${reasoning}"\n`);

    const { result } = await AISO.streamText(
      {
        user,
        system: CreateResourceArrayPrompt(resourceType),
        prompt,
      },
      {
        temperature: 0.2,
        model: 'deepseek-v3',
        dataStreamWriter,
      },
    );
    const { textStream, text: textPromise } = result;
    // let index = 0;
    // // 每100个字符，输出一个换行和***
    // for await (const chunk of textStream) {
    //   index += chunk.length;
    //   if (index % 1000 === 0) {
    //     dataStreamWriter?.write(`g:"..."\n`);
    //   }
    // }
    // const text = await textPromise;
    // const { data, success } = NodeResourceSchema.array().safeParse(JSON.parse(text));
    // if (!success) {
    //   reasoning = `get resource: ${resourceType} failed, ingored...`;
    //   dataStreamWriter?.write(`g:"${reasoning}"\n`);
    // } else {
    //   reasoning = `get resource: ${resourceType} success, added to resources...`;
    //   dataStreamWriter?.write(`g:"${reasoning}"\n`);
    //   this.intentParams.resources!.push(...data!);
    // }
  }

  /**
   * 完成! 创建新意图
   */
  async doComplete(): Promise<{ nextIntent: AIIntentParams | null }> {
    return {
      nextIntent: null,
    };
  }

  /**
   * 获取UI发到客户端，根据当前的解决状态和参数填写情况而定
   * @returns
   */
  // async parseUI(): Promise<AIIntentUIVO> {
  //   return this.defaultPromptUI();
  // }

  private intentToPreviewResourceFlow(messages: AIMessageBO[]) {
    const lastMessage = messages[messages.length - 1];
    const lastMessageContent = lastMessage.content!.toString().trim();
    const lastMessageRole = lastMessage.role;
    if (lastMessageRole === 'user' && lastMessageContent === this.operater.flow_preview) {
      return true;
    }
    return false;
  }

  // private intentToPreivewResourceJson(messages: AIMessageBO[]) {
  //   const lastMessage = messages[messages.length - 1];
  //   const lastMessageContent = lastMessage.content!.toString().trim();
  //   const lastMessageRole = lastMessage.role;
  //   if (lastMessageRole === 'user' && lastMessageContent === this.operater.json_preview) {
  //     return true;
  //   }
  //   return false;
  // }

  private messageFilter(messages: AIMessageBO[], systemPrompt?: string) {
    const cleanMessages = messages.filter((msg) => {
      if (msg.role === 'system') {
        return false;
      }
      if (msg.role === 'user' && typeof msg.content === 'string') {
        return !Object.values(this.operater).includes(msg.content);
      }
      return true;
    });
    if (systemPrompt) {
      cleanMessages.unshift({
        id: generateNanoID(CONST_PREFIX_AI_MESSAGE),
        role: 'system',
        content: systemPrompt,
        parts: [],
      });
    }
    return cleanMessages;
  }
}
