import type { Attachment } from 'ai';
import type React from 'react';
import { useState } from 'react';
import { useTRPC } from '@bika/api-caller/context';
import { Button } from '@bika/ui/button';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import { Box } from '@bika/ui/layouts';
import { useInputStore } from '../input-store';

export interface UploadingAttachment {
  id: number;
  name: string;
  base64: string;
  contentType: string;
}

export type PreviewAttachment = Attachment & { id: string };

interface AttachmentPreviewProps {
  data:
    | {
        type: 'preview';
        attachment: PreviewAttachment;
      }
    | {
        type: 'uploading';
        attachment: UploadingAttachment;
      };
}
export const AttachmentPreview = (props: AttachmentPreviewProps) => {
  const {
    data: { type, attachment },
  } = props;
  const trpc = useTRPC();
  const { attachments = [], setAttachments } = useInputStore();

  const [isDeletingAttachment, setIsDeletingAttachment] = useState(false);
  const deleteAttachment = async (id: string) => {
    setIsDeletingAttachment(true);
    await trpc.attachment.delete.mutate({ id });
    setAttachments(attachments.filter((att) => att.id !== id));
    setIsDeletingAttachment(false);
  };

  return (
    <Box
      key={attachment.id}
      position={'relative'}
      width={'64px'}
      height={'64px'}
      sx={{
        borderRadius: '4px',
        background: 'var(--bg-controls)',
        border: '1px solid var(--border-default)',
      }}
    >
      <img
        src={type === 'uploading' ? attachment.base64 : attachment.url}
        alt={attachment.name}
        className="w-[64px] h-[64px] rounded-[4px]"
      />
      {type === 'uploading' ? (
        <Button
          loading
          variant="plain"
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '32px',
            height: '32px',
            background: 'transparent !important',
          }}
        >
          {''}
        </Button>
      ) : (
        <Box
          onClick={() => {
            if (isDeletingAttachment) return;
            deleteAttachment(attachment.id);
          }}
          sx={{
            position: 'absolute',
            top: '4px',
            right: '4px',
            background: 'var(--bg-controls)',
            borderRadius: '50%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '16px',
            height: '16px',
            backgroundColor: 'var(--bg-mask)',
            cursor: isDeletingAttachment ? 'not-allowed' : 'pointer',
            '&:hover': {
              background: 'var(--bg-controls-hover)',
            },
          }}
        >
          <CloseOutlined color="var(--static)" size={8} />
        </Box>
      )}
    </Box>
  );
};
