import type { Message as AISDKMessage, ToolInvocation } from '@ai-sdk/ui-utils';
import Box from '@mui/joy/Box';
import React from 'react';
import { useTRPCQuery } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n/context';
import type { AIMessageBO, AIIntentParams } from '@bika/types/ai/bo';
import { toAISDKMessage } from '@bika/types/ai/bo';
import type { AIIntentUIResolveDTO } from '@bika/types/ai/dto';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { useSpaceContextForce } from '@bika/types/space/context';
import { Link } from '@bika/ui/form-components';
import ChevronDownOutlined from '@bika/ui/icons/components/chevron_down_outlined';
import ChevronUpOutlined from '@bika/ui/icons/components/chevron_up_outlined';
import { Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import { AIMessageAnnotations } from './ai-message-annotations';
import { MessageIcon } from './ai-message-icon';
import { AIMessageReasoning } from './ai-message-reasoning';
import { Message } from '../wizard/message';
import { ToolUI } from './tools/ai-tool-ui';
import { ToolResultVO } from './tools/type';

type ToolCallId = string;
interface Props {
  chatId: string;
  message: AIMessageBO;
  status: 'submitted' | 'streaming' | 'ready' | 'error';
  disabled: boolean;
  // TODO :  这里定义含糊了， 需要讨论下
  toolExecuteErrors?: Record<ToolCallId, string>;
  sendUI: (uiResolve: AIIntentUIResolveDTO) => Promise<void>;
  sendMessage: (message: string) => Promise<void>;
  onSelectTool?: (uiPart: ToolInvocation, uiMessage: AISDKMessage) => void;
  addToolResult: (args: { toolCallId: string; result: ToolResultVO }) => void;
  skillsets: SkillsetSelectDTO[];

  /**
   * Selecting / Selected Tool ID
   */
  artifactToolId?: string;
  initAIIntent?: AIIntentParams;
}

export function AIChatMessage(props: Props) {
  const m = props.message;
  const msgAnnotations = React.useMemo(() => new AIMessageAnnotations(m.annotations), [m.annotations]);

  const sourceParts = m.parts.filter((p) => p.type === 'source');

  const [showSource, setShowSource] = React.useState(false);
  const locale = useLocale();
  const { i: iStr, t } = locale;
  const spaceContext = useSpaceContextForce();
  const { myInfo } = spaceContext || {};
  const trpcQuery = useTRPCQuery();
  const remoteExecuteTool = trpcQuery.ai.executeTool.useMutation();

  const renderUserMessage = () => {
    if (m.content) {
      if (m.content.startsWith('/resolve:')) {
        // 取后面的JSON字符串
        const jsonStr = m.content.substring('/resolve:'.length);
        try {
          const json = JSON.parse(jsonStr);
          if (json.type === 'UI') {
            const uiResolve = json.uiResolve as AIIntentUIResolveDTO;
            switch (uiResolve.type) {
              case 'CHOICES': {
                return uiResolve.choiceOption?.render || m.content;
              }
              case 'FLOW': {
                return uiResolve.response;
              }
              default: {
                return json.uiResolve.text;
              }
            }
          }
        } catch (e) {
          return m.content;
        }
      }
      return m.content;
    }
    // 有可能 m.content 为空，但是 m.parts 有值
    if (m.parts.length > 0) {
      return m.parts
        .map((part) => {
          if (part.type === 'text') {
            return part.text;
          }
          return '';
        })
        .join('');
    }

    return 'Error';
  };

  const firstAiConsultingIndex = m.parts.findIndex(
    (part) => part.type === 'tool-invocation' && part.toolInvocation.toolName.startsWith('ai-consulting'),
  );

  const renderMessage = () => {
    if (m.role === 'user') {
      const userMessage = renderUserMessage();
      return (
        <Stack key={m.id} display="flex" flexDirection="row" justifyContent="flex-end">
          <Box
            sx={{
              whiteSpace: 'pre-wrap',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            {m.experimental_attachments &&
              m.experimental_attachments.length > 0 &&
              m.experimental_attachments.map((attachment, idx) => {
                const urlIndex = attachment.url.indexOf('?');
                const url = urlIndex === -1 ? attachment.url : attachment.url.slice(0, urlIndex);
                return (
                  <Box key={`${attachment.name}-${idx}`} sx={{ mb: 1, alignSelf: 'flex-end' }}>
                    <img src={url} alt={attachment.name} className="w-[64px] rounded-[4px]" />
                  </Box>
                );
              })}
            {!!userMessage && (
              <Stack
                sx={{
                  fontSize: '16px',
                  lineHeight: '22px',
                  borderRadius: '8px',
                  backgroundColor: 'var(--brand)',
                  color: 'var(--on-brand)',
                  py: 2,
                  px: 1,
                  padding: '12px 16px',
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                }}
              >
                {userMessage}
              </Stack>
            )}
          </Box>
          <MessageIcon type="user" user={msgAnnotations.creator || myInfo} />
        </Stack>
      );
    }
    if (m.role === 'assistant' || m.role === 'system') {
      return (
        <Stack key={m.id} display="flex" flexDirection="row">
          <MessageIcon type="assistant" initAIIntent={props.initAIIntent} />

          <Stack
            sx={{
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
            }}
          >
            {/* Sources */}
            {sourceParts.length > 0 && (
              <Stack mb={1}>
                <Stack
                  sx={{
                    display: 'flex',
                    cursor: 'pointer',
                    flexDirection: 'row',
                    justifyContent: 'center',
                    width: 'max-content',
                    borderRadius: '8px',
                    p: 1,
                    color: 'var(--text-primay)',
                    backgroundColor: 'var(--bg-controls)',
                    maxWidth: '80%',
                    '&:hover': {
                      backgroundColor: 'var(--bg-controls-hover)',
                    },
                  }}
                  onClick={() => {
                    setShowSource(!showSource);
                  }}
                >
                  <Typography mr={1} textColor="var(--text-secondary)" level="b4">
                    {t('ai.reference', { count: sourceParts.length })}
                  </Typography>
                  {showSource ? (
                    <ChevronUpOutlined color={'var(--text-secondary)'} />
                  ) : (
                    <ChevronDownOutlined color={'var(--text-secondary)'} />
                  )}
                </Stack>
                {/* showSource */}

                {showSource &&
                  sourceParts.map((part) => (
                    <Link my={0.5} key={part.source.id} target="_blank" href={part.source.url}>
                      {part.source.title || part.source.url}
                    </Link>
                  ))}
              </Stack>
            )}

            {/* Reasoning & Messages & Tools */}
            {m.parts
              .filter((part) => {
                if (part.type === 'text' && part.text.replace(/\n/g, '') === '') {
                  return false;
                }
                return true;
              })
              .map((part, idx2) => (
                <Box key={`part-${idx2}-${part.type}`}>
                  {/* Reasoning */}
                  {part.type === 'reasoning' && <AIMessageReasoning part={part} />}

                  {/* Text */}
                  {part.type === 'text' && (
                    <Stack
                      sx={{
                        alignSelf: 'flex-start',
                        backgroundColor: 'var(--bg-popup)',
                        fontSize: '16px',
                        borderRadius: '8px',
                        py: 2,
                        px: 1,
                        padding: '12px 16px',
                      }}
                    >
                      <Message text={iStr(part.text)} />
                    </Stack>
                  )}

                  {/* Tools */}
                  {part.type === 'tool-invocation' && (
                    <Stack gap={2}>
                      {/* {toolParts.map((part, idx2) => ( */}
                      <ToolUI
                        executeToolResult={async (toolCallId: string) => {
                          const remoteToolResult = await remoteExecuteTool.mutateAsync({
                            toolCallId,
                            chatId: props.chatId, // 传入 messageId，避免 toolCallId 重复
                          });

                          console.log('executeToolResult', toolCallId, remoteToolResult);
                          return remoteToolResult;
                        }}
                        addToolResult={props.addToolResult}
                        skillsets={props.skillsets}
                        part={part}
                        key={idx2}
                        error={props.toolExecuteErrors?.[part.toolInvocation.toolCallId]}
                        hideFlow={firstAiConsultingIndex === idx2}
                        isHighlight={props.artifactToolId === part.toolInvocation.toolCallId}
                        disabled={props.disabled} // 除了最后 1 个UI，其它全部disabled，不能交互
                        sendUI={props.sendUI}
                        sendMessage={props.sendMessage}
                        onClickTool={(toolUI) => {
                          if (props.onSelectTool) {
                            props.onSelectTool(toolUI, toAISDKMessage(m));
                          }
                        }}
                      />
                    </Stack>
                  )}
                </Box>
              ))}
          </Stack>
        </Stack>
      );
    }
  };

  return (
    <Stack mb={'24px'} sx={{ width: '100%', maxWidth: '720px' }} key={m.id}>
      {renderMessage()}
    </Stack>
  );
}
