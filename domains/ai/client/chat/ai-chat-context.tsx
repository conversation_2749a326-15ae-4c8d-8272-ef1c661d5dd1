import assert from 'assert';
import type { AIChatContextVO } from '@bika/types/ai/vo';
import { NodeIcon } from '@bika/ui/node/icon';

interface Props {
  value: AIChatContextVO;
}
export function AIChatContextRenderer(props: Props) {
  const { value } = props;
  assert(value.type === 'node', 'AIChatContextRenderer only supports node type for now');

  return (
    <>
      <div className="flex items-center gap-1 px-[16px] py-[8px] text-[--text-disabled] space-x-2 border border-[--border-default] rounded-lg h-[32px] cursor-pointer">
        {value.node.type && (
          <NodeIcon
            value={{ kind: 'node-resource', nodeType: value.node.type, customIcon: value.node.icon ?? undefined }}
            size={16}
            color="var(--text-disabled)"
          />
        )}
        {/* <NodeIcon type={node?.type} size={16} color="var(--text-disabled)" /> */}

        <div className="text-b3">{value.node.name}</div>
      </div>
    </>
  );
}
