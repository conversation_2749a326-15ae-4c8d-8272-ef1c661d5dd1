import Skeleton from '@mui/joy/Skeleton';
import React from 'react';
import { TalkVOPopover } from '@bika/domains/talk/client/talk-vo-popover';
import { useTalkDisplayInfo } from '@bika/domains/talk/client/use-talk-display-info';
import { TalkDetailVO } from '@bika/types/space/vo';
import { IconButton } from '@bika/ui/button';
import { Chip } from '@bika/ui/chip';
import { Popover, PopoverTrigger, PopoverContent } from '@bika/ui/components/popover/index';
import GotoOutlined from '@bika/ui/icons/components/goto_outlined';
import { Box } from '@bika/ui/layouts';
import { NodeIcon } from '@bika/ui/node/icon';
import { Typography } from '@bika/ui/texts';
import type { AgentListProps } from '../agents-selector/agents-selector';
// import type { ISelectorItem } from './type';

type Props = AgentListProps & {
  value: TalkDetailVO;
  isSelected: boolean;
  width: number;
};

export function SelectorTalkCardRenderer(props: Props) {
  const item = props.value;
  const isSelected = props.isSelected;
  const skillsets = item.skillsets;
  const displayInfo = useTalkDisplayInfo(item);
  if (!displayInfo) {
    return null; // or handle the error as needed
  }

  const { url, name, description, nodeIconValue } = displayInfo;

  return (
    <Popover
      key={`agent-item-${item?.id}`}
      placement="bottom-start"
      hoverEnable={true}
      clickEnable={false}
      zIndex={1998}
      hoverOptions={{
        delay: { open: 100, close: 200 },
        restMs: 100,
      }}
    >
      <PopoverTrigger asChild>
        <Box
          onClick={() => {
            if (!item) return;
            props.onSelectedChange(item);
          }}
          sx={{
            cursor: 'pointer',
            width: props.width,
            height: 108,
            flexShrink: 0,
            px: 2,
            py: 2,
            borderRadius: 'lg',
            border: isSelected ? '1px solid var(--brand)' : '1px solid var(--border-default)',
            backgroundColor: 'var(--bg-blur)',
            backdropFilter: 'blur(8px)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 1,
            position: 'relative',
            '&:hover': {
              backgroundColor: 'color-mix(in srgb, var(--bg-blur) 100%, var(--hover) 100%)',
            },
          }}
        >
          {item && <NodeIcon color="var(--static)" size={40} value={nodeIconValue} />}
          {item ? (
            <Typography
              level="b3"
              textAlign="center"
              textColor="var(--text-primary)"
              className="truncate max-w-[100%]"
              sx={{
                mt: 1,
              }}
            >
              {name}
            </Typography>
          ) : (
            <Skeleton variant="rectangular" width="100%" height={24} />
          )}
          {item.type === 'expert' && (
            <Chip
              size="sm"
              sx={{
                position: 'absolute',
                top: 6,
                right: 6,
                fontSize: 10,
                px: 0.5,
                py: 0,
                background: 'linear-gradient(90deg, #6681E5 -13.4%, #9852D7 100.33%)',
                borderRadius: '4px',
                color: 'var(--static)',
              }}
            >
              Expert
            </Chip>
          )}
        </Box>
      </PopoverTrigger>
      <PopoverContent>
        <TalkVOPopover
          icon={nodeIconValue}
          nodeType={'AI'} // item?.nodeType}
          skillsets={skillsets}
          // nodeType={item?.nodeType}
          name={name}
          description={description}
          permission={item?.type === 'node' ? item.node.permission?.privilege : undefined}
          right={
            <IconButton
              onClick={() => {
                props.onSubmit(item);
              }}
            >
              <GotoOutlined />
            </IconButton>
          }
        />
      </PopoverContent>
    </Popover>
  );
}
