// import assert from 'assert';
import assert from 'assert';
import { PromptTemplate } from '@langchain/core/prompts';
import { DataStreamWriter } from 'ai';
import { getDictionary, type Dictionary } from '@bika/contents/i18n/translate';
import { AIWriterResponse } from '@bika/types/ai/bo';
import type {
  FolderDTO,
  DatabaseDTO,
  DatabaseFieldDTO,
  AutomationDTO,
  AutomationActionDTO,
  AutomationTriggerDTO,
} from '@bika/types/ai/bo';
import { iStringParse } from '@bika/types/system';
import { BaseAIWriter } from './base-ai-writer';
import { AISO } from '../ai-so';

export class ResourceDescriptionWriter extends BaseAIWriter {
  private _resourceName: string | undefined;

  private formatFolderResource(resource: FolderDTO): string {
    const children = resource.children || [];
    const childrenStr = children
      .map((child) => {
        const childName = iStringParse(child.name, this._locale);
        const childDesc = iStringParse(child.description, this._locale);
        const userRemark = childDesc !== '' ? `\n    user remark: ${childDesc}` : '';
        return `  - resource name: ${childName}\n    resource type: ${child.resourceType}${userRemark}`;
      })
      .join('\n');

    return `**Folder "${this._resourceName}" includes the following sub resouces:**\n\`\`\`yaml\nsub resources:\n${childrenStr}\n\`\`\``;
  }

  private async formatAutomationResource(resource: AutomationDTO): Promise<string> {
    const dict: Dictionary = await getDictionary('en');
    const triggers = resource.triggers || [];
    const actions = resource.actions || [];
    const triggersStr = triggers
      .map((trigger: AutomationTriggerDTO) => {
        const triggerType = trigger.triggerType.toLowerCase();
        const triggerDict = dict.automation.trigger[triggerType as keyof typeof dict.automation.trigger];
        const triggerName = typeof triggerDict === 'string' ? '' : triggerDict?.name;
        const triggerDefinition = typeof triggerDict === 'string' ? '' : triggerDict?.description;
        const triggerDesc = iStringParse(trigger.description, this._locale);
        const userRemark = triggerDesc !== '' ? `user remark: ${triggerDesc}` : '';

        return triggerName !== '' && triggerDefinition !== ''
          ? `  - name: ${triggerName}, ${triggerDefinition}\n    ${userRemark}`
          : '';
      })
      .join('\n');

    const actionsStr = actions
      .map((action: AutomationActionDTO) => {
        const actionType = action.actionType.toLowerCase();
        const actionDict = dict.automation.action[actionType as keyof typeof dict.automation.action];
        const actionName = typeof actionDict === 'string' ? '' : actionDict?.name;
        const actionDefinition = typeof actionDict === 'string' ? '' : actionDict?.description;
        const actionDesc = iStringParse(action.description, this._locale);
        const userRemark = actionDesc !== '' ? `user remark: ${actionDesc}` : '';

        return actionName !== '' && actionDefinition !== ''
          ? `  - name: ${actionName}, ${actionDefinition}\n    ${userRemark}`
          : '';
      })
      .join('\n');

    if (triggersStr !== '' && actionsStr !== '') {
      const triggersAndActions = `\`\`\`yaml\ntriggers:\n${triggersStr}\nactions:\n${actionsStr}\n\`\`\``;

      return `**Automation "${this._resourceName}" includes the following triggers and actions:**\n${triggersAndActions}`;
    }
    return '';
  }

  private async formatDatabaseResource(resource: DatabaseDTO): Promise<string> {
    const dict: Dictionary = await getDictionary('en');
    const fields = resource.fields || [];

    const fieldsStr = fields
      .map((field: DatabaseFieldDTO) => {
        const fieldType = field.type.toLowerCase();
        const fieldDict = dict.database_fields[fieldType as keyof typeof dict.database_fields];
        const fieldTypeName = 'name' in fieldDict ? fieldDict.name : '';
        const fieldDefinition = 'description' in fieldDict ? fieldDict.description : '';
        const fieldDesc = iStringParse(field.description, this._locale);
        const userRemark = fieldDesc !== '' ? `user remark: ${fieldDesc}` : '';

        return fieldTypeName !== '' && fieldDefinition !== ''
          ? `  - name: ${field.name}\n    type: ${fieldTypeName}, ${fieldDefinition}\n    ${userRemark}`
          : '';
      })
      .join('\n');

    if (fieldsStr !== '') {
      return `**Database "${this._resourceName}" includes the following fields:**
\`\`\`yaml\nfields:\n${fieldsStr}\n\`\`\``;
    }
    return '';
  }

  private async getFullPrompt(userPrompt: string = ''): Promise<string> {
    assert(this._writerOptions.type === 'RESOURCE_DESCRIPTION', 'type must be RESOURCE_DESCRIPTION');
    const writerOptions = this._writerOptions;
    const resourceType = writerOptions.resource.resourceType;

    // console.log('resource:', writerOptions.resource);

    this._resourceName = iStringParse(writerOptions.resource.name, this._locale);
    let resourceExtra = '';

    switch (writerOptions.resource.resourceType) {
      case 'FOLDER':
        resourceExtra = this.formatFolderResource(writerOptions.resource as FolderDTO);
        break;
      case 'AUTOMATION':
        resourceExtra = await this.formatAutomationResource(writerOptions.resource as AutomationDTO);
        break;
      case 'DATABASE':
        resourceExtra = await this.formatDatabaseResource(writerOptions.resource as DatabaseDTO);
        break;
      default:
        resourceExtra = '';
    }

    const userInput = userPrompt !== '' ? `**User Input:** \n${userPrompt}` : '';

    const prompt = PromptTemplate.fromTemplate(
      `
**Resource Name:** {resourceName}

**Resource Type:** {resourceType}

{resourceExtra}

{userInput}
`,
    );
    return prompt.format({ resourceName: this._resourceName, resourceType, resourceExtra, userInput });
  }

  public async write(userInput: string, dataStreamWriter?: DataStreamWriter): Promise<AIWriterResponse> {
    const userFullPrompt = await this.getFullPrompt(userInput);
    const model = AISO.getSystemAIModel();
    const { result: streamResult } = await AISO.streamText(
      {
        user: this._user,
        system: `
# Role & Purpose
You are a specialized copywriting assistant with expertise in crafting clear and concise introductions for resource nodes. Your goal is to help users quickly understand the use case and benefits of the resource. 

# Execution Steps
- Read the information provided about the resource.
- Referring to the user's input if available, write an introduction for the resource node.

# Output Format
- The introduction should be no more than 100 words.
- Respond in plain text format without using Markdown, avoiding any formatting symbols like asterisks or hashes.

# Output Language
If no language is specified, default to the language code [${this._locale}].
`,
        prompt: userFullPrompt,
      },
      {
        dataStreamWriter,
        model,
      },
    );

    for await (const _ of streamResult.fullStream) {
      //
    }

    const aimsg = await streamResult.text;

    const usage = await streamResult.usage;

    const result: AIWriterResponse = {
      data: aimsg,
      value: aimsg,
      success: aimsg !== '',
      usages: [await AISO.parseAICreditCost(model, usage)],
    };

    return result;
  }
}
