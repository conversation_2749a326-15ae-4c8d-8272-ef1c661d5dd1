import { LanguageModelV1, LanguageModelV1CallOptions, type LanguageModelV1StreamPart } from '@ai-sdk/provider';
import { simulateReadableStream } from 'ai';
import { MockLanguageModelV1 } from 'ai/test';
import { BikaAdminSO } from '../../../admin/server/bika-admin-so';

type IMockFilter = {
  check: (options: LanguageModelV1CallOptions) => Promise<boolean>;
  doStream: LanguageModelV1['doStream'] | string;
};

/**
 *  启动 AI 的 mock 模式，有几种方式(优先级同下)：
 * 1. runtime force 强制设置 mock (最高优先)
 * 2. 在 bika-admin 中设置 mock
 * 3. 环境变量 env
 */
export class AIMockSO {
  // 运行时 设置 mock
  private static runtimeForceMockAIEnabled: boolean | null = null;

  private static mockAIEnabledCache: { value: boolean; timestamp: number } | null = null;

  private static readonly CACHE_DURATION = 60 * 1000; // 1 minute in milliseconds

  static setRuntimeMockAIEnabled(enabled: boolean | null) {
    this.runtimeForceMockAIEnabled = enabled;
  }

  /**
   * Force mock AI enabled, will force to use Mock AI Model, for mocking purpose.
   *
   * @param fn
   */
  static async sandbox(fn: () => Promise<void>) {
    try {
      this.setRuntimeMockAIEnabled(true);
      await fn();
    } finally {
      this.setRuntimeMockAIEnabled(null);
    }
  }

  static async isMockAI() {
    if (this.runtimeForceMockAIEnabled !== null) {
      return this.runtimeForceMockAIEnabled;
    }

    const now = Date.now();

    // Check if cache exists and is still valid
    if (this.mockAIEnabledCache && now - this.mockAIEnabledCache.timestamp < this.CACHE_DURATION) {
      return this.mockAIEnabledCache.value;
    }

    // Cache expired or doesn't exist, fetch new value
    const value = await BikaAdminSO.isMockAI();
    this.mockAIEnabledCache = { value, timestamp: now };

    return value;
  }

  static _filters: IMockFilter[] = [];

  static addFilter(filter: IMockFilter) {
    this._filters.push(filter);
  }

  static clearFilters() {
    this._filters = [];
  }

  static newMockModel() {
    return new MockLanguageModelV1({
      doStream: async (opts) => {
        console.log('[MockLanguageModelV1:doStream]', JSON.stringify(opts, null, 2));
        for (const filter of this._filters) {
          if (await filter.check(opts)) {
            if (typeof filter.doStream === 'string') {
              const strChunks: LanguageModelV1StreamPart[] = [];
              strChunks.push({ type: 'text-delta', textDelta: filter.doStream });
              return {
                stream: simulateReadableStream({
                  chunks: [
                    ...strChunks,
                    {
                      type: 'finish',
                      finishReason: 'stop',
                      logprobs: undefined,
                      usage: { completionTokens: 10, promptTokens: 3 },
                    },
                  ],
                }),
                rawCall: { rawPrompt: null, rawSettings: {} },
              };
            }
            return filter.doStream(opts);
          }
        }
        // default
        return {
          stream: simulateReadableStream({
            chunks: [
              { type: 'text-delta', textDelta: 'Hello' },
              { type: 'text-delta', textDelta: ', ' },
              { type: 'text-delta', textDelta: `I am Mock LanguageModelV1!` },
              {
                type: 'finish',
                finishReason: 'stop',
                logprobs: undefined,
                usage: { completionTokens: 10, promptTokens: 3 },
              },
            ],
          }),
          rawCall: { rawPrompt: null, rawSettings: {} },
        };
      },
    });
  }
}
