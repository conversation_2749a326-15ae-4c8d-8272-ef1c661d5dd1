import { expect, test, describe } from 'vitest';
import { type AIWriter } from '@bika/types/ai/bo';
import { iStringParse } from '@bika/types/i18n/bo';
import { Locale } from '@bika/types/system';
import { MockContext } from '../../__tests__/mock';
import { AIWriterSO } from '../server/ai-writer-so';

// const TEST_AI2 = process.env.TEST_AI2 && process.env.TEST_AI2 === 'true';
const TEST_AI = process.env.TEST_AI && process.env.TEST_AI === 'true';
if (TEST_AI) {
  console.log(`Will test AI via OpenAI API.... Proxy: ${process.env.OPENAI_BASE_URL}`);
}

const AIWriterContext = {
  locale: 'zh-CN' as Locale,
  createdAt: new Date().toISOString(),
};

describe('AI Writer Testing Suite', async () => {
  const { user } = await MockContext.initUserContext();

  // 生成文案
  test('Test AI Writer: TEXT_GENERATION', async () => {
    if (TEST_AI) {
      // 随便写点什么
      const result = await AIWriterSO.quickWrite(
        {
          type: 'TEXT_GENERATION',
          creatorPrompt: '测试系统Prompt，你擅长胡说八道',
        },
        '生成一段搞笑的笑话',
        AIWriterContext,
        user,
      );
      expect(result.value).toBeDefined();
    }
  });

  // 润色文案
  test('Test AI Writer: TEXT_REPHRASE', async () => {
    if (TEST_AI) {
      const result = await AIWriterSO.quickWrite(
        {
          type: 'TEXT_REPHRASE',
          creatorPrompt: '你擅长胡说八道',
          rephraseText: '这是一个测试',
        },
        '这是一个测试',
        AIWriterContext,
        user,
      );
      console.log('result', result);
      expect(result.value).toBeDefined();
    }
  });

  // 生成资源节点的描述信息
  test('RESOURCE_DESCRIPTION: 文件夹描述生成', async () => {
    if (TEST_AI) {
      const userInput = `您可以使用该模板，实现 AI 自动发布 X(Twitter) 推文，读取数据表中准备好的推文资料，自动发布推文，帮助您提高社交媒体的曝光度，增加粉丝互动。`;
      const writer: AIWriter = {
        type: 'RESOURCE_DESCRIPTION',
        resource: {
          resourceType: 'FOLDER',
          name: 'AI 自动发布 X 推文',
          children: [
            {
              resourceType: 'AUTOMATION',
              name: '定时发推文',
              description: '在特定时间发布推文。',
            },
            {
              resourceType: 'DATABASE',
              name: 'X推文内容',
            },
          ],
        },
      };
      const result = await AIWriterSO.quickWrite(writer, userInput, AIWriterContext, user);
      expect(result.value).toBeDefined();
      console.log('folder description', result);
    }
  });

  test('RESOURCE_DESCRIPTION: 自动化节点的描述生成', async () => {
    if (TEST_AI) {
      const userInput = `一键实现从数据表获取推文并在推特上发布`;
      const writer: AIWriter = {
        type: 'RESOURCE_DESCRIPTION',
        resource: {
          resourceType: 'AUTOMATION',
          name: '定时发推文',
          triggers: [
            {
              triggerType: 'MANUALLY',
              // description: '手动点击时才触发',
            },
          ],
          actions: [
            {
              actionType: 'FIND_RECORDS',
              description: '从数据表中获取推文',
            },
            {
              actionType: 'X_CREATE_TWEET',
              description: '使用授权账号创建推文',
            },
          ],
        },
      };
      const result = await AIWriterSO.quickWrite(writer, userInput, AIWriterContext, user);
      expect(result.value).toBeDefined();
      console.log('folder description', result);
    }
  });

  test('RESOURCE_DESCRIPTION: 数据表的描述生成', async () => {
    if (TEST_AI) {
      const userInput = `您可以使用该模板，实现 AI 自动发布 X(Twitter) 推文，读取数据表中准备好的推文资料，自动发布推文，帮助您提高社交媒体的曝光度，增加粉丝互动。`;
      const writer: AIWriter = {
        type: 'RESOURCE_DESCRIPTION',
        resource: {
          resourceType: 'DATABASE',
          name: 'X 推文内容',
          fields: [
            {
              name: '内容',
              type: 'LONG_TEXT',
            },
            {
              name: '发布日期',
              type: 'DATETIME',
            },
          ],
        },
      };
      const result = await AIWriterSO.quickWrite(writer, userInput, AIWriterContext, user);
      expect(result.value).toBeDefined();
      console.log('folder description', result);
    }
  });

  // 多语言翻译
  test('Test AI Writer: I18N_STRING', async () => {
    if (TEST_AI) {
      const resultI18N = await AIWriterSO.quickWrite(
        {
          type: 'I18N_STRING',
          langs: ['en', 'zh-TW'],
        },
        '一只猪和一只狗在爬树',
        AIWriterContext,
        user,
      );

      console.log('resultI18N', resultI18N);
      expect(resultI18N.data.en).includes('dog');
      expect(resultI18N.data.ja).toBeUndefined();
      expect(resultI18N.data['zh-TW']).includes('狗');
      expect(iStringParse(resultI18N.data, 'en')).includes('dog');
    }
  });

  test('EXTRACT_TO_RECORD: 提取结构化数据', async () => {
    if (TEST_AI) {
      const userInput = `昨天在东莞的中国AI论坛峰会现场，认识了深圳维维安科技有限公司的CEO李三思，他们公司在深圳南山区深圳湾生态科技园，公司有50多号人。他们是一家专注于AI安全的公司，主要产品是AI安全防护系统，可以保护企业的AI系统免受黑客攻击。`;
      const writer: AIWriter = {
        type: 'EXTRACT_TO_RECORD',
        fields: [
          {
            name: '公司名称',
            type: 'SINGLE_TEXT',
          },
          {
            name: '线索来源',
            type: 'SINGLE_SELECT',
            property: {
              options: [
                { id: 'opt1001', name: '网页客服' },
                { id: 'opt1002', name: '自媒体' },
                { id: 'opt1003', name: '线上活动' },
                { id: 'opt1004', name: '线下活动' },
              ],
            },
          },
          {
            name: '公司所在地址',
            type: 'SINGLE_TEXT',
          },
          {
            name: '公司规模',
            type: 'NUMBER',
          },
          {
            name: '备注',
            type: 'LONG_TEXT',
          },
        ],
      };
      const result = await AIWriterSO.quickWrite(writer, userInput, AIWriterContext, user);
      console.log('EXTRACT_TO_RECORD result', result);
      expect(result.value).toBeDefined();
      console.log('result', result);
    }
  });

  test('EXTRACT_TO_RECORD: 信息不全的边界测试', async () => {
    if (TEST_AI) {
      const userInput = `昨天在东莞的中国AI论坛峰会现场，认识了深圳维维安科技有限公司的CEO李三思。他们是一家专注于AI安全的公司，主要产品是AI安全防护系统，可以保护企业的AI系统免受黑客攻击。`;
      const writer: AIWriter = {
        type: 'EXTRACT_TO_RECORD',
        fields: [
          {
            name: '公司名称',
            type: 'SINGLE_TEXT',
          },
          {
            name: '线索来源',
            type: 'SINGLE_SELECT',
            property: {
              options: [
                { id: 'opt1001', name: '网页客服' },
                { id: 'opt1002', name: '自媒体' },
                { id: 'opt1003', name: '线上活动' },
                { id: 'opt1004', name: '线下活动' },
              ],
            },
          },
          {
            name: '公司所在地址',
            type: 'SINGLE_TEXT',
          },
          {
            name: '公司规模',
            type: 'NUMBER',
          },
          {
            name: '备注',
            type: 'LONG_TEXT',
          },
        ],
      };
      const result = await AIWriterSO.quickWrite(writer, userInput, AIWriterContext, user);
      expect(result.value).toBeDefined();
      if (result.data) {
        const parsedResult = result.data;
        expect(parsedResult['公司规模']).toBe(0);
        console.log('result', result);
      } else {
        throw new Error('Result value is undefined');
      }
    }
  });

  test('GENERATE_MOCK_RECORD: 生成模拟的行记录', async () => {
    if (TEST_AI) {
      const userInput = 'Create mock record';
      const writer: AIWriter = {
        type: 'GENERATE_MOCK_RECORD',
        fields: [
          {
            name: '公司名称',
            type: 'SINGLE_TEXT',
          },
          {
            name: '线索来源',
            type: 'SINGLE_SELECT',
            property: {
              options: [
                { id: 'opt1001', name: '网页客服' },
                { id: 'opt1002', name: '自媒体' },
                { id: 'opt1003', name: '线上活动' },
                { id: 'opt1004', name: '线下活动' },
              ],
            },
          },
          {
            name: '公司所在地址',
            type: 'SINGLE_TEXT',
          },
          {
            name: '公司规模',
            type: 'NUMBER',
          },
          {
            name: '备注',
            type: 'LONG_TEXT',
          },
          {
            name: '录入时间',
            type: 'DATETIME',
          },
        ],
      };
      const result = await AIWriterSO.quickWrite(writer, userInput, AIWriterContext, user);
      expect(result.value).toBeDefined();
      console.log('result', result);
    }
  });
  test('EXTRACT_TO_RECORD: 验证日期类型的数据格式', async () => {
    if (TEST_AI) {
      const userInput =
        '3天后，我跟张总有一个线下会议，我会拜访他。他们公司叫“张强科技有限公司”，在深圳湾生态科技园，公司有50多号人。他们是一家专注于AI安全的公司，主要产品是AI安全防护系统，可以保护企业的AI系统免受黑客攻击。';
      const writer: AIWriter = {
        type: 'EXTRACT_TO_RECORD',
        fields: [
          {
            name: '公司名称',
            type: 'SINGLE_TEXT',
          },
          {
            name: '线索来源',
            type: 'SINGLE_SELECT',
            property: {
              options: [
                { id: 'opt1001', name: '网页客服' },
                { id: 'opt1002', name: '自媒体' },
                { id: 'opt1003', name: '线上活动' },
                { id: 'opt1004', name: '线下活动' },
              ],
            },
          },
          {
            name: '公司所在地址',
            type: 'SINGLE_TEXT',
          },
          {
            name: '公司规模',
            type: 'NUMBER',
          },
          {
            name: '备注',
            type: 'LONG_TEXT',
          },
          {
            name: '会议日期',
            type: 'DATETIME',
          },
        ],
      };

      AIWriterContext.createdAt = '2024-12-01T12:00:00Z';

      const result = await AIWriterSO.quickWrite(writer, userInput, AIWriterContext, user);
      expect(result.value).toBeDefined();
      console.log('result', result);
      if (result.data) {
        expect(result.data['会议日期']).toBe('2024-12-04T12:00:00Z');
      } else {
        throw new Error('Result value is undefined');
      }
    }
  });

  test('EXTRACT_TO_RECORD: 评分字段的最大值测试', async () => {
    if (TEST_AI) {
      const userInput = '这部音乐剧《黑天鹅》，我给满分10分。';
      const writer: AIWriter = {
        type: 'EXTRACT_TO_RECORD',
        fields: [
          {
            name: '电影或音乐剧名称',
            type: 'SINGLE_TEXT',
          },
          {
            name: '评分',
            description: 'This field is a number',
            type: 'RATING',
            property: {
              max: 5,
              icon: { type: 'EMOJI', emoji: '⭐️' },
            },
          },
        ],
      };

      AIWriterContext.createdAt = '2024-12-01T12:00:00Z';

      const result = await AIWriterSO.quickWrite(writer, userInput, AIWriterContext, user);
      expect(result.value).toBeDefined();
      console.log('result', result);
      if (result.data) {
        expect(result.data['评分']).toBe(5);
      } else {
        throw new Error('Result data is undefined');
      }
    }
  });

  // test('Test AI Completion', async () => {
  //   const stream = await AICompletionSO.test();
  //   for await (const partialObject of stream.partialObjectStream) {
  //     // console.clear();
  //     console.log(partialObject);
  //   }
  // });

  // 生成资源节点的描述信息
  test('RESOURCE_README: 文件夹README 生成', async () => {
    if (!TEST_AI) return;

    const userInput = `您可以使用该模板，实现 AI 自动发布 X(Twitter) 推文，读取数据表中准备好的推文资料，自动发布推文，帮助您提高社交媒体的曝光度，增加粉丝互动。`;
    const writer: AIWriter = {
      type: 'RESOURCE_README',
      folder: {
        resourceType: 'FOLDER',
        name: 'AI 自动发布 X 推文',
        children: [
          {
            resourceType: 'AUTOMATION',
            name: '定时发推文',
            description: '在特定时间发布推文。',
            triggers: [],
            actions: [],
          },
          {
            resourceType: 'DATABASE',
            name: 'X推文内容',
          },
        ],
      },
    };
    const result = await AIWriterSO.quickWrite(writer, userInput, AIWriterContext, user);
    expect(result).toBeDefined();
    expect(typeof result).toBe('object');
  });
});
