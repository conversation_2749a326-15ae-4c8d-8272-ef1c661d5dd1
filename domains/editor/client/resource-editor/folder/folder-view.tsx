import assert from 'assert';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import { parseTemplateAuthor } from '@bika/domains/node/client/utils';
import { useResourceEditorContext } from '@bika/types/editor/context';
import type { EditorNodeFolderDTO } from '@bika/types/editor/dto';
import { type AvatarLogo, type iString } from '@bika/types/system';
import { EditorScreenFolderSchema } from '@bika/types/template/type';
import { Button } from '@bika/ui/button';
import { ImageCropUpload, useAttachmentUpload } from '@bika/ui/components/image-crop-upload/index';
import { Stack, Box } from '@bika/ui/layouts';
import { FolderBoInput } from '@bika/ui/node/types-form/folder-bo-input';
import { StringInput } from '@bika/ui/shared/types-form/string-input';
import { Skeleton } from '@bika/ui/skeleton';
import { useSnackBar } from '@bika/ui/snackbar';
import { StackHeaderBarConfig } from '@bika/ui/stack-header-bar';
import { Typography } from '@bika/ui/texts';
import { FolderResourcesInput } from './folder-resources-input';
import { useDrawerForm } from '../../../../space/client/drawers/drawer-form-context';
import { useResourceEditorStore } from '../store';

/**
 * 模板文件夹、文件夹的编辑器
 *
 * 编辑器中的Node Folder编辑，是多态的，它可能是:
 *
 * - 本地编辑模式中 CustomTemplate -> 化身 EditorNodeResourceBO，是包含CustomTemplateBO
 * - space编辑环境中，可能是FolderBO、也可能是StoreTemplateVO；
 * - 当然，也可能是正常的NodeResource，如Database、Mirror、Automation等；
 *
 * @returns
 */
export function FolderEditor() {
  const editorContext = useResourceEditorContext();
  const { unsplashDownload } = useAttachmentUpload();
  const { hasError } = useResourceEditorStore();

  const editorProps = EditorScreenFolderSchema.parse(editorContext.getScreen());
  const [isSaving, setSaving] = useState(false);
  const { nodeId: folderId, parentId } = editorProps;

  const locale = useLocale();
  const { t, i } = locale;
  const { toast } = useSnackBar();

  const { setIsDirty } = useDrawerForm();

  // 虽然有isNew，但实际会走到Create Resource View哈
  const isNew = folderId === undefined || folderId === 'new';

  const [showImageUpload, setShowImageUpload] = useState(false);

  // Editor Folder，可能是folder，可能是template
  const { data: remoteFolderDetail, isLoading, refetch } = editorContext.api.folder.useFolderDetail(folderId);
  const mutate = editorContext.api.node.useNodeResourceBOMutation();
  const { isMutating } = mutate;

  const { setScope } = editorContext.api.node.useScope();

  // 每个form，用于编辑、修改，只有一个出口值。
  const [value, setValue] = React.useState<EditorNodeFolderDTO | undefined>();
  const [isChanged, setChanged] = React.useState<boolean>(false);

  useEffect(() => {
    if (remoteFolderDetail) {
      setScope(remoteFolderDetail.data.scope || 'SPACE');
      setValue(remoteFolderDetail);
    }
  }, [remoteFolderDetail, setScope]);

  const name = useMemo(() => {
    let theName: iString | undefined;
    if (isNew) theName = 'New Folder';
    else theName = value?.data.name || 'Loading...';
    return theName;
  }, [isNew, value]);

  const description = useMemo(() => value?.data.description, [value]);
  const readme = useMemo(() => value?.data.readme, [value]);

  const setCover = useCallback((newCover: AvatarLogo) => {
    setValue((oldValue) => {
      if (!oldValue) return oldValue;
      return {
        ...oldValue,
        data: {
          ...oldValue.data,
          icon: newCover,
        },
      };
    });
  }, []);

  const cover: AvatarLogo = useMemo(() => {
    const dataCover = value?.data.icon || value?.data.cover;
    if (dataCover) return dataCover as AvatarLogo;
    if (value?.nodeFolderType === 'TEMPLATE') {
      if (typeof value.template.cover === 'string') {
        return {
          type: 'URL',
          url: value.template.cover,
        };
      }
    }
    return {
      type: 'COLOR',
      color: 'BLUE',
    };
  }, [value]);

  const createFolder = useCallback(async () => {
    if (!parentId) {
      return;
    }
    setSaving(true);
    await mutate.create({
      parentId,
      data: {
        resourceType: 'FOLDER',
        name,
        readme,
        description,
      },
    });

    toast(t('resource.create_folder_success', { name: i(name) }), { variant: 'success' });
    setSaving(false);
    editorContext.back();
  }, [value]);

  const updateFolder = useCallback(async () => {
    assert(value);
    if (!folderId) {
      return;
    }
    setSaving(true);
    await mutate.update({
      id: folderId,
      data: {
        resourceType: 'FOLDER',
        name,
        readme,
        description,
        icon: cover,
      },
    });

    toast(t('resource.update_folder_success', { name: i(name) }), {
      variant: 'success',
    });
    setSaving(false);
    editorContext.back();
  }, [value, cover, description, i]);

  let title: string;
  if (value?.nodeFolderType === 'TEMPLATE') {
    title = t.resource.edit_template;
  } else if (isNew) {
    title = t.resource.title_create_folder;
  } else {
    title = t.resource.title_update_folder;
  }

  if (isLoading) {
    return <Skeleton pos="EDITOR_FOLDER" />;
  }

  return (
    <Box>
      <Box
        sx={{
          m: 2,
          height: 'calc(100vh - 152px)',
          overflowY: 'auto',
          overflowX: 'hidden',
        }}
      >
        <StackHeaderBarConfig
          title={title}
          rightControls={
            folderId && (
              <Button
                onClick={() => {
                  editorContext.pushScreen({
                    screenType: 'ARCHITECTURE',
                    folderId,
                  });
                }}
                loading={isMutating || isLoading}
                sx={{
                  mr: 1,
                }}
              >
                {t.settings.space.workflow}
              </Button>
            )
          }
        />

        {value && (
          <FolderBoInput
            value={value}
            cover={cover}
            onChange={(newVal) => {
              setIsDirty(true);
              setValue(newVal);
              setChanged(true);
            }}
            locale={locale}
            setShowImageUpload={setShowImageUpload}
          />
        )}

        {value && <FolderResourcesInput value={value} locale={locale} folderId={folderId} />}

        {value?.nodeFolderType === 'TEMPLATE' && (
          <Stack spacing={2} sx={{ my: 2 }}>
            <Typography level="h6" textColor="var(--text-secondary)">
              {t.resource.node_detail}
            </Typography>
            <StringInput
              label={t.resource.template_creator}
              disabled={
                true
                // typeof template.author !== 'string'
              }
              value={parseTemplateAuthor(value?.template).name}
              onChange={(_newVal) => {
                alert('todo change author');
              }}
            />
          </Stack>
        )}
      </Box>
      <Box
        sx={{
          textAlign: 'center',
          height: '72px',
          pt: 2,
          borderTop: '1px solid var(--border-default)',
        }}
      >
        <Button
          disabled={!isChanged || hasError()}
          onClick={() => {
            setIsDirty(false);
            if (isNew) {
              createFolder();
            } else {
              updateFolder();
            }
          }}
          loading={isMutating || isSaving}
          sx={{
            minWidth: '200px',
          }}
          size="lg"
        >
          {t.buttons.save}
        </Button>
      </Box>
      {showImageUpload && (
        <ImageCropUpload
          upload={async (file: File) => editorContext.api.folder.uploadFolderLogo({ file, filePrefix: 'folder' })}
          config={{
            unsplash: {
              unsplashDownload,
            },
          }}
          onClose={() => {
            setShowImageUpload(false);
          }}
          avatarName={i(name)}
          // type={cover?.type}
          confirm={(folderCover) => {
            setShowImageUpload(false);
            setCover(folderCover);
            setChanged(true);
          }}
          value={cover}
          // initPreview={<AvatarImg name={i(name)} avatar={cover} customSize={AvatarSize.Size120} shape="SQUARE" />}
        />
      )}
    </Box>
  );
}
