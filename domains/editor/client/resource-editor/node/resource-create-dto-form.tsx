import React, { use<PERSON>emo, useState, use<PERSON>allback, useEffect } from 'react';
import { getResourcesTypesConfig } from '@bika/contents/config/client/node/node-resources';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import { MirrorBOInput } from '@bika/domains/mirror/client/mirror-bo-input';
import { FileNodeBOSchema } from '@bika/types/document/bo';
import { useResourceEditorContext } from '@bika/types/editor/context';
import { MirrorBOSchema, type MirrorCreateClientBO } from '@bika/types/node/bo';
import { defaultResourceCreateDTO } from '@bika/types/node/default';
import {
  type ResourceCreateDTO,
  ResourceCreateDTOSchema,
  type NodeCreateDTOWithoutSpaceId,
} from '@bika/types/node/dto';
import { EditorScreenNodeResourceSchema } from '@bika/types/template/type';
import { SurveyInput } from '@bika/ui/admin/types-form/survey-input';
import { But<PERSON> } from '@bika/ui/button';
import { DatabaseViewSelect } from '@bika/ui/database/types-form/database-view-select';
import { FileUploadAndDisplayBOInput } from '@bika/ui/file/types-form/file-upload-and-display-bo-input';
import { Box, Stack } from '@bika/ui/layouts';
import { getNodeResourceTypesOptions } from '@bika/ui/node/types-form/node-constants';
import { NodeResourceSelect } from '@bika/ui/node/types-form/node-resource-select';
import { NameDescriptionBOInput } from '@bika/ui/shared/types-form/name-description-bo-input';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';
import { StackHeaderBarConfig } from '@bika/ui/stack-header-bar';
import { useDrawerForm } from '../../../../space/client/drawers/drawer-form-context';
import { useResourceEditorStore } from '../store';

interface ResourceCreateDTOFormProps {
  locale: ILocaleContext;
}

type CreateResourceType = ResourceCreateDTO['resourceType'];

export function ResourceCreateDTOForm(props: ResourceCreateDTOFormProps) {
  const editorContext = useResourceEditorContext();
  const { hasError, updateError } = useResourceEditorStore();
  const editorProps = EditorScreenNodeResourceSchema.parse(editorContext.getScreen());

  const parentId = editorProps.parentId!;

  const { locale } = props;
  const { resourceType } = editorProps;
  const isRedirect = editorProps.isRedirect === 'true';

  const { setIsDirty } = useDrawerForm();

  const createDefaultValue = (newResType: CreateResourceType): ResourceCreateDTO =>
    defaultResourceCreateDTO(newResType, locale.lang);
  const { t } = locale;
  // get default
  const [value, setValueOriginal] = useState<ResourceCreateDTO>(
    createDefaultValue((resourceType as CreateResourceType) || 'DATABASE'),
  );

  // global scope todo refactor
  const { setScope } = editorContext.api.node.useScope();
  const { data: parent } = editorContext.api.node.useNodeResourceBO(
    parentId.startsWith('private_') ? undefined : parentId,
  );
  useEffect(() => {
    if (parentId.startsWith('private_')) {
      setScope('PRIVATE');
    } else if (parent) {
      setScope(parent.scope || 'SPACE');
    }
  });

  const setValue = useCallback((newValue: ResourceCreateDTO | ((prev: ResourceCreateDTO) => ResourceCreateDTO)) => {
    // 在这里添加你的处理逻辑
    setValueOriginal((prev) => {
      const nextValue = typeof newValue === 'function' ? newValue(prev) : newValue;
      setIsDirty(true);
      return nextValue;
    });
  }, []);

  const resourceTypesConfig = useMemo(() => getResourcesTypesConfig(locale), [locale]);
  const nodeTypesOptions = useMemo(() => getNodeResourceTypesOptions(locale), [locale]);

  const mutate = editorContext.api.node.useNodeResourceBOMutation();
  const [isCreating, setIsCreating] = useState(false);

  const createNode = async () => {
    if (!parentId) {
      console.error('parentId is required');
      return;
    }
    setIsCreating(true);
    const createNodeDTO: NodeCreateDTOWithoutSpaceId['data'] = ResourceCreateDTOSchema.parse(value);

    try {
      await mutate.create({ parentId, data: createNodeDTO }, isRedirect);
    } catch (e) {
      console.error('create node failed with error:', e);
    } finally {
      setIsCreating(false);
    }

    if (!isRedirect) {
      editorContext.back();
    }
  };

  const generalInputs = (
    <>
      <NameDescriptionBOInput
        locale={props.locale}
        labels={{
          name: t.resource.title_resource_name,
          description: t.resource.title_resource_description,
        }}
        value={{
          name: value.name,
          description: value.description,
        }}
        onChange={(newData) => {
          setValue({ ...value, ...newData });
        }}
        setErrors={updateError}
      />

      {/* 选择resourceType */}
      <SelectInput
        label={t.resource.title_resource_type}
        options={nodeTypesOptions}
        value={resourceType || value.resourceType}
        disabled={resourceType !== undefined}
        onChange={(newValue) => {
          // 切换类型？重新parse默认值
          const newDTO = createDefaultValue(newValue! as ResourceCreateDTO['resourceType']);

          setValue(newDTO);
        }}
      />
    </>
  );

  let boMoreInput = null;

  if (value.resourceType === 'MIRROR') {
    const mirror = MirrorBOSchema.parse(value);
    boMoreInput = (
      <MirrorBOInput
        value={mirror}
        locale={props.locale}
        onChange={(newVal) => {
          setValue(newVal as MirrorCreateClientBO);
        }}
        api={editorContext.api}
      />
    );
  } else if (value.resourceType === 'FORM') {
    boMoreInput = (
      <>
        <Stack spacing={2} sx={{ mt: 2 }}>
          <NodeResourceSelect
            resourceType="DATABASE"
            locale={props.locale}
            resourceId={value.databaseId}
            setResourceId={(id: string) => {
              setValue({ ...value, databaseId: id });
            }}
            required={true}
            label={t.resource.title_database_id}
            setErrors={updateError}
          />
        </Stack>
        <Stack spacing={2} sx={{ mt: 2 }}>
          <DatabaseViewSelect
            label={t.resource.title_view_id}
            setErrors={updateError}
            required={true}
            api={editorContext.api}
            locale={props.locale}
            databaseId={value.databaseId}
            viewId={value.viewId}
            setViewId={(id: string) => {
              setValue({ ...value, viewId: id });
            }}
          />
        </Stack>
      </>
    );
  } else if (value.resourceType === 'FILE') {
    const _value = FileNodeBOSchema.parse(value);
    boMoreInput = (
      <Stack spacing={2} sx={{ mt: 2 }}>
        <FileUploadAndDisplayBOInput
          label={'附件'}
          setErrors={updateError}
          required={true}
          locale={props.locale}
          api={editorContext.api}
          value={_value}
          setValue={(newVal) => {
            setValue(newVal);
          }}
        />
      </Stack>
    );
  }

  return (
    <Box>
      <Box
        sx={{
          mx: 2,
          height: 'calc(100vh - 136px)',
          overflowY: 'auto',
          overflowX: 'hidden',
        }}
      >
        <StackHeaderBarConfig title={t.resource.title_new_resource} />

        {/* 通用组件 */}
        {generalInputs}

        {resourceTypesConfig[value.resourceType].display === 'COMING_SOON' && (
          <SurveyInput surveyType={'COMING_SOON_NODE_RESOURCE'} />
        )}

        {resourceTypesConfig[value.resourceType].display === 'SHOW' && (
          <>
            {/* 输出控件 */}
            {boMoreInput}
          </>
        )}
      </Box>
      <Box
        sx={{
          textAlign: 'center',
          pt: 2,
          borderTop: '1px solid var(--border-default)',
        }}
      >
        <Button
          onClick={() => createNode()}
          loading={isCreating}
          disabled={hasError() || resourceTypesConfig[value.resourceType].display === 'COMING_SOON'}
          sx={{
            minWidth: '200px',
          }}
          size="lg"
        >
          {t.buttons.save}
        </Button>
      </Box>
    </Box>
  );
}
