import _ from 'lodash';
import React from 'react';
import { VIDEO_CONFIG } from '@bika/contents/config/client';
import { useLocale } from '@bika/contents/i18n';
import { useSpaceContextForce } from '@bika/types/space/context';
import type { EditorScreenProps } from '@bika/types/template/type';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';
import HistoryOutlined from '@bika/ui/icons/components/history_outlined';
import InfoCircleOutlined from '@bika/ui/icons/components/info_circle_outlined';
import VideoOutlined from '@bika/ui/icons/components/video_outlined';
import type { INodeVOMenuProps } from './types';
import { showGuideVideo } from '../../../space/client/sidebar/show-guide-video';
import { HelpVideoIframe } from '../../../website/client/help-video/help-video-iframe';

/**
 * Node资源的菜单
 *
 * @param locale
 * @returns
 */
export function useNodeVOMenuOfNodeResources(props: INodeVOMenuProps) {
  const { t, lang } = useLocale();

  const spaceContext = useSpaceContextForce();

  const node = props.value;

  const { permission, id: nodeId } = node;

  const isManager = permission?.privilege === 'FULL_ACCESS';

  const isGuest = spaceContext?.myInfo?.isGuest;

  // 编辑
  return _.compact([
    // permission不允许为空, 如果你想显示该菜单, 请给一个默认值
    permission &&
      isManager &&
      !isGuest && {
        label: t.buttons.edit,
        icon: <EditOutlined color={'var(--text-secondary)'} />,
        onClick: () => {
          let screenProps: EditorScreenProps;

          switch (node.type) {
            case 'AUTOMATION':
              screenProps = {
                screenType: 'NODE_RESOURCE',
                resourceType: 'AUTOMATION',
                nodeId: node.id,
              };
              break;
            case 'FOLDER':
            case 'TEMPLATE':
              screenProps = {
                screenType: 'FOLDER',
                nodeId: node.id,
              };
              break;
            case 'DATABASE':
              screenProps = {
                screenType: 'NODE_RESOURCE',
                resourceType: 'DATABASE',
                nodeId: node.id,
              };
              break;
            // case 'VIEW':
            //   screenProps = {
            //     screenType: 'NODE_RESOURCE',
            //     resourceType: 'VIEW',
            //     // idType: 'NODE_RESOURCE',
            //     nodeId: node.id,
            //   };
            //   break;
            case 'FORM':
              screenProps = {
                screenType: 'NODE_RESOURCE',
                resourceType: 'FORM',
                nodeId: node.id,
              };
              break;
            case 'MIRROR':
              screenProps = {
                screenType: 'NODE_RESOURCE',
                resourceType: 'MIRROR',
                nodeId: node.id,
              };
              break;
            case 'DASHBOARD':
              screenProps = {
                screenType: 'NODE_RESOURCE',
                resourceType: 'DASHBOARD',
                nodeId: node.id,
              };
              break;
            case 'DOCUMENT':
              screenProps = {
                screenType: 'NODE_RESOURCE',
                resourceType: 'DOCUMENT',
                nodeId: node.id,
              };
              break;
            case 'FILE':
              screenProps = {
                screenType: 'NODE_RESOURCE',
                resourceType: 'FILE',
                nodeId: node.id,
              };
              break;
            case 'AI':
              screenProps = {
                screenType: 'NODE_RESOURCE',
                resourceType: 'AI',
                nodeId: node.id,
              };
              break;
            case 'PAGE':
              screenProps = {
                screenType: 'NODE_RESOURCE',
                resourceType: 'PAGE',
                nodeId: node.id,
              };
              break;
            default:
              screenProps = {
                screenType: 'HOME',
                rootNodeId: nodeId,
              };
              break;
          }
          spaceContext?.showUIDrawer({
            type: 'resource-editor',
            props: screenProps,
          });
        },
      },
    node.type === 'AI' &&
      isManager && {
        label: t.global.copilot.history,
        icon: <HistoryOutlined color={'var(--text-secondary)'} />,
        onClick: () => {
          spaceContext?.showUIDrawer({
            type: 'ai-history',
            props: {
              type: 'AI_NODE',
            },
          });
        },
      },

    node.type === 'AUTOMATION' &&
      isManager && {
        label: t.automation.history,
        icon: <HistoryOutlined color={'var(--text-secondary)'} />,
        onClick: () => {
          spaceContext?.showUIDrawer({
            type: 'automation-history',
            automationId: nodeId,
          });
        },
      },

    (permission === undefined || permission?.privilege === 'CAN_EDIT' || permission?.privilege === 'FULL_ACCESS') &&
      node.type !== 'ROOT' && {
        label: t.node.node_info,
        icon: <InfoCircleOutlined color={'var(--text-secondary)'} />,
        onClick: async () => {
          spaceContext?.showUIModal({
            type: 'node-info',
            resourceType: node.type,
            nodeId,
          });
        },
      },

    VIDEO_CONFIG[lang] &&
      VIDEO_CONFIG[lang][node.type] && {
        label: t.tutorial,
        icon: <VideoOutlined color={'var(--text-secondary)'} />,
        onClick: async () => {
          // 获取当前语言的视频配置
          const videoConfig = VIDEO_CONFIG[lang];
          const nodeVideoConfig = videoConfig[node.type];

          showGuideVideo({
            t,
            items: [
              {
                key: node.type,
                title: nodeVideoConfig.title,
                content: <HelpVideoIframe url={nodeVideoConfig.path} title={t.tutorial} />,
              },
            ],
            id: `node-tutorial-${nodeId}`, // 使用节点ID确保唯一性
          });
        },
      },
  ]);
}
