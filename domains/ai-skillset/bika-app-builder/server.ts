import assert from 'assert';
import { z } from 'zod';
import * as No<PERSON><PERSON>ontroller from '@bika/domains/node/apis/node-controller';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { AiNodeBO } from '@bika/types/ai/bo';
import { SkillsetSelectBO, SkillsetSelectBOSchema } from '@bika/types/skill/bo';
import { AIArtifactSO } from '../../ai-artifacts/ai-artifact-so';
import { AISkillsetServerRegistry } from '../server-registry';
import { ToolSetHandler } from '../types';
import { createToolCallRequestContext } from '../utils';

// default tools, usually no server-side toolset needed
const tools: ToolSetHandler = async ({ user, dataStreamWriter }) => ({
  // 'bika-ai-app-builder-space-id': {
  //   description: `A UI confirmation for the user to confirm the spaceId. <PERSON><PERSON><PERSON> needs the 'spaceId' to install the app.`,
  //   parameters: z.object({
  //     userPrompt: z.string().describe('Example: I want to build a crm'),
  //   }),
  // client-side only execute
  // execute: async (params, { toolCallId }) => ({}),
  // },

  'bika-ai-app-builder-planner': {
    description: `Bika AI App Builder - Planner.
Sequential thinking and deep thinking. Then, generate requirement documentation for building agentic apps and AI agents.`,
    parameters: z.object({
      // reasoning
      user_prompt_reasoning: z.string().describe('The sequential thinking from user prompts'),
    }),
    execute: async (params, { toolCallId }) => {
      const artifact = await AIArtifactSO.create(user, {
        type: 'text',
        toolCallId,
        prompt: {
          system: `You are a Bika.ai, AI Agents designer and planner.

Bika.ai is the first AI Organizer platform that can help you build your own agentic AI team combining AI agents, automation, databases, dashboards, and documents.

Base on the and user's needs (user prompt), generate a design chapter paragraph.

## Rules:
- ≈1000 words
- Add some mermaid flow charts or mindmaps
`,
          prompt: params.user_prompt_reasoning,
        },
      });
      await artifact.getValue(user, { dataStreamWriter });
      return { artifactId: artifact.id };
    },
  },
  'bika-ai-app-builder-engineer': {
    description: `AI Consulting Team - Engineer, to generate JSON object of the app.
    ## Rules:
    - always execute this tool immediately after 'bika-ai-app-builder-planner' tool
    - the agentSkills parameter is an array of objects, each object has a category and skills property
    - category: used to search for skillsets in the registry, must be a single noun with first letter capitalized, no compound words allowed
    - skills: specific skill keywords to match skills in the skillset, use single nouns, use underscore for multi-word terms

    ## agentSkills example:
    if the user prompt is "I want to publish a blog in twitter", 
    the agentSkills params would be:
    [{
      "category": "Twitter",
      "skills": ["post", "blog", "auth"]
    }]
`,
    parameters: z.object({
      plannerArtifactId: z.string().describe('The artifactId from the planner tool result'),
      agentName: z.string().describe('The name of the agent'),
      agentDescription: z.string().describe('The description of the agent'),
      agentSkills: z
        .array(
          z.object({
            category: z.string().describe('skill category, used to search for skillsets'),
            skills: z.array(z.string()).describe('skill keywords, used to match skills in the skillset'),
          }),
        )
        .describe('The skills of the agent'),
      agentPrompt: z
        .string()
        .optional()
        .describe('The System prompt of the agent, describe the agent main task, use markdown format'),
      // templateId: z.string(),
      // search_queries: z
      //   .array(z.string())
      //   .describe(`The search queries and keywords base on planner's user_prompt_reasoning, for searching template`),
      // userPrompt: z.string(),
    }),
    execute: async (params, { toolCallId }) => {
      const plannerArtifact = await AIArtifactSO.getById(params.plannerArtifactId);
      assert(plannerArtifact, 'Planner artifact not found');
      // get planner
      assert(plannerArtifact.type === 'text', 'Planner artifact type must be text');
      const v = await plannerArtifact.getValue(user, { dataStreamWriter });
      assert(v.type === 'text', 'Planner artifact value type must be text');

      const { agentName, agentDescription, agentSkills, agentPrompt } = params;

      const skillsetsSelects: SkillsetSelectBO[] = [];
      for (const { category, skills } of agentSkills) {
        const searchResult = await AISkillsetServerRegistry.search(user, {
          query: category,
        });
        if (searchResult.data.length === 0) {
          console.error('No skillset found for category', category);
          continue;
        }

        for (const skillsetVO of searchResult.data) {
          const hitSkills = skillsetVO.skills.filter((skill) =>
            skills.some(
              (s: string) =>
                skill.key.includes(s) || skill.name.includes(s) || skill.description?.includes(s.replace(/_/g, ' ')),
            ),
          );
          const { success, data, error } = SkillsetSelectBOSchema.safeParse({
            kind: skillsetVO.kind,
            key: skillsetVO.key,
            configuration: skillsetVO.configuration,
            includes: hitSkills.map((skill) => skill.key),
          });
          if (success) {
            skillsetsSelects.push(data);
            // 找到了一个，之后类似的就不找了 // todo 放到es 之后，用score找到最匹配的
            break;
          } else {
            // 继续遍历
            console.error('SkillsetSelectBOSchema parse error', skillsetVO, error);
          }
        }
      }

      if (skillsetsSelects.length === 0) {
        // 直接抛出错误，ai检查参数
        throw new Error(`No skillsets found for agentSkills, please check the agentSkills parameter`);
      }

      const agent: AiNodeBO = {
        resourceType: 'AI',
        name: agentName,
        description: agentDescription,
        skillsets: skillsetsSelects,
        prompt: agentPrompt,
      };

      const artifact = await AIArtifactSO.create(user, {
        type: 'ai-agent',
        toolCallId,
        prompt: {
          system: `You are a Bika.ai AI Agent Engineer`,
          prompt: '',
        },
        initData: agent,
        state: 'SUCCESS',
      });
      return { artifactId: artifact.id };
    },
  },

  'bika-ai-app-builder-installer': {
    description: 'Do install the agentic app or ai agent, must be execute after engineer',
    parameters: z.object({
      spaceId: z.string().describe('The spaceId to install the app'),
      engineerArtifactId: z.string().describe('The artifactId of the engineer result'),
    }),
    execute: async (params, { toolCallId }) => {
      const { spaceId, engineerArtifactId } = params;
      const engineerArtifact = await AIArtifactSO.getById(engineerArtifactId);
      assert(engineerArtifact, 'Engineer artifact not found');
      assert(engineerArtifact.type === 'ai-agent', 'Engineer artifact type must be ai-agent');
      const agent = engineerArtifact.data as AiNodeBO;
      const rootNode = await NodeSO.getRootNode(spaceId);
      const node = await NodeController.createResource(createToolCallRequestContext(user, toolCallId), {
        spaceId,
        parentId: rootNode.id,
        data: agent,
      });
      return {
        nodeId: node.id,
        name: node.name,
      };
    },
  },
});
export default tools;
