import assert from 'assert';
import { z } from 'zod';
import { AiPageNodeBO } from '@bika/types/ai/bo';
import { Slides } from '@bika/types/ai/vo';
import type { DocumentBO } from '@bika/types/document/bo';
import { Folder, NodeResource } from '@bika/types/node/bo';
import { ToolSetHandler } from '../types';
import { ImageSkillsetName } from './types';
import { AISO } from '../../ai/server';
import { AIArtifactSO, AIArtifactDTO } from '../../ai-artifacts/ai-artifact-so';
import { NodeSO } from '../../node/server/node-so';

// default 的，通常不需要 服务端 toolset
const tools: ToolSetHandler = async (props) => {
  //
  // const image = await BikaImageToolSetHandler(props);
  // console.log('todo');
  assert(true);

  return {
    // 请求澄清，当用户的需求不明确时，使用此工具请求澄清，智能生成 UI 界面，让用户点击鼠标做选择
    // disambiguation: {
    //   description:
    //     'Request clarification when user requirements are unclear, use this tool to request clarification and intelligently generate UI interface (select or form) for user to make selections by clicking',
    //   parameters: z.object({}),
    //   execute: async () => {},
    // },

    //  加载附件
    load_attachment: {
      description: 'Analyze and describe the content of an attachment, or extract text from it using OCR',
      parameters: z.object({
        attachmentUrl: z.string().describe('The URL of the attachment to analyze or extract text from'),
      }),
      execute: async (params, { toolCallId }) => {
        try {
          console.log('🚀 ~ load_attachment executing:', params);
          const { attachmentUrl } = params;
          const text = await AISO.imageToText(attachmentUrl);

          // 创建 artifact 用于 server-artifact 机制
          const artifact = await AIArtifactSO.create(props.user, {
            type: 'image-text',
            toolCallId,
            initData: { text, imageUrl: attachmentUrl },
          });

          // 流式写入 artifact 数据
          const artifactVO = await artifact.getValue(props.user, {
            dataStreamWriter: props.dataStreamWriter,
          });

          // 返回 artifactId 供前端使用
          return { artifactId: artifactVO.id };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          throw new Error(`Failed to process attachment: ${errorMessage}`);
        }
      },
    },
    [ImageSkillsetName.image_to_text]: {
      description: 'Analyze and describe the content of an image, or extract text from it using OCR',
      parameters: z.object({
        imageUrl: z.string().describe('The URL of the image to analyze or extract text from'),
      }),
      execute: async (params, { toolCallId }) => {
        try {
          const { imageUrl } = params;
          const text = await AISO.imageToText(imageUrl);

          // 创建 artifact 用于 server-artifact 机制
          const artifact = await AIArtifactSO.create(props.user, {
            type: 'image-text',
            toolCallId,
            initData: { text, imageUrl },
          });

          // 流式写入 artifact 数据
          const artifactVO = await artifact.getValue(props.user, {
            dataStreamWriter: props.dataStreamWriter,
          });

          // 返回 artifactId 供前端使用
          return { artifactId: artifactVO.id };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          throw new Error(`Failed to process image: ${errorMessage}`);
        }
      },
    },

    read_node_content: {
      description: 'Get bika node content by nodeId. Only supports nodes with IDs starting with doc, aip, or fold.',
      parameters: z.object({
        nodeId: z.string().describe(`The node id must start with doc, aip, or fold`),
      }),
      execute: async (params, { toolCallId }) => {
        const { nodeId } = params;
        const user = props.user;
        const node = await NodeSO.init(nodeId);
        const bo = await node.toBO();
        const getArtifactCreateDTO = (): AIArtifactDTO => {
          let type: AIArtifactDTO['type'] | undefined;
          let initData: string | { html: string } | { slides: Slides } | NodeResource[] | undefined;
          if (bo.resourceType === 'DOCUMENT') {
            type = 'text';
            initData = (bo as DocumentBO).markdown;
          }
          if (bo.resourceType === 'PAGE') {
            const { data } = bo as AiPageNodeBO;
            if (data?.kind === 'SINGLE_HTML' && data.content) {
              type = 'html';
              initData = {
                html: data.content || '',
              };
            }
            if (data?.kind === 'SLIDES' && data.contents) {
              type = 'slides';
              initData = {
                slides: (data.contents || []) as unknown as Slides,
              };
            }
          }
          if (bo.resourceType === 'FOLDER') {
            type = 'node-resources';
            initData = (bo as Folder).children || [];
          }
          if (!type) {
            throw new Error(`Node resource type is not supported: ${bo.resourceType}`);
          }
          return {
            type,
            initData,
            state: 'SUCCESS',
            toolCallId,
            prompt: {
              system:
                'You are a helpful assistant that analyzes node resources and provides comprehensive summaries based on the artifact data.',
              prompt: `Analyze the following node resource and provide a detailed summary:\n\nResource Type: ${bo.resourceType}\nNode ID: ${nodeId}\n\nPlease provide a comprehensive analysis and summary of this resource based on the artifact data.`,
            },
          } as AIArtifactDTO;
        };
        const artifact = await AIArtifactSO.create(user, getArtifactCreateDTO());
        return {
          artifactId: artifact.id,
          error: artifact.error,
          data: artifact.data,
        };
      },
    },
  };
};
export default tools;
