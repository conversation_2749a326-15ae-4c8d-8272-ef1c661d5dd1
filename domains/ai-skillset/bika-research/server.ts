import { inputFieldsToZodSchema } from '@toolsdk.ai/sdk-ts/utils';
import type { Tool, ToolSet } from 'ai';
import { isInCI } from '@bika/types/system/app-env';
import { ToolSDKAISO } from '../../automation-nodes/toolsdk-action/toolsdk-ai-so';
import { ToolSetHandler } from '../types';

const fetchExaMCPToolSet = async (): Promise<ToolSet> => {
  const packageKey = 'exa-mcp-server';
  const packageVersion = undefined;
  const exa = await ToolSDKAISO.getPackage(packageKey, packageVersion);

  const companyResearchToolKey = 'company_research';
  const exaCompanyResearch = exa.tools.find((tool) => tool.key === companyResearchToolKey);
  if (!exaCompanyResearch) {
    return {};
  }
  const tool: Tool = {
    description:
      exaCompanyResearch.description ||
      'Comprehensive company research tool that crawls company websites to gather detailed information about businesses.',
    parameters: inputFieldsToZodSchema(exaCompanyResearch.inputFields),
    execute: async (params) =>
      ToolSDKAISO.runPackageTool({
        packageKey,
        packageVersion,
        envs: {
          EXA_API_KEY: process.env.EXA_API_KEY || '',
        },
        body: {
          toolKey: companyResearchToolKey,
          inputData: params,
        },
      }),
  };
  return { bika_company_research: tool };
};

const tools: ToolSetHandler = async () => {
  if (isInCI()) {
    return Promise.resolve({});
  }

  const exaToolSet = await fetchExaMCPToolSet();

  return {
    ...exaToolSet,
  };
};

export default tools;
