import { Typo<PERSON>, <PERSON>, <PERSON><PERSON>, Stack, Divider } from '@mui/joy';
import React, { useState } from 'react';
import { OnboardingRoles, useOnboardingTemplateMap } from '@bika/contents/config/client/space/onboarding-templates';
import { useLocale } from '@bika/contents/i18n/context';
import { TemplateSelector } from '@bika/domains/ai/client/launcher/space-home/templates-selector/templates-selector';
import { useCssColor } from '@bika/ui/colors';
import { Tooltip } from '@bika/ui/tooltip';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface OnboardingTemplatesComponentProps {
  handleChange: (_data: string[]) => void;
}

export const OnboardingTemplatesComponent: React.FC<OnboardingTemplatesComponentProps> = (props) => {
  const { t, i } = useLocale();

  const OnboardingTemplateMap = useOnboardingTemplateMap();

  const ExtendedOnboardingTemplateMap = {
    ...OnboardingTemplateMap,
    scratch: {
      // TODO add i18n
      name: t.wizard.build_scratch_name,
      description: t.wizard.build_scratch_description,
      templateIds: [],
    },
  } as const;

  const [submitted, setSubmitted] = useState(false);

  const colors = useCssColor();
  const [_role, setSelectedRole] = useState<keyof typeof ExtendedOnboardingTemplateMap>('ceo');

  const roleConfig = ExtendedOnboardingTemplateMap[_role];

  return (
    <Stack direction="column">
      <Stack justifyContent="center" className="w-full">
        <Typography level="h3" textColor="var(--text-primary)" textAlign={'center'}>
          {t.wizard.welcome_website}
        </Typography>
      </Stack>
      <Box marginTop="8px" paddingTop="24px">
        <Typography level="b2" textColor="var(--text-primary)">
          {t.wizard.select_role}
        </Typography>
      </Box>
      <Stack
        gap="16px"
        direction="row"
        sx={{
          marginTop: '30px',
          marginBottom: '36px',
          flexWrap: 'wrap',
        }}
      >
        {[...OnboardingRoles, 'scratch'].map((role) => {
          const currentRoleConfig = ExtendedOnboardingTemplateMap[role as keyof typeof ExtendedOnboardingTemplateMap];

          return (
            <Tooltip
              key={role}
              title={i(currentRoleConfig.description)}
              sx={{ zIndex: 11111 }}
              arrow
              placement="bottom-start"
            >
              <Box
                className="cursor-pointer"
                onClick={() => {
                  setSelectedRole(role as keyof typeof ExtendedOnboardingTemplateMap);
                }}
                sx={{
                  borderColor: role === _role ? 'var(--brand)' : 'var(--border-default)',
                  borderRadius: '8px',
                  borderStyle: 'solid',
                  borderWidth: '1px',
                  padding: '8px 16px',
                }}
              >
                <Typography level="b1" textColor="var(--text-primary)">
                  {i(currentRoleConfig.name)}
                </Typography>
              </Box>
            </Tooltip>
          );
        })}
      </Stack>

      <Box
        sx={{
          minHeight: '200px',
        }}
      >
        {_role === 'scratch' && (
          <Typography level="b4" sx={{ textAlign: 'center' }}>
            {t.wizard.choose_tailor_myself}
          </Typography>
        )}

        {_role !== 'scratch' && (
          <>
            <Divider
              sx={{
                marginBottom: '24px',
              }}
            >
              {t.wizard.selected_templates}
            </Divider>
            <TemplateSelector templateIds={[...(roleConfig?.templateIds ?? [])]} />
          </>
        )}
      </Box>

      <Button
        size="lg"
        loading={submitted}
        onClick={() => {
          setSubmitted(true);
          props.handleChange([...(roleConfig?.templateIds ?? [])]);
        }}
        sx={{
          color: colors.textStaticPrimary,
          backgroundColor: colors.bgBrandDefault,
          borderRadius: '8px',
          height: '48px',
          width: '100%',
        }}
      >
        {_role === 'scratch' ? t.welcome.get_started : t.template.install}
      </Button>
      {/* Template installer component content */}
    </Stack>
  );
};

export default OnboardingTemplatesComponent;
