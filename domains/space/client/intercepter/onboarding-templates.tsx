'use client';

import { useState, useEffect } from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { useInstaller } from '@bika/domains/ai-skillset/bika-app-builder/use-installer';
import { useSpaceContextForce } from '@bika/types/space/context';
import type { OnboardingPage } from '@bika/types/space/vo';
import { Modal } from '@bika/ui/modal';
import { useSnackBar } from '@bika/ui/snackbar';
import { OnboardingTemplatesComponent } from './onboarding-templates-component';
import { useBuildInstallGlobalStore } from './use-build-install-global-store';

export function OnboardingTemplates() {
  const trpcQuery = useTRPCQuery();
  const spaceContext = useSpaceContextForce();

  const updateSpace = trpcQuery.space.update.useMutation();

  const [installing, setInstalling] = useState(false);
  const installTemplates = useInstaller();

  const { toast } = useSnackBar();
  const { setData } = useBuildInstallGlobalStore();
  const { t } = useLocale();

  useEffect(() => {
    setData(undefined);
  }, [setData]);

  const handleUpdateOnboarding = async () => {
    const newOnboardings: OnboardingPage[] = [
      ...(spaceContext.data.settings.onboardings || []),
      'ONBOARDING_TEMPLATES',
    ];

    await updateSpace.mutateAsync({
      id: spaceContext.data.id,
      data: {
        settings: {
          onboardings: newOnboardings,
        },
      },
    });
  };

  const handleTemplateInstall = async (data: string[]) => {
    if (data?.length) {
      await installTemplates(data, () => {
        setInstalling(true);
      });
    } else {
      setInstalling(true);
    }
    // Complete onboarding after template installation
    await handleUpdateOnboarding();
  };

  if (installing) {
    return null;
  }
  return (
    <Modal
      closable={false}
      onClose={handleUpdateOnboarding}
      sx={{
        backgroundImage: `url(/assets/wizards/limit_modal_bg.png)`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        width: '820px',
        overflowY: 'hidden',
        paddingTop: '32px',
      }}
    >
      <OnboardingTemplatesComponent handleChange={handleTemplateInstall}></OnboardingTemplatesComponent>
    </Modal>
  );
}
