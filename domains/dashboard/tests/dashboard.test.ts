import { expect, test } from 'vitest';
import { TextWidgetSO } from '@bika/domains/dashboard-widgets/text-widget/server';
import { ChartWidgetVO } from '@bika/types/dashboard/vo';
import { MockContext } from '../../__tests__/mock';
import { DatabaseSO } from '../../database/server/database-so';
import { DashboardSO } from '../server/dashboard-so';

test('Test Dashboard', async () => {
  const { user, member, rootFolder } = await MockContext.initUserContext();
  const database = await rootFolder.createChildSimple(user, {
    resourceType: 'DATABASE',
    name: 'Test Database',
    templateId: 'databaseId',
    // databaseType: 'DATUM',
    views: [
      {
        name: 'view',
        type: 'TABLE',
        templateId: 'viewTemplateId',
      },
    ],
    fields: [
      {
        name: 'name',
        type: 'SINGLE_TEXT',
        templateId: 'fieldTemplateId1',
      },
      {
        name: 'age',
        type: 'AUTO_NUMBER',
        templateId: 'fieldTemplateId2',
        property: {
          nextId: 1,
        },
      },
    ],
  });
  const dbSO = await database.toResourceSO<DatabaseSO>();
  const view = await dbSO.firstView();

  const dashboardNodeSO = await rootFolder.createChildSimple(user, {
    name: 'Test Dashboard',
    description: 'This is a test dashboard',
    resourceType: 'DASHBOARD',
    widgets: [
      // {
      //   type: 'LIST',
      //   name: 'Test widget list',
      //   items: [
      //     {
      //       id: '1',
      //       name: 'Item 1',
      //       url: '/item/1',
      //     },
      //     {
      //       id: '2',
      //       name: 'Item 2',
      //       url: '/item/2',
      //     },
      //   ],
      // },
      {
        type: 'CHART',
        name: 'Test widget chart',
        datasource: {
          type: 'DATABASE',
          chartType: 'line',
          metricsType: 'COUNT_RECORDS',
          dimension: 'fieldTemplateId1',
          databaseId: database.id,
          viewId: view.id,
        },
      },
      {
        type: 'NUMBER',
        name: 'Test widget number',
        datasource: {
          type: 'DATABASE',
          metricsType: 'COUNT_RECORDS',
          metrics: {
            aggregationType: 'HIDDEN',
          },
        },
        // filters: {},
      },
      {
        type: 'EMBED',
        name: 'Test widget embed',
      },
      {
        type: 'TEXT',
        name: 'Test widget text',
        datasource: {
          type: 'CUSTOM',
          text: 'Hello, world!',
        },
      },
    ],
  });
  const dashboardSO = await dashboardNodeSO.toResourceSO<DashboardSO>();
  const dashboardVO = await dashboardSO.toVO();
  expect(dashboardVO.widgets.length).toBe(4);
  expect(dashboardVO.name).toBe('Test Dashboard');
  expect(dashboardVO.description).toBe('This is a test dashboard');

  const widgets = await dashboardSO.getWidgets();

  // find text widget
  let found = false;
  for (const widget of widgets) {
    if (widget.type === 'TEXT') {
      const textWidgetSOO = widget as TextWidgetSO;
      expect(textWidgetSOO.text).toBe('Hello, world!');
      found = true;
    }
  }
  expect(found).toBe(true);
});

test('Test Chart Dashboard', async () => {
  const { user, member, rootFolder } = await MockContext.initUserContext();
  const node = await rootFolder.createChildSimple(user, {
    resourceType: 'DATABASE',
    name: 'Test Database',
    templateId: 'databaseId',
    // databaseType: 'DATUM',
    views: [
      {
        name: 'All',
        type: 'TABLE',
        templateId: 'viewTemplateId',
      },
    ],
    fields: [
      {
        templateId: 'date',
        name: 'date',
        type: 'DATETIME',
        property: {
          dateFormat: 'YYYY-MM-DD',
          includeTime: false,
        },
      },
      {
        templateId: 'name',
        name: 'name',
        type: 'SINGLE_TEXT',
      },
    ],
  });
  const db = await node.toResourceSO<DatabaseSO>();
  const dimensionField = db.findFieldByFieldKey('date');
  const view = await db.firstView();

  await db.createRecords(user, member, [
    {
      date: '2020-01-01T00:00:00Z',
      name: 'A',
    },
    {
      date: '2020-01-01T00:00:00Z',
      name: 'B',
    },
    {
      date: '2020-01-02T00:00:00Z',
      name: 'B',
    },
  ]);

  const dashboardNode = await rootFolder.createChildSimple(user, {
    name: 'Test Dashboard',
    description: 'This is a test dashboard',
    resourceType: 'DASHBOARD',
    widgets: [
      {
        type: 'CHART',
        name: 'Test widget chart',
        datasource: {
          type: 'DATABASE',
          chartType: 'line',
          databaseId: db.id,
          viewId: view.id,
          dimension: dimensionField?.id,
          metricsType: 'COUNT_RECORDS',
        },
      },
    ],
  });
  const dashboard = await dashboardNode.toResourceSO<DashboardSO>();

  const vo = await dashboard.toVO();
  const widget = vo.widgets.find((w) => w.type === 'CHART') as ChartWidgetVO;

  expect(widget.value?.series).toEqual([{ type: 'line', data: ['1', '2'] }]);
  expect(widget.value?.xAxis.data).toEqual(['2020-01-02', '2020-01-01']);
});
