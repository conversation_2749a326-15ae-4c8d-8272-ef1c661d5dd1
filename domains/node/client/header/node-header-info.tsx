'use client';

import type React from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import { TalkVOPopover } from '@bika/domains/talk/client/talk-vo-popover';
import type { INodeIconValue } from '@bika/types/node/bo';
import type { AccessPrivilege } from '@bika/types/permission/bo';
import { useShareContext } from '@bika/types/space/context';
import type { iString } from '@bika/types/system';
import { useCssColor } from '@bika/ui/colors';
import { Popover, PopoverContent, PopoverTrigger } from '@bika/ui/components/popover/index';
import QuestionCircleOutlined from '@bika/ui/icons/components/question_circle_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { NodeIcon, type NodeResourceIconType } from '@bika/ui/node/icon';
import { EllipsisText } from '@bika/ui/text';
import { Typography } from '@bika/ui/texts';

// Permission description mapping - moved outside component as const
export const PERMISSION_DESCRIPTION_KEYS: Record<AccessPrivilege, string> = {
  FULL_ACCESS: 'FULL_ACCESS',
  CAN_EDIT: 'CAN_EDIT',
  CAN_EDIT_CONTENT: 'CAN_EDIT_CONTENT',
  CAN_COMMENT: 'CAN_COMMENT',
  CAN_VIEW: 'CAN_VIEW',
  NO_ACCESS: 'NO_ACCESS',
} as const;

// Permission color mapping - matches PermissionTag component colors
export const getPermissionColor = (permission: AccessPrivilege, colors: ReturnType<typeof useCssColor>) => {
  switch (permission) {
    case 'FULL_ACCESS':
      return colors.rainbowPurple5;
    case 'CAN_EDIT':
    case 'CAN_EDIT_CONTENT':
      return colors.rainbowTeal5;
    case 'CAN_VIEW':
      return colors.rainbowOrange5;
    case 'CAN_COMMENT':
    case 'NO_ACCESS':
    default:
      return colors.thirdLevelText;
  }
};

interface NodeHeaderInfoProps {
  name: string;
  nodeId: string;
  nodeType: NodeResourceIconType;
  description?: iString;
  permission?: AccessPrivilege;
  icon: INodeIconValue;
  onEdit?: () => void;
}

/**
 * NodeHeaderInfo component displays the node name, icon, description and permission tag.
 * It handles the click interaction to open the resource editor drawer.
 */
export const NodeHeaderInfo: React.FC<NodeHeaderInfoProps> = ({
  name,
  nodeId: _nodeId,
  nodeType,
  description,
  permission,
  icon,
  onEdit,
}) => {
  const { i } = useLocale();

  const { sharing } = useShareContext();

  if (!permission && sharing) {
    permission = 'FULL_ACCESS';
  }
  return (
    <Popover
      placement="bottom-start"
      hoverEnable={true}
      showArrow={true}
      hoverOptions={{
        delay: { open: 100, close: 600 },
        restMs: 100,
      }}
    >
      <PopoverTrigger asChild>
        <Box
          sx={{
            height: '45px',
            // width: '300px',
            maxWidth: '300px',
            // flex: '0 0 300px',
            marginTop: '2px',
            display: 'flex',
            alignItems: 'center',
            backgroundColor: 'var(--bg-surface)',
            cursor: 'pointer',
            '&:hover': {
              backgroundColor: 'var(--bg-hover)',
            },
          }}
        >
          <Stack
            direction={'row'}
            alignItems={'center'}
            sx={{
              '& > svg': {
                flexShrink: 0,
              },
            }}
          >
            <Stack
              direction={'row'}
              alignItems={'center'}
              sx={{
                maxWidth: '300px',
                marginRight: '12px',
              }}
            >
              <NodeIcon value={icon} size={32} title={name} />

              <EllipsisText tooltip={name}>
                <Typography textColor={'var(--text-primary)'} level={'h6'} sx={{ ml: '12px', flex: '1 1 auto' }}>
                  {i(name)}
                </Typography>
              </EllipsisText>
            </Stack>

            <QuestionCircleOutlined color={'var(--text-secondary)'} size={16} />
          </Stack>
        </Box>
      </PopoverTrigger>

      <PopoverContent>
        <TalkVOPopover
          icon={icon}
          nodeType={nodeType}
          name={name}
          description={description}
          permission={permission}
          onEdit={onEdit || (() => {})}
        />
      </PopoverContent>
    </Popover>
  );
};

// Backward compatibility export for DatabaseHeaderInfo
interface DatabaseHeaderInfoProps {
  name: string;
  databaseId: string;
  description?: iString;
  isView?: boolean;
  permission?: AccessPrivilege;
  onEdit?: () => void;
  icon: INodeIconValue;
}

export const DatabaseHeaderInfo: React.FC<DatabaseHeaderInfoProps> = ({
  name,
  databaseId,
  description,
  isView,
  permission,
  onEdit,
  icon,
}) => (
  <NodeHeaderInfo
    name={name}
    nodeId={databaseId}
    nodeType={'DATABASE'}
    description={description}
    permission={permission}
    onEdit={onEdit}
    icon={icon}
  />
);
