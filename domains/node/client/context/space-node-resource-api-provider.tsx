/* eslint-disable max-lines */
import { TRPCClientError } from '@trpc/client';
import React from 'react';
import toast from 'react-hot-toast';
import { useApiCaller } from '@bika/api-caller/context';
import { errors } from '@bika/contents/config/server/error/errors';
import { FeatureName } from '@bika/contents/config/server/pricing/feature-name';
import { useLocale } from '@bika/contents/i18n/context';
import {
  removeNodeChildrenItem,
  addItemOnRootNodeTree,
  updateNodeChildrenItem,
  removeItemOnRootNodeTree,
} from '@bika/domains/space/client/sidebar/utils/match-template-util';
import type { AIWriter } from '@bika/types/ai/bo';
import type { ActionState, ActionType, TriggerType } from '@bika/types/automation/bo';
import type {
  AutomationActionCreateDTO,
  AutomationActionUpdateDTO,
  AutomationTriggerCreateDTO,
  AutomationTriggerUpdateDTO,
} from '@bika/types/automation/dto';
import type { AutomationVariablesVO } from '@bika/types/automation/vo';
import type { WidgetCreateDTO, WidgetDeleteDTO, WidgetUpdateDTO } from '@bika/types/dashboard/dto';
import { DatabaseFieldWithId } from '@bika/types/database/bo';
import type {
  RecordListDTO,
  FieldCreateDTO,
  FieldUpdateDTO,
  ViewUpdateDTO,
  DatabaseViewCreateDTO,
  FieldGetDTO,
} from '@bika/types/database/dto';
import type { EmailBoxesDTO } from '@bika/types/email/dto';
import type { IntegrationListDTOWithoutSpaceID } from '@bika/types/integration/dto';
import type { Mission } from '@bika/types/mission/bo';
import type { NodeResourceScope } from '@bika/types/node/bo';
import { NodeResourceApiProvider } from '@bika/types/node/context';
import type {
  NodeCreateDTOWithoutSpaceId,
  NodeMoveDTOReqWithoutSpaceId,
  NodeSearchDTO,
  NodeUpdateDTOReqWithoutSpaceId,
} from '@bika/types/node/dto';
import type { NodeTreeVO } from '@bika/types/node/vo';
import { SkillsetSelectBO } from '@bika/types/skill/bo';
import { SkillsetSearchDTO } from '@bika/types/skill/dto';
import { useSpaceRouter, useSpaceContextForce } from '@bika/types/space/context';
import type { UnitSearch } from '@bika/types/unit/dto';
import { transformUsageLimitErrorData } from '@bika/types/utils';
import type { CommitSurveyDTO } from '@bika/types/website/dto';
import { useAttachmentUpload } from '@bika/ui/components/image-crop-upload/index';
import { formatVariables } from './utils';

type AutomationTestResult = ActionState['testResult'] & {
  actionType?: ActionType;
  triggerType?: TriggerType;
};

export function SpaceNodeResourceApiProvider(props: { children: React.ReactNode }) {
  // const globalContext = useGlobalContext();
  const locale = useLocale();
  const { t, i } = locale;
  // const stackContext = useStackNavigatorContext();
  // const stackRouter = stackContext.router;
  // const { toast } = useSnackBar();

  // const pushScreen = (screen: EditorScreenProps) => {
  //   stackRouter.push(screen.screenType, screen);
  // };
  // const { params: stackParams } = stackContext;
  // const getScreen = () => EditorScreenPropsSchema.parse(stackParams);
  /**
   * 自动化变量
   */
  const [automationVariables, setAutomationVariables] = React.useState<AutomationVariablesVO>();
  const [loadingVariables, setLoadingVariables] = React.useState(false);
  const [parentActionId, setParentActionId] = React.useState<string>();
  const [automationTestResult, setAutomationTestResult] = React.useState<Record<string, AutomationTestResult>>();
  const [resourceScope, setResourceScope] = React.useState<NodeResourceScope>('SPACE');

  const spaceContext = useSpaceContextForce();

  const {
    data: { id: spaceId },
    useRootNode,
    rootTeam,
  } = spaceContext;
  const { rootNode: privateRootNode, setRootNode: setPrivateRootNode } = useRootNode('PRIVATE');
  const { rootNode, setRootNode } = useRootNode();
  const [rootNodeId, setRootNodeId] = React.useState<string | undefined>(undefined);

  const { trpcQuery, trpc } = useApiCaller();
  const utils = trpcQuery.useUtils();
  const router = useSpaceRouter();

  const { mutateAsync: createMission } = trpcQuery.mission.create.useMutation();
  const { mutateAsync: createField, isLoading: isLoadingCreateField } = trpcQuery.database.createField.useMutation();
  const { mutateAsync: deleteField, isLoading: isLoadingDeleteField } = trpcQuery.database.deleteField.useMutation();
  const { mutateAsync: updateField, isLoading: isLoadingUpdateField } = trpcQuery.database.updateField.useMutation();
  const { mutateAsync: deleteView, isLoading: isLoadingDeleteView } = trpcQuery.database.deleteView.useMutation();
  const { mutateAsync: updateView, isLoading: isLoadingUpdateView } = trpcQuery.database.updateView.useMutation();
  const { mutateAsync: createView, isLoading: isLoadingCreateView } = trpcQuery.database.createView.useMutation();

  const isMutating =
    isLoadingCreateField ||
    isLoadingDeleteField ||
    isLoadingUpdateField ||
    isLoadingDeleteView ||
    isLoadingUpdateView ||
    isLoadingCreateView;
  const { upload: uploadAttachment } = useAttachmentUpload();

  // Create the provider value object outside the JSX to prevent excessive type recursion
  const nodeResourceApiValue = {
    // onChange: props.onChange,
    // slotComponents: props.slotComponents,

    automation: {
      updateAutomationVariables: async (automationId: string, parentId?: string) => {
        setParentActionId(parentId);
        setLoadingVariables(true);
        // reset automation variables
        setAutomationVariables(undefined);
        const data = await trpc.automation.fetchVariables.query({ automationId });
        setAutomationVariables(data);
        setLoadingVariables(false);
      },
      useTrigger: (triggerId?: string) =>
        trpcQuery.automation.getTrigger.useQuery(
          {
            triggerId: triggerId || '',
          },
          {
            enabled: !!triggerId,
          },
        ),

      // getFormAppAIAccountToken: () => trpcQuery.automation.fetchFormAppAIAccountToken.useQuery({ spaceId }),
      getToolSDKAIAccountToken: () => trpcQuery.automation.fetchToolSDKAIAccountToken.useQuery({ spaceId }),

      getAutomationGlobalVariables: (actionId?: string) =>
        loadingVariables ? undefined : formatVariables(automationVariables, locale, actionId, parentActionId),
      testAction: async (actionId: string, actionType?: ActionType) => {
        try {
          const rlt = await trpc.automation.testAction.mutate({ actionId });
          setAutomationTestResult((prev) => ({
            ...(prev || {}),
            [actionId]: {
              ...rlt,
              actionType,
            },
          }));
        } catch (e: unknown) {
          if (e instanceof Error) {
            toast.error(`Something error: ${e.message}`);
          } else {
            toast.error(`Something error: ${String(e)}`);
          }
          // eslint-disable-next-line no-useless-return
          return;
        }
      },
      testTrigger: async (triggerId: string, triggerType?: TriggerType) => {
        try {
          const rlt = await trpc.automation.testTrigger.mutate({ triggerId });
          setAutomationTestResult((prev) => ({
            ...(prev || {}),
            [triggerId]: {
              ...rlt,
              triggerType,
            },
          }));
        } catch (e: unknown) {
          if (e instanceof Error) {
            toast.error(`Something error: ${e.message}`);
          } else {
            toast.error(`Something error: ${String(e)}`);
          }
          // eslint-disable-next-line no-useless-return
          return;
        }
      },
      getAutomationTestResult: (id: string) => automationTestResult?.[id],
      useAutomationMutation: () => {
        const isMutating = false;
        return {
          isMutating,

          // Form
          // getForm: () => null!,
          // updateForm: async () => {},
          // createForm: async () => {},
          // Automation
          // getAutomation: () => null!,
          update: async () => {},
          create: async () => {},

          // Automation Trigger
          // getAutomationTrigger: () => null!,
          updateTrigger: async (dto: AutomationTriggerUpdateDTO) => {
            await trpc.automation.updateTrigger.mutate(dto);
          },
          deleteTrigger: async (triggerId: string) => {
            await trpc.automation.deleteTrigger.mutate({
              triggerId,
            });
          },
          createTrigger: async (dto: AutomationTriggerCreateDTO) => {
            await trpc.automation.addTrigger.mutate(dto);
          },

          // Automation Actions
          // getAutomationAction: () => null!,
          updateAction: async (dto: AutomationActionUpdateDTO) => {
            await trpc.automation.updateAction.mutate(dto);
          },
          deleteAction: async (actionId: string) => {
            await trpc.automation.deleteAction.mutate({
              actionId,
            });
          },
          createAction: async (dto: AutomationActionCreateDTO) => {
            await trpc.automation.addAction.mutate(dto);
          },
        };
      },
    },
    node: {
      useScope: () => ({
        scope: resourceScope,
        setScope: setResourceScope,
      }),
      useRootNode: (scope?: NodeResourceScope) => {
        if (scope === 'PRIVATE') {
          return privateRootNode!;
        }
        setRootNodeId(rootNode?.id);
        return rootNode!;
      },
      useSearchNodes: (dto: Omit<NodeSearchDTO, 'spaceId'>) =>
        trpcQuery.node.search.useQuery({
          spaceId,
          ...dto,
        }),
      getSearchNodes: (dto: Omit<NodeSearchDTO, 'spaceId'>) =>
        trpc.node.search.query({
          spaceId,
          ...dto,
        }),
      getNodeResourceDetail: (nodeId: string | undefined) =>
        trpcQuery.node.detail.useQuery({ id: nodeId! }, { enabled: nodeId !== undefined }),
      getNodeResourceBO: (nodeId: string) => trpc.node.boInfo.query({ id: nodeId }),
      useNodeResourceBO: (nodeId: string | undefined) =>
        trpcQuery.node.boInfo.useQuery({ id: nodeId! }, { enabled: nodeId !== undefined }),

      invalidateNodeResourceDetail: () => {
        utils.database.getFieldROs.invalidate();
        utils.node.detail.invalidate();
        utils.node.talk.invalidate();
        utils.member.info.invalidate();
      },

      useNodeResourceBOMutation: () => {
        let isMutating = false;

        return {
          create: async (nodeCreateReq: NodeCreateDTOWithoutSpaceId, isRedirect?: boolean) => {
            isMutating = true;
            const { parentId } = nodeCreateReq;
            let createData: any;
            // console.log('nodeCreateReq', nodeCreateReq);
            try {
              createData = await trpc.node.create.mutate({
                spaceId,
                ...nodeCreateReq,
              });
            } catch (e: unknown) {
              if (e instanceof TRPCClientError) {
                if (e?.data?.code === errors.usage.exceed_limit.code) {
                  const errorData = transformUsageLimitErrorData(e?.data?.data);
                  if (errorData) {
                    const errMsg = t('settings.billing.usage_limit_template', {
                      feature: i(FeatureName[errorData.feature]),
                      max: errorData.max,
                      current: errorData?.current,
                    });
                    toast.error(errMsg);
                  }
                } else {
                  toast.error(e?.message);
                }
              }
              return;
            }
            let messageKey = '';
            switch (nodeCreateReq.data.resourceType) {
              case 'DATABASE':
                messageKey = 'resource.create_database_success';
                break;
              case 'FOLDER':
                messageKey = 'resource.create_folder_success';
                break;
              // case 'VIEW':
              //   messageKey = 'resource.create_view_success';
              //   break;
              case 'AUTOMATION':
                messageKey = 'resource.create_automation_success';
                break;
              case 'FORM':
                messageKey = 'resource.create_form_success';
                break;
              case 'MIRROR':
                messageKey = 'resource.create_mirror_success';
                break;
              case 'DOCUMENT':
                messageKey = 'resource.create_document_success';
                break;
              case 'DASHBOARD':
                messageKey = 'resource.create_dashboard_success';
                break;
              case 'PAGE':
                messageKey = 'resource.create_ai_page_success';
                break;
              case 'AI':
                messageKey = 'resource.create_ai_agent_success';
                break;
              default:
                break;
            }
            toast.success(t(messageKey, { name: i(nodeCreateReq.data.name) }));
            isMutating = false;
            setPrivateRootNode(addItemOnRootNodeTree({ ...createData, parentId } as NodeTreeVO, privateRootNode));

            const newNodeData = updateNodeChildrenItem(rootNode.children || [], nodeCreateReq.parentId, {
              ...createData,
              parentId,
            } as NodeTreeVO);
            setRootNode((prev) => ({
              ...prev!,
              children: newNodeData,
            }));

            utils.database.getFieldROs.invalidate();
            await utils.node.detail.invalidate();
            if (isRedirect && createData) {
              router.push(`/space/${spaceId}/node/${createData?.id}`);
            }
          },
          // 移除某一个resource资源
          delete: async (id: string) => {
            isMutating = true;
            await trpc.node.delete.mutate({
              spaceId,
              id,
            });
            isMutating = false;
            const newNodeData = removeNodeChildrenItem(
              rootNode.children || [],
              id,
              t.resource.folder_empty_description,
            );
            setRootNode((prev) => ({
              ...prev!,
              children: newNodeData,
            }));
            setPrivateRootNode(removeItemOnRootNodeTree(id, privateRootNode));
          },
          update: async (nodeUpdateDTO: NodeUpdateDTOReqWithoutSpaceId) => {
            isMutating = true;
            await trpc.node.update.mutate({
              spaceId,
              ...nodeUpdateDTO,
            });
            isMutating = false;
          },
          move: async (nodeMoveDTO: NodeMoveDTOReqWithoutSpaceId) => {
            isMutating = true;
            await trpc.node.move.mutate({
              spaceId,
              ...nodeMoveDTO,
            });
            isMutating = false;
          },
          isMutating,
        };
      },
      uploadFile: (data: { file: File; filePrefix: string }) =>
        uploadAttachment({ file: data.file, filePrefix: data.filePrefix }),
      // uploadToS3({
      //   spaceId,
      //   ...data,
      // }),
    },

    dashboard: {
      useWidgets: (dashboardId?: string) =>
        trpcQuery.dashboard.fetchDashboard.useQuery(
          {
            dashboardId: dashboardId ?? '',
          },
          {
            enabled: !!dashboardId,
          },
        ),

      getDashboardWidget: (args: { widgetId: string; dashboardId?: string }) =>
        trpcQuery.dashboard.fetchWidgetBO.useQuery({
          widgetId: args.widgetId,
        }),

      createWidget: async (dto: WidgetCreateDTO) => {
        await trpc.dashboard.createWidget.mutate(dto);
        utils.node.detail.invalidate();
        utils.node.talk.invalidate();
      },
      updateWidget: async (dto: WidgetUpdateDTO) => {
        await trpc.dashboard.updateWidget.mutate(dto);
        utils.node.detail.invalidate();
        utils.dashboard.wigets.invalidate();
        utils.node.talk.invalidate();
      },
      deleteWidget: async ({ dashboardId, widgetId }: WidgetDeleteDTO) => {
        await trpc.dashboard.deleteWidget.mutate({
          dashboardId,
          widgetId,
        });
        utils.node.detail.invalidate();
        utils.node.talk.invalidate();
      },
    },

    unit: {
      useRoles: () =>
        trpcQuery.role.list.useQuery({
          spaceId,
        }),

      useTeams: () =>
        trpcQuery.team.children.useQuery({
          spaceId,
          id: rootTeam.id,
        }),
      getTeams: (teamId?: string) =>
        trpc.team.children.query({
          spaceId,
          id: teamId || rootTeam.id,
        }),
      getTeamSubList: (teamId: string) =>
        trpc.team.subList.query({
          spaceId,
          teamId,
          pageSize: 50,
        }),
      getSearchUnits: (search: UnitSearch) =>
        trpc.unit.list.query({
          spaceId,
          ...search,
        }),
      useUnitsByIds: (unitIds: string[]) =>
        trpcQuery.unit.list.useQuery(
          {
            spaceId,
            ids: unitIds.filter((id) => id),
            pageNo: 1,
            pageSize: unitIds.length,
          },
          { enabled: unitIds.length > 0 },
        ),
      useMembers: (args?: { name?: string }) =>
        trpcQuery.member.list.useQuery({
          name: args?.name,
          spaceId,
          detail: true,
          pageSize: 5,
        }),
    },
    useIntegrations: (dto: IntegrationListDTOWithoutSpaceID) => {
      const { data, isLoading, refetch } = trpcQuery.integration.list.useQuery({
        spaceId,
        ...dto,
      });

      if (data) {
        return { data: data.integrations, isLoading, refetch };
      }
      return { data: undefined, isLoading, refetch };
    },

    template: {
      useTemplateDetail: (templateId: string | undefined) =>
        trpcQuery.template.detail.useQuery(
          {
            templateId: templateId!,
          },
          {
            enabled: !!templateId,
          },
        ),
    },
    folder: {
      useFolderDetail: (nodeId: string | undefined) => {
        const {
          data: folderDetailBO,
          isLoading,
          refetch,
        } = trpcQuery.node.folderDetail.useQuery({ id: nodeId! }, { enabled: nodeId !== undefined });
        return {
          data: folderDetailBO,
          isLoading,
          refetch,
        };
      },

      uploadFolderLogo: (data: { file: File; filePrefix: string }) =>
        uploadAttachment({ file: data.file, filePrefix: data.filePrefix }),
      // uploadToS3({
      //   spaceId,
      //   ...data,
      // }),
    },

    database: {
      getLinkDatabase: (databaseId: string | undefined) =>
        trpcQuery.database.getLinkDatabase.useQuery(
          { databaseId: databaseId ?? '' },
          {
            enabled: databaseId != null && databaseId.length > 0,
            onError: () => {},
          },
        ),
      getLinkDatabaseInfo: (databaseId: string, linkFieldId: string, formId?: string) =>
        trpcQuery.database.getLinkDatabaseInfo.useQuery(
          { databaseId, linkFieldId, formId },
          {
            enabled: !!databaseId,
          },
        ),

      getDatabaseVO: (databaseId: string | undefined) =>
        trpcQuery.database.info.useQuery(
          {
            databaseId: databaseId!,
          },
          {
            enabled: !!databaseId,
          },
        ),
      // Fields
      getFieldsBO: (
        databaseId: string | undefined,
      ): {
        data: DatabaseFieldWithId[] | undefined;
        isLoading: boolean;
        refetch: () => void;
      } =>
        trpcQuery.database.getFieldBOs.useQuery(
          { databaseId: databaseId || '' },
          {
            enabled: !!databaseId,
            select: (data) => (databaseId ? data : undefined),
          },
        ),
      getFieldsRO: (databaseId: string | undefined) =>
        trpcQuery.database.getFieldROs.useQuery(
          { databaseId: databaseId || '' },
          {
            enabled: !!databaseId,
            select: (data) => (databaseId ? data : undefined),
          },
        ),
      getRecordVO: (databaseId: string | undefined, recordId: string | undefined) =>
        trpcQuery.database.getRecord.useQuery(
          {
            databaseId: databaseId!,
            recordId: recordId!,
          },
          {
            enabled: !!databaseId && !!recordId,
          },
        ),

      getRecord: (databaseId: string, recordId: string) =>
        trpc.database.getRecord.query({
          databaseId,
          recordId,
        }),
      getRecordsVo: (dto: RecordListDTO) =>
        trpcQuery.database.listRecords.useQuery(dto, {
          enabled: !!dto.databaseId,
        }),

      // Database inside Views
      useDatabaseView: (_args: { viewId: string } | undefined) => null!,

      // Fields
      useDatabaseField: (
        args: FieldGetDTO | undefined,
      ): {
        data: DatabaseFieldWithId | undefined;
        isLoading: boolean;
        refetch: () => void;
      } =>
        trpcQuery.database.getField.useQuery(
          {
            databaseId: args?.databaseId as string,
            fieldId: args?.fieldId as string,
          },
          {
            enabled: args !== undefined && args?.fieldId?.length > 0,
            onError: () => {},
          },
        ),

      useDatabaseMutation: () => ({
        updateField: async (args: FieldUpdateDTO) => {
          await updateField({
            databaseId: args.databaseId,
            field: args.field,
          });

          utils.database.getFieldROs.invalidate();
          utils.node.detail.invalidate();
        },
        deleteField: async ({ databaseId, fieldId }: { databaseId: string; fieldId: string }) => {
          await deleteField({
            databaseId,
            fieldId,
          });
          utils.database.getFieldROs.invalidate();
          utils.node.detail.invalidate();
        },
        createField: async (args: FieldCreateDTO) => {
          await createField({
            databaseId: args.databaseId,
            viewId: args.viewId,
            field: args.field,
          });
          utils.database.getFieldROs.invalidate();
          utils.node.detail.invalidate();
        },
        updateView: async (dto: ViewUpdateDTO) => {
          await updateView(dto);
          await spaceContext.refetch();
          utils.database.getFieldROs.invalidate();
          utils.node.detail.invalidate();
        },
        deleteView: async (args: { databaseId: string; viewId: string }) => {
          deleteView({
            databaseId: args.databaseId,
            viewId: args.viewId,
          });
          utils.database.getFieldROs.invalidate();
          utils.node.detail.invalidate();
        },
        createView: async (dto: DatabaseViewCreateDTO) => {
          const view = await createView(dto);
          utils.database.getFieldROs.invalidate();
          utils.node.detail.invalidate();
          return view;
        },
        refetchView: async (viewId: string) => {
          await utils.node.detail.invalidate();
          utils.database.getFieldROs.invalidate();
          utils.database.info.invalidate();
          await utils.database.getView.invalidate({ viewId });
        },

        isMutating,
      }),
    },

    ai: {
      searchSkillset: async (params?: SkillsetSearchDTO) => trpc.ai.searchSkillset.query(params),
      getSkillset: async (skillset: SkillsetSelectBO) => trpc.ai.getSkillset.query(skillset),

      callAIWriter: async (_option: AIWriter, _userPrompt: string) => {
        const result = await trpc.ai.callAIWriter.mutate({
          writerDTO: _option,
          userInput: _userPrompt,
        });

        return result;
      },
    },
    mission: {
      useMissionMutation: () => ({
        create: async (spaceId: string, mission: Mission) => {
          await createMission({
            spaceId,
            mission,
          });
        },
      }),
    },

    email: {
      fetchBoxes: async (dto: EmailBoxesDTO) => trpc.email.fetchBoxes.query(dto),
    },

    commitSurvey: (survey: CommitSurveyDTO) => trpc.admin.survey.commitSurvey.mutate(survey),
  };

  return (
    <NodeResourceApiProvider
      value={
        {
          ...nodeResourceApiValue,
        } as any
      }
    >
      {props.children}
    </NodeResourceApiProvider>
  );
}
