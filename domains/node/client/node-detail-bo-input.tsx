import React from 'react';
import type { ILocaleContext } from '@bika/contents/i18n';
import { DashboardBOInput } from '@bika/domains/dashboard/client/types-form/dashboard-bo-input';
import { MirrorBOInput } from '@bika/domains/mirror/client/mirror-bo-input';
import { AIAgentBOInput } from '@bika/domains/node-resources/ai-agent/bo-input';
import { AIPageBOInput } from '@bika/domains/node-resources/ai-page/bo-input';
import { AiNodeBO, AiPageNodeBO } from '@bika/types/ai/bo';
import { type Automation, AutomationSchema } from '@bika/types/automation/bo';
import { Dashboard } from '@bika/types/dashboard/bo';
import { DatabaseSchema, type Database } from '@bika/types/database/bo';
import { FileBO } from '@bika/types/document/bo';
import { FormBo } from '@bika/types/form/bo';
import type { MirrorBO, NodeResource } from '@bika/types/node/bo';
import type { INodeResourceApi } from '@bika/types/node/context';
import { SurveyInput } from '@bika/ui/admin/types-form/survey-input';
import { AutomationBoListInput } from '@bika/ui/automation/types-form/automation-bo-list-input';
import { DatabaseBoListInput } from '@bika/ui/database/types-form/database-bo-list-input';
import { FileUploadAndDisplayBOInput } from '@bika/ui/file/types-form/file-upload-and-display-bo-input';
import { FormBOInput } from '@bika/ui/node/types-form/form-bo-input';
import { NodeBOInput } from '@bika/ui/node/types-form/node-bo-input';

interface Props {
  refetch: () => void;
  value: NodeResource;
  onChange: (value: NodeResource) => void;
  locale: ILocaleContext;
  api: INodeResourceApi;
  setErrors?: (errors?: Record<string, string>) => void;
}

export function NodeDetailBoInput(props: Props) {
  const { setErrors, locale } = props;

  const { t } = locale;

  const isNew = props.value.id === 'new';

  const resourceType = props.value.resourceType;

  let boInput: React.ReactNode = null;
  if (props.value) {
    if (resourceType === 'AUTOMATION') {
      const automationVO = AutomationSchema.parse(props.value);
      boInput = (
        <AutomationBoListInput
          refetch={props.refetch}
          value={automationVO}
          onChange={(value: Automation) => {
            props.onChange(value);
          }}
          api={props.api}
          locale={props.locale}
        />
      );
    } else if (resourceType === 'DATABASE' && !isNew) {
      const database = DatabaseSchema.parse(props.value);
      boInput = (
        <DatabaseBoListInput
          refetch={props.refetch}
          value={database}
          onChange={(value: Database) => {
            console.log('newValue', value);
            props.onChange(value);
          }}
          locale={props.locale}
          api={props.api}
        />
      );
    } else if (resourceType === 'FORM') {
      const form = props.value as FormBo;
      boInput = (
        <FormBOInput
          value={form}
          api={props.api}
          onChange={(newVal) => {
            props.onChange(newVal);
          }}
          locale={props.locale}
        />
      );
    } else if (resourceType === 'MIRROR') {
      const mirror = props.value as MirrorBO;
      boInput = (
        <MirrorBOInput
          value={mirror}
          api={props.api}
          onChange={(newVal) => {
            props.onChange(newVal);
          }}
          locale={props.locale}
        />
      );
    } else if (resourceType === 'DASHBOARD') {
      const dashboard = props.value as Dashboard;
      boInput = (
        <DashboardBOInput
          locale={props.locale}
          value={dashboard}
          api={props.api}
          onChange={(newValue) => {
            props.onChange(newValue);
          }}
        ></DashboardBOInput>
      );
    } else if (resourceType === 'DOCUMENT') {
      boInput = <> Document</>;
    } else if (resourceType === 'FILE') {
      const fileBO = props.value as FileBO;
      boInput = (
        <FileUploadAndDisplayBOInput
          label={t.database_fields.attachment.name}
          required={true}
          locale={props.locale}
          api={props.api}
          value={fileBO}
          setValue={(newVal) => {
            props.onChange(newVal);
          }}
          setErrors={setErrors}
        />
      );
    } else if (resourceType === 'AI') {
      const AIAgentBO = props.value as AiNodeBO;
      boInput = (
        <AIAgentBOInput
          label={t.database_fields.attachment.name}
          required={true}
          locale={props.locale}
          api={props.api}
          value={AIAgentBO}
          setValue={(newVal) => {
            // console.log('🚀 ~ NodeDetailBoInput ~ newVal:', newVal);
            // if ('bo' in newVal) {
            //   props.onChange(newVal);
            // } else {
            //   props.onChange({
            //     ...newVal,
            //     bo: newVal,
            //   } as unknown as AiNodeBO);
            // }
            props.onChange(newVal);
          }}
          setErrors={setErrors}
        />
      );
    } else if (resourceType === 'PAGE') {
      const AIPageBO = props.value as AiPageNodeBO;
      boInput = (
        <AIPageBOInput
          label={t.database_fields.attachment.name}
          required={true}
          locale={props.locale}
          api={props.api}
          value={AIPageBO}
          setValue={(newVal) => {
            // props.onChange({
            //   ...newVal,
            //   bo: {
            //     resourceType: 'PAGE',
            //     content: newVal?.content || undefined,
            //   },
            // } as unknown as AiPageNodeBO);
            props.onChange(newVal);
          }}
          setErrors={setErrors}
        />
      );
    } else {
      boInput = <SurveyInput surveyType={'COMING_SOON_NODE_RESOURCE'} />;
    }

    return (
      <>
        <NodeBOInput
          locale={props.locale}
          value={props.value}
          onChange={(newValue) => {
            props.onChange(newValue);
          }}
          setErrors={setErrors}
        />
        {boInput}
      </>
    );
  }
}
