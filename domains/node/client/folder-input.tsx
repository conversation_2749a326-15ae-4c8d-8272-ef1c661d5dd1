/* eslint-disable react/display-name */
import Button from '@mui/joy/Button';
import Stack from '@mui/joy/Stack';
import {
  TreeItem2Content,
  TreeItem2IconContainer,
  TreeItem2GroupTransition,
  TreeItem2Root,
  TreeItem2Checkbox,
} from '@mui/x-tree-view/TreeItem2';
import { TreeItem2DragAndDropOverlay } from '@mui/x-tree-view/TreeItem2DragAndDropOverlay';
import { TreeItem2Provider } from '@mui/x-tree-view/TreeItem2Provider';
import { useTreeItem2, type UseTreeItem2Parameters } from '@mui/x-tree-view/useTreeItem2';
import { RichTreeViewPro } from '@mui/x-tree-view-pro/RichTreeViewPro';
import React, { useMemo } from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import { getResourcesTypesConfig } from '@bika/contents/config/client/node/node-resources';
import { type ILocaleContext, useLocale } from '@bika/contents/i18n';
import { updateItemOnRootNodeTree } from '@bika/domains/space/client/sidebar/utils/match-template-util';
import { useResourceEditorContext } from '@bika/types/editor/context';
import type { EditorNodeFolderDTO } from '@bika/types/editor/dto';
import type { NodeResource } from '@bika/types/node/bo';
import type { NodeTreeVO } from '@bika/types/node/vo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { FormLabel } from '@bika/ui/forms';
import DragOutlined from '@bika/ui/icons/components/drag_outlined';
import { Box } from '@bika/ui/layout-components';
import { Modal } from '@bika/ui/modal';
import { NodeResourceItem } from '@bika/ui/node/renderer/item';
import { useSnackBar } from '@bika/ui/snackbar';

interface Props {
  value: EditorNodeFolderDTO;
  locale: ILocaleContext;
  folderId: string;
}

export function FolderInput(props: Props) {
  const { value, locale, folderId } = props;

  const { t, i } = locale;

  const spaceContext = useSpaceContextForce();

  const editorContext = useResourceEditorContext();

  const mutate = editorContext.api.node.useNodeResourceBOMutation();
  const { isMutating } = mutate;

  const { trpcQuery } = useApiCaller();

  const utils = trpcQuery.useUtils();

  const childrenResources: NodeResource[] = useMemo(() => value.data.children || [], [value.data.children]);

  const lists = useMemo(() => {
    const configs = getResourcesTypesConfig(locale);
    return childrenResources.map((child) => {
      const config = configs[child.resourceType];
      return {
        icon: config.iconPath,
        name: i(child.name),
        id: child.id!,
        label: i(child.name),
      };
    });
  }, [childrenResources, i, props]);
  return (
    <>
      <Stack sx={{ my: 2 }}>
        <Stack direction="row" justifyContent="space-between" mb={1}>
          <FormLabel>{t.resource.included_resources}</FormLabel>
          <Button
            variant="plain"
            onClick={() => {
              editorContext.pushScreen({
                screenType: 'NODE_RESOURCE',
                nodeId: 'new',
                parentId: folderId,
              });
            }}
          >
            {t.buttons.add}
          </Button>
        </Stack>
        <RichTreeViewPro
          items={lists}
          experimentalFeatures={{
            indentationAtItemLevel: true,
            itemsReordering: true,
          }}
          itemsReordering
          onItemClick={(event: React.MouseEvent, itemId: string) => {
            event.stopPropagation();

            const item = childrenResources.find((child) => child.id === itemId);

            if (!item) return;

            switch (item.resourceType) {
              case 'FOLDER':
                editorContext.pushScreen({
                  screenType: 'FOLDER',
                  nodeId: item.id || item.templateId!,
                  parentId: folderId,
                });
                break;
              default:
                editorContext.pushScreen({
                  screenType: 'NODE_RESOURCE',
                  resourceType: item.resourceType,
                  nodeId: item.id || item.templateId!,
                  parentId: folderId,
                });
                break;
            }
          }}
          canMoveItemToNewPosition={(params) =>
            !isMutating && params.newPosition.parentId === params.oldPosition.parentId
          }
          slots={{ item: CustomTreeItem }}
          onItemPositionChange={async (params) => {
            const { itemId, oldPosition, newPosition } = params;

            const newResources = [...childrenResources];
            const [movedResource] = newResources.splice(oldPosition.index, 1);
            newResources.splice(newPosition.index, 0, movedResource);

            await mutate.move({
              id: itemId,
              data: {
                preNodeId: newResources[newPosition.index - 1]?.id || null,
                parentId: value.data.id!,
              },
            });
            await utils.node.folderDetail.invalidate();
            const detail = await utils.node.detail.fetch({ id: props.folderId });
            const { rootNode, setRootNode } = spaceContext.useRootNode(value.data.scope);
            setRootNode(updateItemOnRootNodeTree(detail.resource as NodeTreeVO, rootNode));
          }}
        />
      </Stack>
    </>
  );
}

interface CustomTreeItemProps
  extends Omit<UseTreeItem2Parameters, 'rootRef'>,
    Omit<React.HTMLAttributes<HTMLLIElement>, 'onFocus'> {}

const CustomTreeItem = React.forwardRef((props: CustomTreeItemProps, ref: React.Ref<HTMLLIElement>) => {
  const { id, itemId, label, disabled, children, ...other } = props;

  const {
    getRootProps,
    getContentProps,
    getCheckboxProps,
    getLabelProps,
    getGroupTransitionProps,
    getDragAndDropOverlayProps,
    publicAPI,
  } = useTreeItem2({ id, itemId, children, label, disabled, rootRef: ref });

  const { draggable, onDragStart, onDragOver, onDragEnd, ...otherRootProps } = getRootProps(other);

  const item = publicAPI.getItem(itemId);

  const handleDragStart = (event: React.DragEvent) => {
    if (!onDragStart) {
      return;
    }

    onDragStart(event);
    event.dataTransfer.setDragImage((event.target as HTMLElement).parentElement!, 0, 0);
  };

  return (
    <TreeItem2Provider itemId={itemId}>
      <TreeItem2Root {...otherRootProps}>
        <TreeItem2Content
          sx={{
            backgroundColor: 'var(--bg-controls)',
            paddingY: 1,
            marginY: 1,
            '&:hover': {
              background: 'var(--bg-controls-hover)',
            },
          }}
          {...getContentProps()}
        >
          <TreeItem2IconContainer
            draggable={draggable}
            onDragStart={handleDragStart}
            onDragOver={onDragOver}
            onDragEnd={onDragEnd}
          >
            <DragOutlined color={'var(--text-secondary)'} />
          </TreeItem2IconContainer>
          <TreeItem2Checkbox {...getCheckboxProps()} />
          <ResourceLabel
            {...getLabelProps({
              ...item,
            })}
          />
          <TreeItem2DragAndDropOverlay {...getDragAndDropOverlayProps()} />
        </TreeItem2Content>
        {children && <TreeItem2GroupTransition {...getGroupTransitionProps()} />}
      </TreeItem2Root>
    </TreeItem2Provider>
  );
});

export const ResourceLabel = (item: any) => {
  const { i, t } = useLocale();
  const { trpcQuery } = useApiCaller();

  const utils = trpcQuery.useUtils();

  const { toast } = useSnackBar();

  const { api } = useResourceEditorContext();

  const mutate = api.node.useNodeResourceBOMutation();

  const deleteResource = async (resource: NodeResource) => {
    try {
      await mutate.delete(resource.id || resource.templateId!);
      await utils.node.folderDetail.invalidate();
      await utils.node.detail.invalidate();
      toast(t('resource.delete_resource_success', { name: i(resource.name) }), {
        variant: 'success',
      });
    } catch (_err) {
      toast(t('resource.delete_resource_failed', { name: i(resource.name) }), {
        variant: 'error',
      });
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        height: '24px',
        '#delete-icon': {
          display: 'none',
        },
        '&:hover': {
          '#delete-icon': {
            display: 'flex',
          },
        },
      }}
    >
      <NodeResourceItem
        name={item.name}
        icon={item.icon}
        onClickDeleteItem={() => {
          Modal.show({
            type: 'error',
            title: t.resource.title_delete_resource,
            content: t('resource.delete_resource_description', { name: i(item.name) }),
            okText: t.buttons.delete,
            cancelText: t.buttons.cancel,
            onOk: () => deleteResource(item),
          });
        }}
      />
    </Box>
  );
};
