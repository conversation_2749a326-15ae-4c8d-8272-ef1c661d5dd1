// eslint-disable-next-line max-classes-per-file
import assert from 'assert';
import fs from 'fs';
import _ from 'lodash';
import { generateNanoID } from 'sharelib/nano-id';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { NodeErrors } from '@bika/contents/config/server/error/node_errors';
import { TmpAttachmentSO } from '@bika/domains/attachment/server/tmp-attachment-so';
import { AttachmentModel } from '@bika/domains/attachment/server/types';
import { AutomationSO } from '@bika/domains/automation/server/automation-so';
import { AutomationCreateOperations } from '@bika/domains/automation/server/types';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { DatabaseModel } from '@bika/domains/database/server/types';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { parseTemplateAuthor } from '@bika/domains/node/client/utils'; // 服务端可以调client的utils
import { IRelationIdOpts, nodeResourceLockKey, PRIVATE_ROOT_NODE_PREFIX } from '@bika/domains/node/server/types';
import { ReportTemplateSO } from '@bika/domains/report/server/report-template-so';
import { Logger } from '@bika/domains/shared/server';
import { SpaceAttachmentSO } from '@bika/domains/space/server/space-attachment-so';
import { SpaceTemplateInstallation } from '@bika/domains/space/server/space-template-installation';
import { StoreTemplateModel, StoreTemplateSO } from '@bika/domains/store/server/store-template-so';
import { TemplateComparator } from '@bika/domains/template/server/template-comparator';
import { TemplateIdPathStore } from '@bika/domains/template/server/template-id-path-store';
import { TemplateRepoSO } from '@bika/domains/template/server/template-repo-so';
import { TemplateUpgrader } from '@bika/domains/template/server/template-upgrader';
import { UserSO } from '@bika/domains/user/server/user-so';
import {
  DatabaseRecordModel,
  db,
  MongoTransactionCB,
  Prisma,
  PrismaPromise,
  TemplateApplyType,
  TemplateSourceType,
} from '@bika/server-orm';
import { Bikafile } from '@bika/server-orm/bikafile';
import { AttachmentReference } from '@bika/types/attachment/bo';
import { Automation } from '@bika/types/automation/bo';
import { AttachmentCellData, Database, RecordData } from '@bika/types/database/bo';
import { CONST_PREFIX_FOLD, CONST_PREFIX_NODTPL, CONST_PREFIX_TEMPLATE } from '@bika/types/database/vo';
import { i18n, iString } from '@bika/types/i18n/bo';
import {
  ExportBOOptions,
  Folder,
  NodeResource,
  NodeResourceScope,
  NodeResourceType,
  NodeStateBO,
  ToBoOptions,
  ToTemplateOptions,
  WebPage,
} from '@bika/types/node/bo';
import { FolderUpdateBO, type NodeCreateDTO } from '@bika/types/node/dto';
import { FolderVO, NodeRenderOpts, NodeTreeVO, TemplateFolderVO } from '@bika/types/node/vo';
import { ReportTemplate } from '@bika/types/report/bo';
import { Pagination, PaginationSchema } from '@bika/types/shared';
import { AvatarLogo } from '@bika/types/system';
import { isInCI } from '@bika/types/system/app-env';
import { CustomTemplate, CustomTemplateSchema } from '@bika/types/template/bo';
import { StoreTemplateCreateDTO, TemplatePublishDTO, TemplatePublishType } from '@bika/types/template/dto';
import { NodeResourceAdapter } from './node-resource-adapter';
import { NodeSO } from './node-so';
import { NodeSorter } from './node-sorter';
import { NodeTreeBuilder } from './node-tree-builder';
import { NodeModel, NodeResourceSO, NodeTypeEnums } from './types';
import { addChildrenIfCanRead, boToCreatePO, initIdPathStore } from './utils';
import { WebPageSO } from './web-page-so';
import { SseSO } from '../../event/server/sse/sse-so';
import { IntegrationSO, VikaImporterHandler, VikaNodeImportExecutor } from '../../integration/server';

export type FolderModel = Prisma.FolderGetPayload<Prisma.FolderDefaultArgs>;
export type FolderWithNodeModel = Prisma.FolderGetPayload<{
  include: {
    node: {
      include: { children: true };
    };
  };
}>;

export class FolderSO extends NodeResourceSO {
  /**
   * 孩子NodeTreeSO，涉及到链式数据库查询children，
   * 因此这里惰式加载，被调用的时候，才加载
   */
  protected _nodeModel: NodeModel;

  protected _folderPO: FolderWithNodeModel;

  private _children: NodeSO[] | null = null;

  constructor(model: NodeModel, folderPO: FolderWithNodeModel) {
    super();
    this._nodeModel = model;
    this._folderPO = folderPO;
  }

  get resourceType(): NodeResourceType {
    return 'FOLDER';
  }

  async refresh() {
    const folderPO = await FolderSO.getFolderPO(this.id);
    this._folderPO = folderPO;
    this._nodeModel = folderPO.node;
  }

  static async getFolderPO(nodeId: string) {
    const folderPO = await db.prisma.folder.findUnique({
      where: {
        id: nodeId.replace(PRIVATE_ROOT_NODE_PREFIX, ''),
      },
      include: {
        node: {
          include: { children: true },
        },
      },
    });
    return folderPO!;
  }

  static async init(id: string): Promise<FolderSO> {
    const folder = await this.getFolderPO(id);
    if (!folder) {
      throw new ServerError(errors.common.not_found);
    }
    return new FolderSO(folder.node, folder);
  }

  static async initWithModel(model: NodeModel): Promise<FolderSO> {
    const folderPO = await this.getFolderPO(model.id);
    return new FolderSO(model, folderPO);
  }

  get model() {
    return this._nodeModel;
  }

  /**
   * 返回封面，如果没有设置封面，这里是返回Node Icon
   */
  get cover() {
    if (this._folderPO.cover) {
      return this._folderPO.cover as AvatarLogo;
    }

    return (this._nodeModel.icon as AvatarLogo) || undefined;
  }

  get readme() {
    return (this._folderPO.readme as iString) || undefined;
  }

  get id() {
    return this.model.id;
  }

  get name() {
    return this.model.name as iString;
  }

  /**
   * Node Icon，Folder、Template、RootFolder，另有封面cover
   */
  get icon() {
    return this.model.icon as AvatarLogo;
  }

  get description(): iString | undefined {
    return (this.model.description as iString) || undefined;
  }

  get spaceId() {
    return this.model.spaceId;
  }

  get parentId() {
    return this.model.parentId;
  }

  get preNodeId() {
    return this.model.preNodeId;
  }

  get templateId() {
    return this.model.templateId || undefined;
  }

  get isRoot(): boolean {
    return this.model.type === 'ROOT';
  }

  get isTemplate(): boolean {
    return this.model.type === 'TEMPLATE';
  }

  get unitId(): string | null {
    return this.toNodeSO().unitId;
  }

  get scope(): NodeResourceScope {
    return this.toNodeSO().scope;
  }

  /**
   * 获取所有子节点(递归查询)
   * @returns NodeSO List
   */
  async getAllChildren(): Promise<NodeSO[]> {
    const allChildren: NodeSO[] = [];

    const loopChildren = async (nodes: NodeSO[]) => {
      if (nodes.length === 0) {
        return;
      }
      allChildren.push(...nodes);
      for (const node of nodes) {
        if (node.isFolder || node.isTemplate) {
          const folderSO = await node.toResourceSO<FolderSO>();
          const children = await folderSO.getChildren();
          await loopChildren(children);
        }
      }
    };
    const downChildren = await this.getChildren();
    await loopChildren(downChildren);
    return allChildren;
  }

  async getAllChildIds(): Promise<string[]> {
    const allChildIds: string[] = [];

    const loopChildren = async (parentId: string) => {
      if (!parentId) {
        return;
      }
      const childPOs = await db.prisma.node.findMany({ where: { parentId }, select: { id: true, type: true } });
      for (const childPO of childPOs) {
        allChildIds.push(childPO.id);
        if (childPO.type === 'FOLDER' || childPO.type === 'TEMPLATE') {
          await loopChildren(childPO.id);
        }
      }
    };

    await loopChildren(this.id);
    return allChildIds;
  }

  /**
   * 是否有子节点
   */
  async hasChildren(): Promise<boolean> {
    const childrenCounts = await db.prisma.node.count({
      where: {
        spaceId: this.spaceId,
        parentId: this.id,
      },
    });
    return childrenCounts > 0;
  }

  /**
   * 加载children的NodeTreeSO，这里是惰式加载，被调用的时候，才加载
   * 因为，NodeTreeSO中的Model PO，虽然包含children，但是children们的children，还没有加载
   * 这里只加载子节点,并不会递归加载子节点
   *
   *
   * dontCache，声明不缓存
   *
   * @returns 返回NodeTreeSO
   */
  async getChildren(dontCache = false): Promise<NodeSO[]> {
    if (dontCache || this._children === null) {
      await this.reloadChildren();
    }
    if (this._children === null) {
      Logger.warn(`folder ${this.id} 's children can not be null, only can be empty`);
      throw new Error('folder children can not be null, only can be empty');
    }
    return this._children;
  }

  /**
   * reload children of current node (depth: 1).
   * this method will override the `_children` property.
   * allow call this method to reload children outside
   */
  async reloadChildren() {
    const children: NodeSO[] = [];
    this._children = [];
    const nodePOs: NodeModel[] = await db.prisma.node.findMany({
      where: {
        spaceId: this.spaceId,
        parentId: this.id,
      },
    });
    for (const nodePO of nodePOs) {
      const childSO = NodeSO.initWithModel(nodePO);
      children.push(childSO);
    }
    if (children.length > 0) {
      const sorter = NodeSorter.init(children);
      this._children = sorter.sort() as NodeSO[];
    }
  }

  async findChildren(option?: {
    name?: string;
    type?: NodeResourceType;
    pagination?: Pagination;
    orderBy?: Prisma.NodeOrderByWithRelationInput;
    loopParents?: boolean;
    privateUnitId?: string;
  }): Promise<NodeSO[]> {
    const { name, type, pagination, orderBy, loopParents, privateUnitId } = option || {};
    let where: Prisma.NodeWhereInput = {
      spaceId: this.spaceId,
    };
    // 非根目录需要限制在当前文件下查找
    if (!this.isRoot) {
      where = { ...where, parentId: this.id };
    }
    // 名称过滤
    if (name) {
      const nameFilter: Prisma.NodeWhereInput = {
        AND: {
          OR: [
            {
              name: { string_contains: name },
            },
            ...i18n.locales.map((lang) => ({ name: { path: [lang], string_contains: name } })),
          ],
        },
      };
      where = { ...where, ...nameFilter };
    }
    // 类型过滤
    if (type) {
      where = { ...where, type: type as NodeTypeEnums };
    }
    if (privateUnitId) {
      where = { ...where, unitId: privateUnitId };
    } else {
      where = { ...where, unitId: null };
    }
    let children = await db.prisma.node.findMany({
      where,
      select: {
        id: true,
      },
      orderBy,
    });
    const foundNodeIds = [];
    if (!this.isRoot && children.length > 0) {
      // 非根目录下,递归查找,因为根目录可以直接根据空间站查找
      while (children.length > 0) {
        const childIds = children.map((child) => child.id);
        foundNodeIds.push(...childIds);
        children = await db.prisma.node.findMany({
          where: {
            spaceId: this.spaceId,
            parentId: {
              in: childIds,
            },
          },
          select: {
            id: true,
          },
          orderBy,
        });
      }
    } else {
      foundNodeIds.push(...children.map((child) => child.id));
    }
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});
    const nodeModels = await db.prisma.node.findMany({ where: { id: { in: foundNodeIds } } });
    // 内存分页, prisma不支持自定义字段排序
    const orderedNodes = foundNodeIds.map((id) => nodeModels.find((nodeModel) => nodeModel.id === id));
    const paginatedNodes = orderedNodes.slice(pageSize * (pageNo - 1), pageSize * pageNo);
    let parentsMap: Map<string, NodeModel[]> = new Map();
    if (loopParents) {
      const nodeIds = paginatedNodes.map((i) => i?.id).filter((i) => i !== undefined);
      parentsMap = await NodeSO.queryParents(nodeIds);
    }
    const result = paginatedNodes.map((node) => node && NodeSO.initWithModel(node, parentsMap.get(node.id)));
    return result.filter((item): item is NodeSO => item !== undefined);
  }

  async createChildSimple(
    user: UserSO,
    data: NodeCreateDTO['data'],
    createParams?: {
      cover?: AvatarLogo;
      readme?: iString;
      scope?: NodeResourceScope; // only for private root create children
      createDefaultRecords?: boolean;
    },
  ): Promise<NodeSO> {
    const spaceId = this.spaceId;
    const unitId = createParams?.scope === 'PRIVATE' && this.isRoot ? await user.getMemberId(spaceId) : this.unitId;

    const space = await this.toNodeSO().getSpace();
    // 计算ID和排序加上锁
    const lock = await db.redis.lock(nodeResourceLockKey(this.id, 'create'), 5000);
    const nextNodeId = await this.firstChildId(unitId);
    const {
      id: nodeId,
      operations,
      mongoSessions,
      records,
    } = await NodeSO.boToCreateInput(user, space, { ...data, ...createParams, asMember: !unitId } as NodeResource, {
      parentId: this.id,
      nextNodeId,
      unitId,
    });
    const recordDAO = db.mongo.databaseRecord(this.spaceId);
    await recordDAO.init();
    await db.mongo.transaction(async (session) => {
      if (records && records.length > 0) {
        await recordDAO.insertMany(records, { session });
      } else if (data.resourceType === 'DATABASE' && createParams?.createDefaultRecords) {
        await DatabaseSO.createDefaultRecordsSession({ databaseId: nodeId, userId: user.id, spaceId }, { session });
      }
      for (const mongoSession of mongoSessions) {
        await mongoSession(session);
      }
      await db.prisma.$transaction(operations);
    });
    await lock.release();
    const node = await NodeSO.init(nodeId);

    EventSO.folder.onChildCreated(node);

    SseSO.emit(user.id, {
      name: 'node',
      spaceId: this.spaceId,
      nodeId: this.id,
    });

    return node;
  }

  /**
   * create children by bo
   *
   * @returns first child id
   */
  async createChildren(
    user: UserSO,
    data: NodeResource[],
    param?: { scope?: NodeResourceScope; recovery?: boolean; loadExistingResource?: boolean; bikafile?: Bikafile },
  ): Promise<string> {
    const callbackFns: (() => Promise<void>)[] = [];
    // handle bo
    if (!param?.recovery) {
      // 加载已存在的资源，建立关联关系时需要使用到
      const fetchRelationResources = async () => {
        if (!param?.loadExistingResource) {
          return undefined;
        }
        const children = await this.getAllChildren();
        return Promise.all(children.map(async (child) => (await child.toResourceSO()).toBO()));
      };
      const existingResources = await fetchRelationResources();
      await boToCreatePO(this.spaceId, data, existingResources);
    }
    if (param?.bikafile) {
      // handle assets
      const callbacks = await this.handleBikafileAssets(user.id, data, param.bikafile.getAssets());
      callbackFns.push(...callbacks);
    }
    const lock = await db.redis.lock(nodeResourceLockKey(this.id, 'create'), 5000);
    // get operation,lock for 5s
    const {
      prismaOperations,
      mongoTxSessions,
      callbackFns: createCallbackFns,
      records,
    } = await this.createResourceOperations(user, data, param?.scope);
    callbackFns.push(...createCallbackFns);
    const recordDAO = db.mongo.databaseRecord(this.spaceId);
    await recordDAO.init();
    await db.mongo.transaction(async (session) => {
      for (const mongoSession of mongoTxSessions) {
        await mongoSession(session);
      }
      if (records.length > 0) {
        // 500 records per time
        for (let i = 0; i < records.length; i += 500) {
          const batch = records.slice(i, i + 500);
          await recordDAO.insertMany(batch, { session });
        }
      }
      await db.prisma.$transaction(prismaOperations);
    });
    await lock.release();
    // 异步执行回调函数
    if (callbackFns.length > 0) {
      if (isInCI()) {
        await Promise.all(callbackFns.map((fn) => fn()));
      } else {
        Promise.all(callbackFns.map((fn) => fn()));
      }
    }
    // event
    const promises: Promise<unknown>[] = data.map(async (item) => {
      const node = await NodeSO.init(item.id!);
      return EventSO.folder.onChildCreated(node);
    });
    // sse
    promises.push(
      SseSO.emit(user.id, {
        name: 'node',
        spaceId: this.spaceId,
        nodeId: this.id,
      }),
    );

    await Promise.allSettled(promises);

    return data[0].id!;
  }

  private async createResourceOperations(
    user: UserSO,
    data: NodeResource[],
    scope?: NodeResourceScope,
  ): Promise<{
    prismaOperations: PrismaPromise<unknown>[];
    mongoTxSessions: MongoTransactionCB[];
    callbackFns: (() => Promise<void>)[];
    records: DatabaseRecordModel[];
  }> {
    const prismaOperations: PrismaPromise<unknown>[] = [];
    const mongoTxSessions: MongoTransactionCB[] = [];
    const callbackFunctions: (() => Promise<void>)[] = [];
    const recordModels: DatabaseRecordModel[] = [];
    const unitId = scope === 'PRIVATE' ? await user.getMemberId(this.spaceId) : null;
    let nextNodeId = await this.firstChildId(unitId);
    const space = await this.toNodeSO().getSpace();
    const inputToModelOperations = async (
      resources: NodeResource[],
      options: { preNodeId?: string; parentId?: string },
    ) => {
      let { preNodeId } = options;
      for (const resource of resources) {
        const {
          id: nodeId,
          operations,
          mongoSessions,
          callbackFns,
          records,
        } = await NodeSO.boToCreateInput(user, space, resource, {
          parentId: options.parentId,
          // if it has pre so the next must not be created
          nextNodeId,
          preNodeId,
          unitId,
        });
        prismaOperations.push(...operations);
        mongoTxSessions.push(...mongoSessions);
        if (callbackFns) {
          callbackFunctions.push(...callbackFns);
        }
        if (records) {
          recordModels.push(...records);
        }
        preNodeId = nodeId;
        nextNodeId = undefined; // reset next node, only used for once
        if (resource.resourceType === 'FOLDER') {
          await inputToModelOperations((resource as Folder).children ?? [], { parentId: nodeId });
        }
      }
    };

    await inputToModelOperations(data, {
      parentId: this.id,
    });
    return { prismaOperations, mongoTxSessions, callbackFns: callbackFunctions, records: recordModels };
  }

  static createFolderNodeOperationWithTemplate(
    userId: string,
    createParam: {
      spaceId: string;
      parentId: string;
      preNodeId?: string;
      nextNodeId?: string;
      unitId?: string;
    },
    folderTemplate: Folder,
    readme?: iString,
  ): { id: string; operation: PrismaPromise<NodeModel> } {
    const { spaceId, parentId, preNodeId, nextNodeId, unitId } = createParam;
    const { id: folderNodeId, input: folderNodeCreateInput } = this.buildFolderNodeCreateInputWithTemplate(
      userId,
      {
        spaceId,
        parentId,
        preNodeId,
        nextNodeId,
        unitId,
      },
      folderTemplate,
      readme,
    );
    const operation = db.prisma.node.create({ data: folderNodeCreateInput });
    return { id: folderNodeId, operation };
  }

  async bikafilePreview(tmpAttachmentId: string) {
    const tmpAttach = await TmpAttachmentSO.initById(tmpAttachmentId);
    const filePath = await tmpAttach.getObjectAsLocalFile();
    const bikaFile = new Bikafile(filePath);
    // remove tmpfile
    try {
      fs.rmSync(filePath, { recursive: true });
    } catch (e) {
      Logger.error('Failed to remove tmp bikafile', e);
    }
    return bikaFile.data;
  }

  async importResourceFromBikafile(user: UserSO, tmpAttachment: TmpAttachmentSO, scope?: NodeResourceScope) {
    const filePath = await tmpAttachment.getObjectAsLocalFile();
    const bikaFile = new Bikafile(filePath);
    // remove tmpfile
    try {
      fs.rmSync(filePath, { recursive: true });
      await tmpAttachment.delete();
    } catch (e) {
      Logger.error('Failed to remove tmp bikafile', e);
    }
    if (bikaFile.format !== 'RESOURCES') {
      throw new Error('Only support import RESOURCES file');
    }
    if (bikaFile.data.format === 'RESOURCES') {
      const folderId = await this.createChildren(user, bikaFile.data.resources, { scope, bikafile: bikaFile });
      return folderId;
    }
    throw new Error('Only support import RESOURCES file');
  }

  async handleBikafileAssets(
    userId: string,
    resources: NodeResource[],
    assets: { buffer: Buffer; fileName: string }[],
  ) {
    const space = await this.toNodeSO().getSpace();
    // 这里因为有数据库查询,采用批量操作，放在循环外
    const notExistAssets = await SpaceAttachmentSO.findNonExistingAttachments(assets);
    const callbacks: (() => Promise<void>)[] = [];

    const getAvatar = (avatar: AvatarLogo, ref: AttachmentReference): AvatarLogo => {
      if (avatar.type !== 'ATTACHMENT') {
        return avatar;
      }
      const notExist = notExistAssets[avatar.attachmentId];
      if (!notExist) {
        return avatar;
      }
      // 如果资源不存在, 需要上传到服务端
      const { id, filePath, callback } = SpaceAttachmentSO.buildUploadCallback(space, userId, { ...notExist, ref });
      if (callback && id && filePath) {
        callbacks.push(callback);
        return {
          type: 'ATTACHMENT',
          attachmentId: id,
          relativePath: filePath,
        };
      }
      return avatar;
    };

    const getRecordAttachData = (
      databaseId: string,
      recordId: string,
      fieldId: string,
      data: AttachmentCellData[],
    ): AttachmentCellData[] => {
      if (data.length === 0) {
        return [];
      }
      const newAttachData: AttachmentCellData[] = [];
      for (const attach of data) {
        const notExist = notExistAssets[attach.id];
        if (!notExist) {
          newAttachData.push(attach);
          continue;
        }
        const { id, filePath, callback } = SpaceAttachmentSO.buildUploadCallback(space, userId, {
          ...notExist,
          ref: { type: 'RESOURCE', id: databaseId, fieldId, recordId },
        });
        if (callback && id && filePath) {
          callbacks.push(callback);
          // actually, only id is needed
          newAttachData.push({
            ...attach,
            id,
            path: filePath,
          });
        }
      }
      return newAttachData;
    };

    // 递归遍历资源, 修改asset, 获取上传callback
    const recursiveUploadAssets = async (data: NodeResource[]) => {
      for (const resource of data) {
        if ('brandLogo' in resource) {
          resource.brandLogo = getAvatar(resource.brandLogo as AvatarLogo, {
            type: 'RESOURCE',
            id: resource.id!,
          });
        }

        if ('cover' in resource) {
          resource.cover = getAvatar(resource.cover as AvatarLogo, {
            type: 'RESOURCE',
            id: resource.id!,
          });
        }

        if ('records' in resource && (resource as Database).records) {
          const fieldMap = _.keyBy((resource as Database).fields, 'id');
          for (const record of (resource as Database).records!) {
            Object.entries(record.data as RecordData).forEach(async ([fieldId, value]) => {
              const field = fieldMap[fieldId];
              if (field?.type === 'ATTACHMENT') {
                record.data[fieldId] = getRecordAttachData(
                  resource.id!,
                  record.id!,
                  fieldId,
                  value as unknown as AttachmentCellData[],
                );
              }
            });
          }
        }
        if (resource.resourceType === 'FOLDER' && (resource as Folder).children) {
          await recursiveUploadAssets((resource as Folder).children!);
        }
      }
    };
    // ps 一定是经过boToCreatePO处理过的
    await recursiveUploadAssets(resources);
    return callbacks;
  }

  async importResourceFromVika(user: UserSO, integrationSO: IntegrationSO, vikaNodeIds: string[]): Promise<string> {
    const handler = integrationSO.getHandler<VikaImporterHandler>();
    const memberId = await user.getMemberId(this.spaceId);
    let childId = '';
    // check permission
    await handler.init();
    try {
      // 初始化导入执行器
      const executor = new VikaNodeImportExecutor(
        {
          user,
          spaceId: this.spaceId,
          memberId,
          folder: this,
        },
        handler.getConnection(),
      );
      // 执行导入
      childId = await executor.execute(vikaNodeIds);
    } finally {
      // 记得销毁处理器
      await handler.destroy();
    }
    return childId;
  }

  /**
   * create folder node.
   *
   * @param userContext user context
   * @param name node name
   * @param preNodeId specify the pre node id, if not specified, the new node will be the first node
   * @param description node description
   * @param config node extra config
   */
  async createFolderNode(
    userContext: { userId: string },
    name: string,
    preNodeId?: string,
    description?: string,
    config?: {
      cover?: AvatarLogo;
      readme?: iString;
    },
  ): Promise<FolderSO> {
    const { userId } = userContext;
    // new node id
    const folderNodeId = generateNanoID(CONST_PREFIX_FOLD);
    // 插入节点的顺序
    const { preNode, nextNode } = await this.findNodeInsertPos(preNodeId);
    const input: Prisma.NodeCreateInput = {
      id: folderNodeId,
      name,
      description,
      type: 'FOLDER',
      // property: config,
      space: {
        connect: {
          id: this.spaceId,
        },
      },
      parent: {
        connect: {
          id: this.id,
        },
      },
      preNode: {
        connect: preNode ? { id: preNode } : undefined,
      },
      nextNode: {
        connect: nextNode ? { id: nextNode } : undefined,
      },
      createdBy: userId,
      updatedBy: userId,
      folder: {
        create: {
          cover: config?.cover,
          readme: config?.readme,
          createdBy: userId,
          updatedBy: userId,
        },
      },
    };
    const [_folderNodePO] = await db.prisma.$transaction([db.prisma.node.create({ data: input })]);
    await this.reloadChildren();
    const nodeSO = await NodeSO.init(folderNodeId);
    return nodeSO.toResourceSO<FolderSO>();
  }

  private static buildFolderNodeCreateInputWithTemplate(
    userId: string,
    params: {
      spaceId: string;
      parentId: string;
      preNodeId?: string;
      nextNodeId?: string;
      unitId?: string;
    },
    folderTemplate: Folder,
    readme?: iString,
  ): { id: string; input: Prisma.NodeCreateInput } {
    const { spaceId, parentId, preNodeId, nextNodeId, unitId } = params;
    const folderNodeId = folderTemplate.id || generateNanoID(CONST_PREFIX_FOLD);
    const input: Prisma.NodeCreateInput = {
      id: folderNodeId,
      icon: folderTemplate.icon,
      templateId: folderTemplate.templateId,
      name: folderTemplate.name,
      description: folderTemplate.description,
      type: 'FOLDER',
      createdBy: userId,
      updatedBy: userId,
      unit: unitId ? { connect: { id: unitId } } : undefined,
      space: {
        connect: { id: spaceId },
      },
      parent: {
        connect: { id: parentId },
      },
      preNode: {
        connect: preNodeId ? { id: preNodeId } : undefined,
      },
      nextNode: {
        connect: nextNodeId ? { id: nextNodeId } : undefined,
      },
      folder: {
        create: {
          templateId: folderTemplate.templateId,
          cover: folderTemplate.icon,
          readme,
          createdBy: userId,
          updatedBy: userId,
        },
      },
    };
    return {
      id: folderNodeId,
      input,
    };
  }

  /**
   * find child node with template id
   * @param templateId template id
   */
  async findChildNodeByTemplateId(templateId: string): Promise<NodeSO | undefined> {
    const childNode = await db.prisma.node.findFirst({
      where: {
        spaceId: this.spaceId,
        parentId: this.model.id,
        templateId,
      },
    });
    if (!childNode) {
      return undefined;
    }
    return NodeSO.init(childNode?.id);
  }

  /**
   * get first child node under this folder node.
   */
  async firstChildNode(privateUnitId?: string | null): Promise<NodeSO | null> {
    const childNode = await db.prisma.node.findFirst({
      where: {
        spaceId: this.spaceId,
        parentId: this.id,
        preNodeId: null,
        unitId: privateUnitId || null,
      },
    });
    if (!childNode) {
      return null;
    }
    return NodeSO.initWithModel(childNode);
  }

  // unitID如存在，则理解为获取用户私有空间
  async firstChildId(privateUnitId?: string | null): Promise<string | undefined> {
    const firstChildId = await db.prisma.node.findFirst({
      where: {
        spaceId: this.spaceId,
        parentId: this.id,
        preNodeId: null,
        unitId: privateUnitId || null,
      },
      select: {
        id: true,
      },
    });
    return firstChildId?.id;
  }

  /**
   * get last child node under this folder node.
   */
  async lastChildNode(privateUnitId?: string | null): Promise<NodeSO | null> {
    const childNode = await db.prisma.node.findFirst({
      where: {
        spaceId: this.spaceId,
        parentId: this.id,
        nextNode: null,
        unitId: privateUnitId || null,
      },
    });
    if (!childNode) {
      return null;
    }
    return NodeSO.initWithModel(childNode);
  }

  /**
   * find child node with pre node id
   * @param preNodeId pre node id
   */
  async findByPreNodeId(preNodeId: string): Promise<NodeSO | null> {
    const childNode = await db.prisma.node.findFirst({
      where: {
        spaceId: this.spaceId,
        parentId: this.model.id,
        preNodeId,
      },
    });
    return childNode && NodeSO.init(childNode?.id);
  }

  // 获取node关系，不实际执行insert
  async findNodeInsertPos(
    beforeNodeId?: string,
  ): Promise<{ preNode: string | undefined; nextNode: string | undefined }> {
    if (!beforeNodeId) {
      // 默认在最前面
      const firstNode = await this.firstChildNode();
      if (firstNode) {
        return { preNode: undefined, nextNode: firstNode.id };
      }
      return { preNode: undefined, nextNode: undefined };
    }
    const nextNode = await this.findByPreNodeId(beforeNodeId);
    if (nextNode) {
      return { preNode: beforeNodeId, nextNode: nextNode.id };
    }
    return { preNode: beforeNodeId, nextNode: undefined };
  }

  /**
   * get update folder node
   * @returns prisma operations and mongo sessions
   */
  async updateWithNodeInput(
    user: UserSO,
    data: FolderUpdateBO,
  ): Promise<{
    operations: PrismaPromise<FolderModel | AttachmentModel>[];
    mongoSessions: MongoTransactionCB[];
  }> {
    const { name, cover, description, readme } = data;
    const operations: PrismaPromise<FolderModel | AttachmentModel>[] = [
      db.prisma.folder.update({
        where: {
          id: this.id,
        },
        data: {
          cover,
          readme,
          updatedBy: user.id,
          node: {
            update: {
              description,
              name,
              updatedBy: user.id,
            },
          },
        },
      }),
    ];
    const mongoSessions: MongoTransactionCB[] = [];
    if (cover) {
      // 改变了封面
      const space = await this.toNodeSO().getSpace();
      const { operations: attachmentOperations, mongoSessions: attachmentMongoSessions } =
        await SpaceAttachmentSO.buildChangeAvatarSession(
          user.id,
          space,
          { type: 'RESOURCE', id: this.id },
          {
            previous: this.cover,
            current: cover,
          },
        );
      operations.push(...attachmentOperations);
      mongoSessions.push(...attachmentMongoSessions);
    }
    return { operations, mongoSessions };
  }

  /**
   * 使用SQL With Recursive查询子节点树
   *
   * @param opts
   */
  public async toNodeTreeVOWithSQLRecursive(opts?: NodeRenderOpts): Promise<NodeTreeVO> {
    // const sqlSearchInput = `%${category}%`;
    const { depth = 2 } = opts ?? {};
    let unitId: string | undefined;

    if (opts?.scope === 'PRIVATE') {
      assert(opts.userId, 'userId is required when scope is PRIVATE');
      // user id对应的member id (unitId) 获取
      const user = await UserSO.init(opts.userId);
      const member = await user.getMember(this.spaceId);
      unitId = member.id;
    }

    const sql = `
WITH RECURSIVE tree AS (
    SELECT id, "unitId", "templateId", "spaceId", "parentId", "preNodeId", name, description, type, state, icon, "createdBy", "updatedBy", "createdAt", "updatedAt", 0 AS depth
    FROM public."Node"
    WHERE id = '${this.id}' -- 假设我们从 id = ${this.id} 的节点开始

    UNION ALL

    SELECT n.id, n."unitId", n."templateId", n."spaceId", n."parentId", n."preNodeId", n.name, n.description, n.type, n.state, n.icon, n."createdBy", n."updatedBy", n."createdAt", n."updatedAt", depth + 1
    FROM public."Node" n
    INNER JOIN tree t ON n."parentId" = t.id AND n."unitId" ${unitId ? `= '${unitId}'` : 'IS NULL'} 
    WHERE depth < ${depth} -- 设置最大深度为 ${depth}
)
SELECT * FROM tree ORDER BY "preNodeId" DESC;
`;
    const nodePOs = await db.prisma.$queryRawUnsafe<NodeModel[]>(sql);
    // 节点列表
    const nodes = await Promise.all(
      nodePOs.map(async (item) => {
        const nodeSO = NodeSO.initWithModel(item);
        return nodeSO.toVO(opts);
      }),
    );
    // 构建节点树
    return new NodeTreeBuilder(nodes).build(this.id);
  }

  public static async recursiveGetParents(
    ids: string[],
  ): Promise<Map<string, { id: string; name: iString; icon?: AvatarLogo }[]>> {
    if (ids.length === 0) {
      return new Map();
    }
    const sql = `
WITH RECURSIVE tree AS (
    SELECT id, "parentId",  name, icon
    FROM public."Node"
    WHERE id in (${ids.join(', ')})
		
    UNION ALL

    SELECT n.id, n."parentId", n.name,  n.icon
    FROM public."Node" n
    INNER JOIN tree t ON n."id" = t."parentId" and n."parentId" != 'NULL'  -- 直到遇到根目录
  )
SELECT * FROM tree
`;
    const nodePOs = await db.prisma.$queryRawUnsafe<NodeModel[]>(sql);
    const nodePOMap = _.keyBy(nodePOs, 'id');

    const loopParents = (id: string) => {
      let node = nodePOMap[id];
      const parents = [];
      do {
        if (node.parentId) {
          node = nodePOMap[node.parentId];
          parents.push({ id: node.id, name: node.name, icon: node.icon });
        }
      } while (node);
      return parents;
    };
    return ids.reduce((acc, id) => {
      acc.set(id, loopParents(id));
      return acc;
    }, new Map());
  }

  /**
   * 递归查询子节点树,包含本身节点
   *
   * @returns NodeTreeVO
   */
  private async toNodeTreeVO(opts?: NodeRenderOpts): Promise<NodeTreeVO> {
    const { depth = 2 } = opts ?? {};
    const newTreeVO = await this.toNodeSO().toVO(opts);
    if (!depth || depth <= 0) {
      return newTreeVO;
    }
    const children = await this.getChildren();
    const childrenInVO: NodeTreeVO[] = [];
    // console.log(`深度: ${depth}, 子节点数量: ${children.length}`);
    for (const child of children) {
      if (child.isFolder || child.isTemplate) {
        // 文件夹节点,继续遍历
        // console.log(`node id: ${child.id}, 是文件夹`);
        const childFolderSO = await child.toResourceSO<FolderSO>();
        const hasChildren = await childFolderSO.hasChildren();
        // console.log(`node id: ${child.id}, has children: ${hasChildren}`);
        if (hasChildren) {
          // console.log(`node id: ${child.id}, 有子节点`);
          const childTreeVO = await childFolderSO.toNodeTreeVO({ ...opts, depth: depth - 1 });
          addChildrenIfCanRead(childrenInVO, childTreeVO);
        } else {
          // console.log(`node id: ${child.id}, 没有子节点`);
          const treeVO = await child.toVO(opts);
          addChildrenIfCanRead(childrenInVO, treeVO);
        }
      } else {
        // console.log(`node id: ${child.id}, 不是文件夹`);
        const treeVO = await child.toVO(opts);
        addChildrenIfCanRead(childrenInVO, treeVO);
      }
    }
    newTreeVO.children = childrenInVO;
    return newTreeVO;
  }

  /**
   * 获取到space的和个人的
   *
   * @param opts
   * @returns
   */
  async toVO(opts?: NodeRenderOpts): Promise<FolderVO> {
    const nodeTreeVO = await this.toNodeTreeVOWithSQLRecursive({ ...opts, scope: this.scope });
    return {
      ...nodeTreeVO,
      cover: this.cover,
      readme: this.readme,
    };
  }

  /**
   * 基于模板创建数据库节点数据操作
   * @param userId user id
   * @param createDatabaseParam create database parameter
   */
  createDatabaseNodeOperationWithTemplate(
    userId: string,
    createDatabaseParam: {
      preNodeId?: string;
      nextNodeId?: string;
      databaseTemplate: Database;
    },
  ): { id: string; operation: PrismaPromise<DatabaseModel>; recordModels: DatabaseRecordModel[] } {
    const { preNodeId, nextNodeId, databaseTemplate } = createDatabaseParam;
    const { spaceId, id: parentId } = this.model;

    return DatabaseSO.createDatabaseOperationWithTemplate(
      userId,
      {
        spaceId,
        parentId,
        preNodeId,
        nextNodeId,
      },
      databaseTemplate,
    );
  }

  /**
   * 基于模板创建自动化节点的数据库操作
   * @param userId user id
   * @param createAutomationParam create automation parameter
   */
  async createAutomationNodeOperationWithTemplate(
    userId: string,
    createAutomationParam: {
      preNodeId?: string;
      nextNodeId?: string;
      automationTemplate: Automation;
      isTemplateOperation?: boolean;
    },
  ): Promise<AutomationCreateOperations> {
    const { spaceId, id: parentId } = this.model;

    const user = await UserSO.init(userId);

    return AutomationSO.createAutomationOperationWithTemplate(user, { spaceId, parentId, ...createAutomationParam });
  }

  toNodeSO() {
    return NodeSO.initWithModel(this._nodeModel);
  }

  override async toBO(): Promise<Folder> {
    return {
      resourceType: 'FOLDER',
      name: this.name,
      description: this.description,
      readme: this.readme,
      id: this.id,
      templateId: this.templateId,
      cover: this.cover,
      scope: this.scope,
      icon: this.toNodeSO().icon,
    };
  }

  async toBORecursive(opts?: ToBoOptions): Promise<Folder> {
    const children = await this.getChildren();
    const childrenBO: NodeResource[] = [];
    for (const child of children) {
      if (child.isFolder || child.isTemplate) {
        const childBO = await (await child.toResourceSO<FolderSO>()).toBORecursive(opts);
        childrenBO.push(childBO);
      } else {
        const childBO = await (await child.toResourceSO()).toBO(opts);
        childrenBO.push({
          ...childBO,
          icon: child.icon,
        });
      }
    }
    const bo = await this.toBO();
    return {
      ...bo,
      children: childrenBO.length > 0 ? childrenBO : undefined,
    };
  }

  override async toTemplate(opts?: ToTemplateOptions): Promise<Folder> {
    const children = await this.getChildren();
    const childrenBOList: NodeResource[] = [];
    for (const child of children) {
      const childSO = await child.toResourceSO();
      const childBO = await childSO.toTemplate(opts);
      childrenBOList.push(childBO);
    }
    const folder = _.omit(await this.toBO(), 'id') as Folder;
    return { ...folder, children: childrenBOList };
  }

  /**
   * Publish as Template
   *
   * @param user
   * @param param
   * @returns
   */
  async publish(user: UserSO, param: TemplatePublishDTO): Promise<string> {
    if (param.type === 'LOCAL' && !this.isTemplate) {
      throw new Error('Export only support template folder');
    }
    if (!(await this.hasChildren())) {
      throw new Error('Publish folder must have children');
    }
    // check republish
    if (param.type === 'TEMPLATE_CENTER' && this.isTemplate) {
      const storeTemplateSO = await StoreTemplateSO.init(this.templateId!);
      if (!storeTemplateSO.createdBy || storeTemplateSO.createdBy !== user.id) {
        throw new ServerError(NodeErrors.only_creator_can_republish_template);
      }
      const templateRepo = await TemplateRepoSO.initByDb(this.templateId!);
      if (templateRepo?.version === param.data.version) {
        throw new ServerError(NodeErrors.template_version_already_exist, { version: templateRepo.version });
      }
      if (templateRepo) {
        const comparator = TemplateComparator.init(templateRepo.version);
        if (!comparator.lessThan(param.data.version)) {
          throw new ServerError(NodeErrors.template_version_should_be_greater_than_current);
        }
      }
    }
    // export to local
    if (param.type === 'LOCAL') {
      const templateRepo = await TemplateRepoSO.init(this.templateId!);
      return this.toNodeSO().exportBikafileAndGetDownloadUrl(
        {
          format: 'TEMPLATE',
          template: templateRepo.currentTemplate,
          readme:
            typeof templateRepo.readme === 'string' ? { [user.locale]: templateRepo.readme } : templateRepo.readme,
          releases: [...templateRepo.releases, { data: templateRepo.currentTemplate, version: templateRepo.version }],
        },
        user.locale,
      );
    }
    // format template bo
    const option = param.type === TemplatePublishType.TEMPLATE_CENTER ? param.data : undefined;
    const template = await this.makeTemplateRepo(user, option);
    // template center operation
    await this.publishToTemplateCenterOperation(user.id, template);
    if (isInCI()) {
      await EventSO.folder.onPublish(template.templateId);
    } else {
      EventSO.folder.onPublish(template.templateId);
    }
    return template.templateId;
  }

  async export(opts?: ExportBOOptions): Promise<Folder> {
    const allChildIds = await this.getAllChildIds();
    const idPathStore = new TemplateIdPathStore();
    // TODO: 这里没有进一步加载节点的数据，如表格的视图、字段等；会导致导出时无法校验到，关联的视图是否真实存在
    allChildIds.forEach((id) => {
      idPathStore.set(id, id);
    });
    const getInstanceId = (id: string): string | undefined => idPathStore.getMayBeNull(id);
    const exportFolder = async (folderSO: FolderSO) => {
      const childrenBO: NodeResource[] = [];
      const children = await folderSO.getChildren();
      for (const child of children) {
        const childResourceSO = await child.toResourceSO();
        if (child.isFolder || child.isTemplate) {
          childrenBO.push(await exportFolder(childResourceSO as FolderSO));
        } else {
          childrenBO.push(await childResourceSO.export({ ...opts, getInstanceId }));
        }
      }
      return {
        ...(await folderSO.toBO()),
        children: childrenBO.length > 0 ? childrenBO : undefined,
      };
    };
    return exportFolder(this);
  }

  /**
   * 将当前文件夹制作成template repo
   *
   * @param user
   * @param option
   * @returns
   */
  private async makeTemplateRepo(user: UserSO, option?: StoreTemplateCreateDTO): Promise<CustomTemplate> {
    // used for check folder relation, whether linked to another resource witch outside the current folder
    const idPathStore = initIdPathStore([await this.toBORecursive({ withRecords: option?.withRecords })]);
    const getTemplateId = (id: string) => idPathStore.getMayBeNull(id);
    // only first publish can set templateId
    const templateId: string = this.isTemplate
      ? this.templateId!
      : option?.templateId || generateNanoID(CONST_PREFIX_TEMPLATE);
    const templateRepo = this.isTemplate ? await TemplateRepoSO.initByDb(this.templateId!) : undefined;
    const resource = await this.toTemplate({
      withRecords: option?.withRecords || false,
      getTemplateId,
      templateId,
    });
    const getVersion = () => {
      if (option?.version) {
        return option.version;
      }
      return templateRepo?.version || '0.0.1';
    };
    const getAuthor = () => {
      if (option) {
        return {
          userId: user.id,
          spaceId: this.spaceId,
          display: option.authorDisplay || 'USER',
        };
      }
      return user.email || user.name || templateRepo?.author;
    };
    const {
      success,
      error,
      data: template,
    } = CustomTemplateSchema.safeParse({
      // keep original data such as initMissions
      ...templateRepo?.currentTemplate,
      templateId,
      category: option?.category || templateRepo?.category || 'project',
      cover: this.cover || { type: 'COLOR', color: 'BLUE' },
      name: this.name,
      version: getVersion(),
      description: option?.description || this.description,
      readme: this.readme,
      schemaVersion: 'v1',
      detach: option?.detach,
      visibility: option?.visibility || templateRepo?.visibility,
      author: getAuthor(),
      // rewrite Node Resources
      resources: (resource as Folder).children || [],
      useCases: option?.useCases || templateRepo?.useCases,
      keywords: option?.keywords || templateRepo?.keywords,
      initMissions: option?.initMissions || templateRepo?.initMissions,
    });
    if (!success) {
      Logger.error(`schema parse error`, error);
      throw new Error('schema parse error');
    }
    return template;
  }

  /**
   * 创建报表模板
   * @param reportTemplate report template
   */
  async createReportTemplate(
    reportTemplate: ReportTemplate,
  ): Promise<{ node: NodeSO; reportTemplate: ReportTemplateSO }> {
    const reportTemplateSO = await ReportTemplateSO.createReportTemplateInParentNode(this.toNodeSO(), reportTemplate);
    const nodeSO = NodeSO.initWithModel(reportTemplateSO.model.node);
    return { node: nodeSO, reportTemplate: reportTemplateSO };
  }

  async createWebPage(_webPageTpl: WebPage): Promise<{ node?: NodeSO; webPage?: WebPageSO }> {
    return { node: undefined, webPage: undefined };
  }

  override relationInstanceId(folder: Folder, opts: IRelationIdOpts): boolean {
    for (const child of folder?.children ?? []) {
      const childSO = NodeResourceAdapter.new(child.resourceType);
      childSO.relationInstanceId(child, opts);
    }
    return true;
  }

  override async setTemplateId() {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    const children = await this.getChildren();
    for (const child of children) {
      const resourceSO = await child.toResourceSO();
      const childOperations = await resourceSO.setTemplateId();
      operations.push(...childOperations);
      if (child.isFolder && !child.templateId) {
        operations.push(...(resourceSO as FolderSO).setTemplateIdWithIdOperation());
      }
    }
    return operations;
  }

  /**
   * 创建模板类型节点，此操作只能在根节点上操作
   * 默认放在第一位，但是这方法只是一个db操作，需要调用者自己组合节点移动操作组合完成
   * @param userId user id
   * @param createParam 用户上下文
   * @returns id: node id, operation: db operation
   */
  async createTemplateNodeOperation(
    userId: string,
    createParam: {
      preNodeId?: string;
      nextNodeId?: string;
      template: CustomTemplate;
      unitId?: string;
    },
  ): Promise<{ id: string; operation: PrismaPromise<NodeModel> }> {
    const { preNodeId, nextNodeId, template, unitId } = createParam;
    const templateRepo = await TemplateRepoSO.init(template.templateId);
    const id = generateNanoID(CONST_PREFIX_NODTPL);
    const cover =
      typeof templateRepo.currentTemplate.cover === 'string'
        ? {
            type: 'URL',
            url: templateRepo.currentTemplate.cover,
          }
        : templateRepo.currentTemplate.cover;
    const operation = db.prisma.node.create({
      data: {
        id,
        icon: cover,
        name: templateRepo.currentTemplate.name,
        description: templateRepo.currentTemplate.description,
        templateId: templateRepo.templateId,
        type: 'FOLDER',
        unit: unitId ? { connect: { id: unitId } } : undefined,
        folder: {
          create: {
            cover,
            readme: templateRepo.readme || undefined,
            createdBy: userId,
            updatedBy: userId,
          },
        },
        space: {
          connect: {
            id: this.spaceId,
          },
        },
        parent: {
          connect: {
            id: this.id,
          },
        },
        preNode: {
          connect: preNodeId ? { id: preNodeId } : undefined,
        },
        nextNode: {
          connect: nextNodeId ? { id: nextNodeId } : undefined,
        },
        createdBy: userId,
        updatedBy: userId,
      },
    });
    return { id, operation };
  }

  async publishToTemplateCenterOperation(userId: string, template: CustomTemplate) {
    // child publish operations
    const operations: PrismaPromise<Prisma.BatchPayload | StoreTemplateModel>[] = await this.setTemplateId();
    operations.push(StoreTemplateSO.upsertPayload(userId, this.spaceId, { ...template, readme: this.readme }));
    if (this.isTemplate) {
      operations.push(
        db.prisma.templateApply.updateMany({
          where: {
            spaceId: this.spaceId,
            templateId: this.templateId!,
            nodeId: this.id,
          },
          data: {
            templateVersion: template.version,
          },
        }),
      );
    } else {
      operations.push(
        ...[
          db.prisma.node.updateMany({
            where: {
              id: this.id,
            },
            data: {
              type: 'TEMPLATE',
              templateId: template.templateId,
            },
          }),
          db.prisma.templateApply.createMany({
            data: {
              id: generateNanoID('tpa'),
              templateId: template.templateId,
              templateVersion: template.version,
              spaceId: this.spaceId,
              nodeId: this.id,
              source: TemplateSourceType.STORE,
              type: TemplateApplyType.INSTALL,
            },
          }),
        ],
      );
    }
    await db.prisma.$transaction(operations);
  }

  /**
   * 获取当前文件夹子有效节点数量，不包括文件夹
   * @param folderId 文件夹ID
   * @returns 子节点资源数量
   */
  static async getChildResourceCount(folderId: string) {
    let count = await db.prisma.node.count({
      where: { parentId: folderId, type: { notIn: ['FOLDER', 'TEMPLATE'] } },
    });
    const folderStates = await db.prisma.node.findMany({
      where: { parentId: folderId, type: { in: ['FOLDER', 'TEMPLATE'] } },
      select: {
        state: true,
      },
    });
    for (const item of folderStates) {
      const state = item.state as NodeStateBO[];
      if (state && state.length > 0) {
        const numberState = state.find((i) => i.state === 'NUMBER');
        if (numberState) {
          count += numberState.number;
        }
      }
    }
    return count;
  }

  /**
   * 判断当前文件夹子节点是否存在错误状态
   * @param folderId 文件夹ID
   * @returns 是否存在错误状态
   */
  static async hasChildResourceError(folderId: string): Promise<boolean> {
    const count = await db.prisma.node.count({
      where: {
        parentId: folderId,
        type: { in: ['AUTOMATION', 'FOLDER', 'TEMPLATE'] },
        state: {
          array_contains: [{ state: 'ERROR', message: '' }],
        },
      },
    });
    return count > 0;
  }

  private setTemplateIdWithIdOperation() {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    if (!this.templateId) {
      operations.push(
        ...[
          db.prisma.folder.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
          db.prisma.node.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
        ],
      );
    }
    return operations;
  }
}

export class TemplateFolderSO extends FolderSO {
  /**
   * 模版类型文件夹必定有templateId
   */
  override get templateId(): string {
    return this.model.templateId!;
  }

  get resourceType(): NodeResourceType {
    return 'TEMPLATE';
  }

  static async initWithModel(model: NodeModel): Promise<TemplateFolderSO> {
    const folderPO = await this.getFolderPO(model.id);
    return new TemplateFolderSO(model, folderPO);
  }

  static async findNodeResourceByKey<T extends NodeResourceSO>(
    param: {
      nodeId?: string;
      nodeTemplateId?: string;
      templateNodeId?: string;
    },
    resourceName: string = 'node',
  ): Promise<T> {
    const { nodeId, nodeTemplateId, templateNodeId } = param;
    if (nodeId) {
      const nodeSO = await NodeSO.init(nodeId);
      return nodeSO.toResourceSO<T>();
    }
    if (!templateNodeId) {
      throw new Error(`could not find ${resourceName}, because ${resourceName} without install with template`);
    }
    if (!nodeTemplateId) {
      throw new Error(`${resourceName}Id or ${resourceName}TemplateId is required`);
    }
    const nodeSO = await TemplateFolderSO.findNodeSO(templateNodeId, nodeTemplateId);
    if (!nodeSO) {
      throw new Error(`${resourceName} not found`);
    }
    return nodeSO.toResourceSO<T>();
  }

  static async findNodeSO(templateNodeId: string, nodeTemplateId: string): Promise<NodeSO | undefined> {
    const templateNodeSO = await NodeSO.init(templateNodeId);
    const templateFolderNodeSO = await templateNodeSO.toResourceSO<TemplateFolderSO>();
    return templateFolderNodeSO.findChildNodeByTemplateId(nodeTemplateId);
  }

  /**
   * 通过当前节点ID和目标节点模板ID查找模板节点
   */
  static async findTemplateNode(currentNodeId: string, targetNodeTemplateId: string): Promise<NodeSO | undefined> {
    const nodeSO = await NodeSO.init(currentNodeId);
    const templateFolderNodeSO = await nodeSO.findTemplateFolderNode();
    if (!templateFolderNodeSO) {
      return undefined;
    }
    return templateFolderNodeSO.findChildNodeByTemplateId(targetNodeTemplateId);
  }

  /**
   * 获取节点对应模板安装的信息, 结果可能为null
   */
  async getInstallation(): Promise<SpaceTemplateInstallation | null> {
    return SpaceTemplateInstallation.init(this.spaceId, this.id);
  }

  /**
   * 当前目标文件夹是否可以升级?
   */
  async canUpgrade(): Promise<boolean> {
    const installation = await this.getInstallation();
    if (!installation) {
      // 竟然没有安装信息?应该报错
      return false;
    }
    return installation.canUpgrade();
  }

  /**
   * upgrade template
   */
  async upgradeTemplate(user: UserSO, skipVersionCompare?: boolean): Promise<TemplateFolderSO> {
    const upgrader = TemplateUpgrader.init({ user }, this);
    // 执行升级
    await upgrader.execute(skipVersionCompare);

    const reloaded = await NodeSO.init(this.id);
    const templateFolder = await TemplateFolderSO.initWithModel(reloaded.model);
    return templateFolder;
  }

  async toVO(opts?: NodeRenderOpts): Promise<TemplateFolderVO> {
    const [baseVO, installation] = await Promise.all([super.toVO(opts), this.getInstallation()]);

    const storeTemplate = await StoreTemplateSO.init(this.templateId);
    const storeTemplateVO = await storeTemplate.toVO(opts?.userId || storeTemplate.createdBy || undefined);
    const author = parseTemplateAuthor(storeTemplateVO);

    return {
      ...baseVO,
      installation: {
        templateId: installation!.templateId,
        version: installation!.templateVersion!,
      },
      author,
    };
  }

  async detach() {
    await db.prisma.node.update({
      where: { id: this.id },
      data: {
        type: 'FOLDER',
      },
    });
  }
}

export class RootFolderSO extends FolderSO {
  static async initWithModel(model: NodeModel): Promise<RootFolderSO> {
    const folderPO = await this.getFolderPO(model.id);
    return new RootFolderSO(model, folderPO);
  }

  get resourceType(): NodeResourceType {
    return 'ROOT';
  }

  /**
   * 创建的数据库操作
   * @param userId user id
   * @param spaceId space id
   */
  static createRootFolderOperation(
    userId: string,
    spaceId: string,
  ): { id: string; operation: PrismaPromise<NodeModel> } {
    const id = generateNanoID('rot');
    const operation = db.prisma.node.create({
      data: {
        id,
        name: 'ROOT',
        description: 'ROOT NODE OF SPACE (INVISIBLE)',
        type: 'ROOT',
        spaceId,
        createdBy: userId,
        updatedBy: userId,
        folder: {
          create: {
            createdBy: userId,
            updatedBy: userId,
          },
        },
      },
    });

    return { id, operation };
  }

  override async toVO(opts?: NodeRenderOpts): Promise<NodeTreeVO> {
    const nodeTreeVO = await this.toNodeTreeVOWithSQLRecursive(opts);
    return {
      ...nodeTreeVO,
      id: opts?.scope === 'PRIVATE' ? PRIVATE_ROOT_NODE_PREFIX + this.id : this.id,
      scope: opts?.scope,
    };
  }

  async searchChildren(params: {
    nodeId?: string;
    keyword?: string;
    resourceType?: NodeResourceType;
    privateUnitId?: string;
    pageSize: number;
    pageNo: number;
  }): Promise<NodeSO[]> {
    const { resourceType, privateUnitId, pageNo, pageSize, nodeId } = params;
    const parentId = this.id;
    const keyword = `%${params.keyword}%`;
    const poList = await db.prisma.$queryRaw<NodeModel[]>`
      SELECT * FROM "public"."Node" node INNER JOIN (
        SELECT id FROM  "public"."Node" 
        WHERE "spaceId" = ${this.spaceId}
        AND id != ${parentId}
        ${resourceType ? Prisma.sql`AND cast(type as TEXT) = ${resourceType}` : Prisma.empty}
        ${keyword ? Prisma.sql`AND cast(name as TEXT) ILIKE ${keyword}` : Prisma.empty}
        ${privateUnitId ? Prisma.sql`AND "unitId" = ${privateUnitId}` : Prisma.sql`AND "unitId" IS NULL`}
        ${nodeId ? Prisma.sql`AND id != ${nodeId}` : Prisma.empty}
        LIMIT ${pageSize} OFFSET ${(pageNo - 1) * pageSize}
      ) node_id ON node.id = node_id.id
    `;
    const nodeIds = poList.filter((i) => i.parentId !== this.id).map((i) => i.id);
    const parentsMap = await NodeSO.queryParents(nodeId ? [...nodeIds, nodeId] : nodeIds);
    const result = poList.map((po) => NodeSO.initWithModel(po, parentsMap.get(po.id)));
    // node id on the top of the list
    if (nodeId) {
      const node = await NodeSO.init(nodeId);
      result.unshift(NodeSO.initWithModel(node.model, parentsMap.get(nodeId)));
    }
    return result;
  }
}
