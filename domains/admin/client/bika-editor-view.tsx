'use client';

import dynamic from 'next/dynamic';
import { useState } from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { CustomTemplate, CustomTemplateSchema } from '@bika/types/template/bo';
import { FlowEditor } from '@bika/ui/editor/flow-editor';
import { type IFlowEditorData } from '@bika/ui/editor/flow-editor/use-flow-data';
import { Box, Grid } from '@bika/ui/layouts';
import { List, ListItem, ListItemButton } from '@bika/ui/list';
import { Tabs, Tab, TabList, TabPanel } from '@bika/ui/tabs';
import { BikaCodeEditorView } from './bika-code-editor-view';
import { LocalEditorView } from './bika-local-editor-view';

const JsonView = dynamic(() => import('@bika/ui/json-view').then((module) => module.JsonView), {
  loading: () => <Box>loading...</Box>,
  ssr: false,
});

/**
 *
 * LOCAL文件版
 *
 * @returns
 */
export function BikaEditorView() {
  const locale = useLocale();
  const [templateId, setTemplateId] = useState<string | null>(null);

  const [tabValue, setTabValue] = useState<number>(0);
  const [editTemplate, setEditTemplate] = useState<CustomTemplate | null>(null); // [templateId, setTemplateId

  const trpcQuery = useTRPCQuery();
  const { data: localTemplates, isLoading: isLoadingLocalTemplates } =
    trpcQuery.admin.templates.localTemplates.useQuery();
  const {
    data: templateBO,
    isLoading: isLoadingTemplate,
    refetch: refetchTemplate,
  } = trpcQuery.admin.templates.getLocalTemplateBO.useQuery(
    {
      templateId: templateId || 'DISABLE',
    },
    {
      enabled: templateId !== null,
      onSuccess: (data) => {
        setEditTemplate(data as CustomTemplate);
        // setEditCode(JSON.stringify(data, null, 2));
        // setChanged(false);
      },
    },
  );

  const doSaveMutate = trpcQuery.admin.templates.saveLocalTemplateBO.useMutation();
  const isLoading = isLoadingLocalTemplates || isLoadingTemplate;

  const doSave = () => {
    // if (!editCode) return;

    const formated = CustomTemplateSchema.parse(editTemplate);
    doSaveMutate.mutate(
      {
        template: formated,
      },
      {
        onSuccess: () => {
          // setChanged(false);
          refetchTemplate();
        },
        onError: (err) => {
          console.log(err);
        },
      },
    );
  };
  return (
    <>
      <Grid container overflow={'hidden'} sx={{ height: '100%', flex: 1 }}>
        <Grid xs={3} overflow={'auto'} height={'900px'}>
          <List>
            {localTemplates &&
              localTemplates.map((template, idx) => (
                <ListItem key={idx}>
                  <ListItemButton
                    onClick={() => {
                      setTemplateId(template.templateId);
                      setTabValue(0); // 恢复标签页到第一个
                    }}
                  >
                    {template.templateId}
                  </ListItemButton>
                </ListItem>
              ))}
          </List>
        </Grid>
        <Grid xs={9}>
          {templateId && isLoading && 'loading'}
          {editTemplate && (
            <>
              <Tabs
                sx={{ height: '100%' }}
                value={tabValue}
                onChange={(_, newTabValue) => {
                  setTabValue(newTabValue as number);
                }}
              >
                <TabList>
                  <Tab variant="plain" color="neutral">
                    Architecture Preview
                  </Tab>
                  <Tab variant="plain" color="neutral">
                    Form Editor
                  </Tab>
                  <Tab variant="plain" color="neutral">
                    JSON Preview
                  </Tab>
                  <Tab variant="plain" color="neutral">
                    Code Preview
                  </Tab>
                  <Tab variant="plain" color="neutral">
                    YAML Preview
                  </Tab>
                </TabList>
                <TabPanel value={0}>
                  {templateBO && (
                    <FlowEditor
                      readonly
                      showControl
                      data={
                        {
                          type: 'node-resources',
                          resources: templateBO.resources,
                        } as IFlowEditorData
                      }
                    />
                  )}
                </TabPanel>
                <TabPanel value={1}>{templateId && <LocalEditorView templateId={templateId!} />}</TabPanel>

                <TabPanel value={2}>
                  <JsonView name={editTemplate.templateId} src={editTemplate} displayDataTypes={false} />
                </TabPanel>

                <TabPanel value={3}>
                  <BikaCodeEditorView
                    onSave={(_changedTpl) => {
                      setEditTemplate(_changedTpl);
                      doSave();
                    }}
                    mode="json"
                    template={editTemplate}
                  />
                </TabPanel>
                <TabPanel value={4}>
                  <BikaCodeEditorView
                    onSave={(_changedTpl) => {
                      setEditTemplate(_changedTpl);
                      doSave();
                    }}
                    mode="yaml"
                    template={editTemplate}
                  />
                </TabPanel>
              </Tabs>
            </>
          )}
        </Grid>
      </Grid>
    </>
  );
}
