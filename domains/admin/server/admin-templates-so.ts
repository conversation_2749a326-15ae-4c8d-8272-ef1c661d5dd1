/* eslint-disable no-fallthrough */
import _ from 'lodash';
import { LocalContentLoader } from '@bika/server-orm';
import { Action, Automation, Trigger } from '@bika/types/automation/bo';
import { AutomationActionVO, AutomationTriggerVO, AutomationVO } from '@bika/types/automation/vo';
import { Dashboard } from '@bika/types/dashboard/bo';
import { DashboardVO, WidgetVO } from '@bika/types/dashboard/vo';
import { Database, DatabaseField, View } from '@bika/types/database/bo';
import { DatabaseViewUpdateDTO, FieldUpdateDTO } from '@bika/types/database/dto';
import { DatabaseVO, FieldVO, ViewVO } from '@bika/types/database/vo';
import { Folder, NodeResource, type NodeResourceType } from '@bika/types/node/bo';
import { UpdateResourceDTO } from '@bika/types/node/dto';
import { FolderVO, NodeDetailVO, NodeRenderOpts, NodeTreeVO, ResourceVO } from '@bika/types/node/vo';
import { iStringParse, RenderOption } from '@bika/types/system';
import { CustomTemplate } from '@bika/types/template/bo';
import { StoreTemplateVO } from '@bika/types/template/vo';

const { get, set, keyBy, filter } = _;

/**
 * 编辑器CustomTemplate的local模板
 */
export class AdminTemplatesSO {
  private readonly _template: CustomTemplate;

  private readonly _resource?: NodeResource;

  private readonly _resourcePath?: string;

  get template(): CustomTemplate {
    return this._template;
  }

  get templateId(): string {
    return this._template.templateId;
  }

  get resource() {
    return this._resource;
  }

  get children() {
    return this.template.resources;
  }

  get name() {
    return this.template.name;
  }

  get description() {
    return this.template.description;
  }

  constructor(template: CustomTemplate, resource?: NodeResource, resourcePath?: string) {
    this._template = template;
    this._resource = resource;
    this._resourcePath = resourcePath;
  }

  static async initWithTemplateId(templateId: string): Promise<AdminTemplatesSO> {
    const template = await LocalContentLoader.template.fsLocalTemplateCurrentJSON(templateId);
    if (!template) {
      throw new Error(`template not found templateId: ${template}`);
    }
    return new AdminTemplatesSO(template);
  }

  static async initWithResourceId(resourceTemplateId: string): Promise<AdminTemplatesSO> {
    const { template, resourcePath } = await this.findTemplateByResourceTemplateId(resourceTemplateId);
    if (!template) {
      throw new Error(`Template not found, id: ${resourceTemplateId}`);
    }
    if (resourcePath) {
      const resource = get(template, resourcePath!);
      return new AdminTemplatesSO(template, resource, resourcePath);
    }
    return new AdminTemplatesSO(template);
  }

  toNodeTreeVO(opts?: RenderOption): NodeTreeVO {
    const toVO = (resource: NodeResource) => {
      const data = {
        id: resource.templateId,
        type: resource.resourceType,
        templateId: resource.templateId,
        name: iStringParse(resource.name, opts?.locale),
        description: iStringParse(resource.description, opts?.locale),
        sharing: false,
        hasShareLock: false,
        hasPermissions: false,
        children:
          resource.resourceType === 'FOLDER' && (resource as Folder)?.children?.map((i) => toVO(i as NodeResource)),
      } as NodeTreeVO;
      return data;
    };
    if (this.resource) {
      return toVO(this.resource);
    }
    return toVO({
      ...this.template,
      resourceType: 'FOLDER',
      children: this.children,
    } as Folder);
  }

  /**
   * just used for folder.
   * @param opts locale
   * @returns
   */
  async toNodeDetailVO(opts?: NodeRenderOpts): Promise<NodeDetailVO> {
    const toDetailVO = async (data: NodeResource): Promise<ResourceVO | undefined> => {
      switch (data.resourceType) {
        case 'DATABASE': {
          return this.toDatabaseVO(data as Database, opts);
        }
        case 'FOLDER': {
          return this.toFolderVO(data as Folder, opts);
        }
        case 'AUTOMATION': {
          return this.toAutomationVO(data as Automation, opts);
        }
        case 'DASHBOARD': {
          return this.toDashboardVO(data as Dashboard, opts);
        }
        default:
          return undefined;
      }
    };
    if (this.resource) {
      return {
        id: this.resource.templateId!,
        type: this.resource.resourceType,
        name: iStringParse(this.resource.name, opts?.locale),
        description: iStringParse(this.resource.description, opts?.locale),
        scope: 'SPACE',
        resource: await toDetailVO(this.resource),
      };
    }
    return {
      id: this.template.templateId,
      type: 'FOLDER',
      name: iStringParse(this.template.name, opts?.locale),
      description: iStringParse(this.template.description, opts?.locale),
      scope: 'SPACE',
      resource: await toDetailVO({
        templateId: this.template.templateId,
        name: this.template.name,
        description: this.template.description,
        cover: this.template.cover,
        resourceType: 'FOLDER',
        children: this.children,
      } as Folder),
    };
  }

  static async localTemplatesAsRootNodeVO(opts?: RenderOption): Promise<NodeTreeVO> {
    const templates = LocalContentLoader.template.fsTemplatesList();
    // 根目录children，全部是本地local模板们
    const rootChildrens: NodeTreeVO[] = [];
    for (const tpl of templates) {
      const adminTemplatesSO = await this.initWithTemplateId(tpl.templateId);
      const nodeTree = adminTemplatesSO.toNodeTreeVO(opts);
      rootChildrens.push(nodeTree);
    }
    const rootNode: NodeTreeVO = {
      id: 'LOCAL_ROOT_NODE',
      templateId: 'LOCAL_ROOT_NODE',
      type: 'FOLDER',
      name: 'Local Templates',
      description: 'Here is all Local Templates',
      // isDatabase: false,
      sharing: false,
      hasShareLock: false,
      hasPermissions: false,
      children: rootChildrens,
    };
    return rootNode;
  }

  static async localTemplateVO(templateId: string): Promise<StoreTemplateVO> {
    return LocalContentLoader.template.importLocalTemplateRepo(templateId);
  }

  static async updateResource(resourceId: string, data: UpdateResourceDTO) {
    const { template, resourcePath } = await this.findTemplateByResourceTemplateId(resourceId);
    if (!template) {
      throw new Error(`Template not found for resourceTemplateId: ${resourceId}`);
    }

    Object.keys(data).forEach((key: string) => {
      const path = resourcePath ? `${resourcePath}${key}` : key;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      set(template, path, (data as any)[key]);
    });
    await LocalContentLoader.template.fsSaveLocalTemplateBO(template);
  }

  async deleteDatabaseView(viewId: string) {
    const template = this.template;
    const views = filter((this.resource as Database)!.views, (field) => field.templateId !== viewId);
    const fieldsPath = `${this._resourcePath}.views`;
    set(template, fieldsPath, views);
    await LocalContentLoader.template.fsSaveLocalTemplateBO(template);
  }

  async updateDatabaseView(viewId: string, view: DatabaseViewUpdateDTO) {
    const template = this.template;
    const viewPath = this.getDatabaseViewPathById(viewId);
    Object.keys(view).forEach((key: string) => {
      const path = `${viewPath}${key}`;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      set(template, path, (view as any)[key]);
    });
    await LocalContentLoader.template.fsSaveLocalTemplateBO(template);
  }

  async updateDatabaseField(newField: FieldUpdateDTO['field']) {
    const template = this.template;
    const fieldPath = this.getDatabaseFieldPathById(newField.templateId!);
    Object.keys(newField).forEach((key: string) => {
      if (key !== 'id') {
        const path = `${fieldPath}${key}`;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        set(template, path, (newField as any)[key]);
      }
    });
    await LocalContentLoader.template.fsSaveLocalTemplateBO(template);
  }

  async deleteDatabaseField(fieldId: string) {
    const template = this.template;
    const fields = filter((this.resource as Database)!.fields, (field) => field.templateId !== fieldId);
    const fieldsPath = `${this._resourcePath}.fields`;
    set(template, fieldsPath, fields);
    await LocalContentLoader.template.fsSaveLocalTemplateBO(template);
  }

  /**
   * 传入资源类型和模板ID，找到对应的资源类型
   *
   * @param templateId
   * @param resourceType
   */
  static async findResourceByTemplateIdAndType(
    templateId: string,
    resourceType: NodeResourceType,
  ): Promise<NodeTreeVO[]> {
    const localTemplateSO = await this.initWithTemplateId(templateId);
    const tplRoot = localTemplateSO.toNodeTreeVO();
    return tplRoot.children!.filter((i) => i.type === resourceType);
  }

  static async findTemplateByResourceTemplateId(
    resourceTemplateId: string,
  ): Promise<{ template?: CustomTemplate; resourcePath?: string }> {
    const templates = LocalContentLoader.template.fsTemplatesList();
    const templateIds = templates.filter((i) => i.templateId === resourceTemplateId).map((i) => i.templateId);
    if (templateIds.length > 1) {
      throw new Error(`Multiple templates found for templateId: ${resourceTemplateId}`);
    }
    if (templateIds.length === 1) {
      return {
        template: await LocalContentLoader.template.importLocalCustomTemplate(templateIds[0]),
      };
    }
    const findChildPath = (parentPath: string, resources: NodeResource[]): string | undefined => {
      for (let i = 0; i < resources.length; i += 1) {
        const resource = resources[i];
        if (resource.templateId === resourceTemplateId) {
          return `${parentPath}[${i}]`;
        }
        if (resource.resourceType === 'FOLDER') {
          return findChildPath(`${parentPath}[${i}].children`, (resource as Folder).children as NodeResource[]);
        }
      }
      return undefined;
    };
    for (const template of templates) {
      const customTemplate = await LocalContentLoader.template.fsLocalTemplateCurrentJSON(template.templateId);
      if (customTemplate) {
        const parentPath = 'resources';
        const childPath = findChildPath(parentPath, customTemplate.resources);
        if (childPath && childPath !== parentPath) {
          return { template: customTemplate, resourcePath: childPath };
        }
      }
    }
    return {};
  }

  async getDatabaseField(fieldId: string): Promise<DatabaseField> {
    const database = this.resource as Database;
    if (!database) {
      throw new Error('Database not found');
    }
    const field = database.fields?.find((f) => f.templateId === fieldId);
    if (!field) {
      throw new Error(`Field not found ${database.templateId}:${fieldId}`);
    }
    return field as DatabaseField;
  }

  toDatabaseVO(database: Database, opts?: RenderOption): DatabaseVO {
    const views = database.views!;
    const fields = database.fields!;
    return {
      id: database.templateId!,
      name: iStringParse(database.name, opts?.locale),
      spaceId: '',
      views: this.toViewVO(views, fields, opts),
    };
  }

  private toViewVO(views: View[], fields: DatabaseField[], opts?: RenderOption): ViewVO[] {
    const { locale } = opts ?? {};
    const fieldMap = keyBy(fields, 'templateId');
    return views.reduce<ViewVO[]>((prev, cur, curIdx) => {
      const columns: DatabaseField[] = cur.fields ? cur.fields.map((field) => fieldMap[field.templateId!]) : fields;
      prev.push({
        id: cur.templateId!,
        name: iStringParse(cur.name, locale),
        description: iStringParse(cur.description, locale),
        type: cur.type,
        templateId: cur.templateId,
        databaseId: cur.databaseTemplateId!,
        preViewId: curIdx === 0 ? undefined : views[curIdx - 1].templateId,
        filters: cur.filters,
        columns: this.toFieldVO(columns),
      });
      return prev;
    }, []);
  }

  private toFieldVO(fields: DatabaseField[], opts?: RenderOption): FieldVO[] {
    return fields.reduce<FieldVO[]>((acc, field, curIdx) => {
      acc.push({
        id: field.templateId!,
        name: iStringParse(field.name, opts?.locale),
        description: iStringParse(field.description, opts?.locale),
        type: field.type,
        property: field.property,
        primary: curIdx === 0,
        required: field.required,
        privilege: field.privilege,
      } as FieldVO);
      return acc;
    }, []);
  }

  private toFolderVO(folder: Folder, opts?: RenderOption): FolderVO {
    const adminTemplateSO = new AdminTemplatesSO(this.template, folder);
    return {
      ...adminTemplateSO.toNodeTreeVO(opts),
      cover:
        typeof folder.cover === 'string'
          ? {
              type: 'URL',
              url: folder.cover,
            }
          : folder.cover || { type: 'COLOR', color: 'BLUE' },
    };
  }

  private toAutomationVO(automation: Automation, opts?: RenderOption): AutomationVO {
    return {
      id: automation.templateId!,
      name: iStringParse(automation.name, opts?.locale),
      description: iStringParse(automation.description, opts?.locale),
      isActive: false,
      actions: this.toAutomationActionVO(automation.actions, opts),
      triggers: this.toAutomationTriggerVO(automation.triggers, opts),
      isVerified: false,
    };
  }

  private toAutomationActionVO(actions: Action[], opts?: RenderOption): AutomationActionVO[] {
    return actions.reduce<AutomationActionVO[]>((acc, action) => {
      acc.push({
        id: action.templateId!,
        type: action.actionType,
        description: iStringParse(action.description, opts?.locale),
        bo: action,
        isVerified: false,
      });
      return acc;
    }, []);
  }

  private toAutomationTriggerVO(triggers: Trigger[], opts?: RenderOption): AutomationTriggerVO[] {
    return triggers.reduce<AutomationTriggerVO[]>((acc, trigger) => {
      acc.push({
        id: trigger.templateId!,
        type: trigger.triggerType,
        description: iStringParse(trigger.description, opts?.locale),
        bo: trigger,
        isVerified: false,
      });
      return acc;
    }, []);
  }

  private toDashboardVO(dashboard: Dashboard, opts?: RenderOption): DashboardVO {
    return {
      id: dashboard.templateId!,
      templateId: dashboard.templateId!,
      name: iStringParse(dashboard.name, opts?.locale),
      description: iStringParse(dashboard.description, opts?.locale),
      widgets: dashboard.widgets as unknown as WidgetVO[],
    };
  }

  // private async toViewNodeVO(viewNode: ViewNode, opts?: RenderOption): Promise<DatabaseVO> {
  //   const so = await AdminTemplatesSO.initWithResourceId(viewNode.databaseTemplateId!);
  //   const database = so.resource as Database;
  //   return {
  //     id: viewNode.databaseTemplateId!,
  //     name: iStringParse(database.name, opts?.locale),
  //     views: this.toViewVO([viewNode], database?.fields || [], opts),
  //     spaceId: '',
  //   };
  // }

  private getDatabaseFieldPathById(fieldTemplateId: string): string {
    const fields = (this.resource as Database)!.fields!;
    let fieldPath;
    for (let i = 0; i < fields.length; i += 1) {
      if (fields[i].templateId === fieldTemplateId) {
        fieldPath = `${this._resourcePath}.fields[${i}]`;
      }
    }
    if (!fieldPath) {
      throw new Error(`Field not found for templateId: ${fieldTemplateId}`);
    }
    return fieldPath;
  }

  private getDatabaseViewPathById(viewTemplateId: string): string {
    const views = (this.resource as Database)!.views!;
    let viewPath;
    for (let i = 0; i < views.length; i += 1) {
      if (views[i].templateId === viewTemplateId) {
        viewPath = `${this._resourcePath}.views[${i}]`;
      }
    }
    if (!viewPath) {
      throw new Error(`Field not found for templateId: ${viewTemplateId}`);
    }
    return viewPath;
  }
}
