import { Database } from '@bika/types/database/bo';
import { Datasheet, Node, VikaConnection } from '../orm';
import { IVikaNode, VikaResourceInitOption, VikaToBoOptions } from './type';
import { VikaDatasheetField } from './vika-datasheet-field';
import { VikaDatasheetMeta } from './vika-datasheet-meta';
import { VikaDatasheetRecord } from './vika-datasheet-record';
import { IVikaDatasheetView } from './vika-datasheet-view';

export class VikaDatasheet implements IVikaNode {
  private readonly _vikaConnection: VikaConnection;

  private readonly _datasheet: Datasheet;

  private _datasheetMeta: VikaDatasheetMeta | undefined;

  private _records: Map<string, VikaDatasheetRecord> | undefined;

  constructor(datasheet: Datasheet, vikaConnection: VikaConnection) {
    this._datasheet = datasheet;
    this._vikaConnection = vikaConnection;
  }

  get id(): string {
    return this._datasheet.dstId;
  }

  get name(): string {
    return this._datasheet.dstName;
  }

  get recordMap(): Map<string, VikaDatasheetRecord> {
    return this._records || new Map();
  }

  get fieldMap(): Map<string, VikaDatasheetField> {
    return this._datasheetMeta?.fields || new Map();
  }

  get firstView(): IVikaDatasheetView {
    return this._datasheetMeta!.views[0]!;
  }

  public static async init(
    node: Node,
    vikaConnection: VikaConnection,
    opts?: VikaResourceInitOption,
  ): Promise<VikaDatasheet> {
    const datasheet = await vikaConnection.datasheetRepo().findOneBy({
      dstId: node.nodeId,
    });
    if (!datasheet) {
      throw new Error(`datasheet not found: ${node.nodeId}`);
    }
    const resource = new VikaDatasheet(datasheet, vikaConnection);
    if (opts?.withCtx) {
      await resource.getMeta();
      // 获取records, 需要放到getMeta之后, 因为getRecords需要用到fieldMap
      await resource.getRecords();
    }
    return resource;
  }

  async getMeta(reload?: boolean): Promise<VikaDatasheetMeta> {
    if (this._datasheetMeta && !reload) {
      return this._datasheetMeta;
    }
    this._datasheetMeta = await VikaDatasheetMeta.init(this, this._vikaConnection);
    return this._datasheetMeta;
  }

  async getRecords(reload?: boolean): Promise<Map<string, VikaDatasheetRecord>> {
    if (this._records && !reload) {
      return this._records;
    }
    const records = await this._vikaConnection.datasheetRecordRepo().find({
      where: {
        dstId: this._datasheet.dstId,
        isDeleted: false,
      },
    });
    const archivedRecordIds: string[] = await this._vikaConnection
      .datasheetRecordArchiveRepo()
      .find({
        select: {
          recordId: true,
        },
        where: {
          dstId: this._datasheet.dstId,
          isDeleted: false,
          isArchived: true,
        },
      })
      .then((list) => list.map((item) => item.recordId));
    this._records = new Map(
      records
        .filter((record) => !archivedRecordIds.includes(record.recordId))
        .map((record) => [record.recordId, new VikaDatasheetRecord(record, this, this._vikaConnection)]),
    );
    return this._records;
  }

  async toBO(opts?: VikaToBoOptions): Promise<Database> {
    const meta = await this.getMeta();
    const records = Array.from((await this.getRecords()).values());
    const fields = Array.from(meta.fields.values());
    return {
      id: this.id,
      name: this.name,
      resourceType: 'DATABASE',
      databaseType: 'DATUM',
      views: meta.views.map((v) => v.toBO()).filter((v) => v !== null),
      fields: fields.map((f) => f.toBO(opts)).filter((f) => f !== undefined),
      records: await Promise.all(records.map((record) => record.toBO(opts))),
    };
  }
}
