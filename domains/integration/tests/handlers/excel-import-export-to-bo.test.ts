import assert from 'assert';
import Excel from 'exceljs';
import { describe, expect, test } from 'vitest';
import { DatabaseFieldWithId } from '@bika/types/database/bo';
import { testDatabaseBO, testWorkbook } from './excel.testset';
import { ExcelImporter } from '../../server/handlers/excel-importer-handler';

/**
 * 新增Excel导入，本质就是转成Bikafile的BO
 */
test('Import new Excel: Excel to BO', async () => {
  // 创建 Excel Sheet 用于测试
  const createTestExcelSheet = () => {
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet();

    // Header
    worksheet.columns = [
      // A
      { header: 'String', key: 'string', width: 20 },
      // B
      { header: 'Number', key: 'number', width: 15 },
      // C
      { header: 'Date', key: 'date', width: 20 },
      // D
      { header: 'Boolean', key: 'boolean', width: 15 },
      // E
      { header: 'Formula', key: 'formula', width: 20 },
      // F
      { header: 'Rich Text', key: 'richText', width: 30 },
      // G
      { header: '', key: 'empty', width: 10 },
    ];

    // Rows
    worksheet.addRow({
      string: 'Title 1',
      number: 12345,
      date: new Date(2020, 0, 1), // 2020-01-01
      boolean: true,
      formula: { formula: 'B2*10', result: 123450 },
      richText: {
        richText: [
          { text: 'Bold', font: { bold: true } },
          { text: ' and ', font: { italic: true } },
          { text: 'Italic', font: { italic: true } },
        ],
      },
      // Title 为空, 会被忽略
      empty: 'empty 1',
    });
    worksheet.addRow({
      string: 'Title 2',
      number: 67890,
      date: new Date(2020, 0, 2), // 2020-01-02
      boolean: false,
      formula: { formula: 'B3*10', result: 678900 },
      richText: {
        richText: [
          { text: 'Bold', font: { bold: true } },
          { text: ' and ', font: { italic: true } },
          { text: 'Italic', font: { italic: true } },
        ],
      },
      // Title 为空, 会被忽略
      empty: 'empty 2',
    });

    return worksheet;
  };

  const sheet = createTestExcelSheet();

  // Excel to DatabaseBO
  const databaseBO = ExcelImporter.toDatabaseBO('Test Excel Import', sheet);

  expect(databaseBO.resourceType).toBe('DATABASE');
  expect(databaseBO.databaseType).toBe('DATUM');
  expect(databaseBO.name).toBe('Test Excel Import');

  // Check fields
  const fields = databaseBO.fields! as DatabaseFieldWithId[];
  expect(fields.length).toBe(7);
  expect(fields[0].id).toBeDefined();
  expect(fields[0].name).toBe('String');
  expect(fields[0].type).toBe('LONG_TEXT');
  expect(fields[1].id).toBeDefined();
  expect(fields[1].name).toBe('Number');
  expect(fields[1].type).toBe('LONG_TEXT');
  expect(fields[2].id).toBeDefined();
  expect(fields[2].name).toBe('Date');
  expect(fields[2].type).toBe('LONG_TEXT');
  expect(fields[3].id).toBeDefined();
  expect(fields[3].name).toBe('Boolean');
  expect(fields[3].type).toBe('LONG_TEXT');
  expect(fields[4].id).toBeDefined();
  expect(fields[4].name).toBe('Formula');
  expect(fields[4].type).toBe('LONG_TEXT');
  expect(fields[5].id).toBeDefined();
  expect(fields[6].name).toBe('field_7');
  expect(fields[6].type).toBe('LONG_TEXT');

  // Check records
  const records = databaseBO.records!;
  expect(records.length).toBe(2);
  expect(records[1].data[fields[0].id]).toBe('Title 1');
  expect(records[1].data[fields[1].id]).toBe('12345');
  expect(records[1].data[fields[2].id]).toBe('2020-01-01T00:00:00.000Z');
  expect(records[1].data[fields[3].id]).toBe('true');
  expect(records[1].data[fields[4].id]).toBe('123450');
  expect(records[1].data[fields[5].id]).toBe('Bold and Italic');

  expect(records[0].data[fields[0].id]).toBe('Title 2');
  expect(records[0].data[fields[1].id]).toBe('67890');
  expect(records[0].data[fields[2].id]).toBe('2020-01-02T00:00:00.000Z');
  expect(records[0].data[fields[3].id]).toBe('false');
  expect(records[0].data[fields[4].id]).toBe('678900');
  expect(records[0].data[fields[5].id]).toBe('Bold and Italic');
});

/**
 *  一个Database导出Excel，转成Bikafile (memory)
 */
test('Export Excel: Database VO to BO to Excel ', async () => {
  // 1. 从数据库中读取数据，转成VO
  // DatabaseSO.toVO();,  ViewSO.toVO(); RecordsVO
  // 2. Excel转换器，
  //   const excelFile = ExcelImporter.exportExcel(view, records);

  const database = testDatabaseBO();

  // 需要的 Fields
  const view = database.views![0];
  const fields = database.fields! as DatabaseFieldWithId[];
  const excelFields = ExcelImporter.convertToExcelFields(fields, view);

  // 生成 Excel
  const workbook = new Excel.Workbook();
  const worksheet = workbook.addWorksheet();
  assert(database.records);
  ExcelImporter.exportToWorksheet(worksheet, excelFields, database.records, 'en');

  // 检查
  const header = worksheet.getRow(1);
  expect(header.getCell(1).value).toBe('SingleText');
  expect(header.getCell(2).value).toBe('LongText');
  expect(header.getCell(3).value).toBe('Phone');
  expect(header.getCell(4).value).toBe('Email');
  expect(header.getCell(5).value).toBe('URL');
  expect(header.getCell(6).value).toBe('Number');
  expect(header.getCell(7).value).toBe('Currency');
  expect(header.getCell(8).value).toBe('Percent');
  expect(header.getCell(9).value).toBe('Rating');
  expect(header.getCell(10).value).toBe('AutoNumber');
  expect(header.getCell(11).value).toBe('Checkbox');
  expect(header.getCell(12).value).toBe('Datetime');
  expect(header.getCell(13).value).toBe('SingleSelect');
  expect(header.getCell(14).value).toBe('MultiSelect');
  expect(header.getCell(15).value).toBe('Member');
  expect(header.getCell(16).value).toBe('Attachment');
  expect(header.getCell(17).value).toBe('OneWayLink');
  expect(header.getCell(18).value).toBe('Link');
  expect(header.getCell(19).value).toBe('Lookup');
  expect(header.getCell(20).value).toBe('Formula');
  expect(header.getCell(21).value).toBe('CreatedBy');
  expect(header.getCell(22).value).toBe('ModifiedBy');
  expect(header.getCell(23).value).toBe('CreatedTime');
  expect(header.getCell(24).value).toBe('ModifiedTime');

  const row1 = worksheet.getRow(2);
  expect(row1.getCell(1).value).toBe('Single Text 1');
  expect(row1.getCell(2).value).toBe('Long Text 1');
  expect(row1.getCell(3).value).toBe('12345678');
  expect(row1.getCell(4).value).toBe('<EMAIL>');
  expect(row1.getCell(5).value).toBe('https://www.google.com');
  expect(row1.getCell(6).value).toBe(12345);
  expect(row1.getCell(7).value).toBe(12345);
  expect(row1.getCell(8).value).toBe(0.12345);
  expect(row1.getCell(9).value).toBe(3);
  expect(row1.getCell(10).value).toBe(1);
  expect(row1.getCell(11).value).toBe(true);
  expect(row1.getCell(12).value).toBe('2020-01-01 00:00');
  expect(row1.getCell(13).value).toBe('Option 1');
  expect(row1.getCell(14).value).toBe('Option 1, Option 2');
  // TODO: Member
  expect(row1.getCell(15).value).toBe(null);
  // TODO: Attachment
  expect(row1.getCell(16).value).toBe('attachment1, attachment2');
  // TODO: OneWayLink
  expect(row1.getCell(17).value).toBe(null);
  // TODO: Link
  expect(row1.getCell(18).value).toBe(null);
  // TODO: Lookup
  expect(row1.getCell(19).value).toBe(null);
  // TODO: Formula
  expect(row1.getCell(20).value).toBe(null);
  // TODO: CreatedBy
  expect(row1.getCell(21).value).toBe(null);
  // TODO: ModifiedBy
  expect(row1.getCell(22).value).toBe(null);
  expect(row1.getCell(23).value).toBe('2020-01-01 00:00');
  expect(row1.getCell(24).value).toBe('2020-01-01 00:00');
});

/**
 * 下载增量导入Excel使用的模板
 */
describe('Download Incremental Excel Template', async () => {
  const database = testDatabaseBO();

  // 需要导出的 Fields
  const fields = database.fields! as DatabaseFieldWithId[];
  const excelFields = ExcelImporter.convertToExcelFields(fields);

  test('Generate Incremental Excel Template (en)', async () => {
    const workbook = new Excel.Workbook();
    const sheet = workbook.addWorksheet();
    ExcelImporter.generateIncrementalTemplate(sheet, excelFields, 'en');

    const { success } = ExcelImporter.validateSheetHeader(sheet, excelFields, 'en');
    expect(success).toBeTruthy();

    // 检查
    const header = sheet.getRow(1);
    expect(header.getCell(1).value).toBe('SingleText');
    expect(header.getCell(2).value).toBe('LongText');
    expect(header.getCell(3).value).toBe('Phone');
    expect(header.getCell(4).value).toBe('Email');
    expect(header.getCell(5).value).toBe('URL');
    expect(header.getCell(6).value).toBe('Number');
    expect(header.getCell(7).value).toBe('Currency');
    expect(header.getCell(8).value).toBe('Percent');
    expect(header.getCell(9).value).toBe('Rating');
    expect(header.getCell(10).value).toBe('AutoNumber');
    expect(header.getCell(11).value).toBe('Checkbox');
    expect(header.getCell(12).value).toBe('Datetime');
    expect(header.getCell(13).value).toBe('SingleSelect');
    expect(header.getCell(14).value).toBe('MultiSelect');
    expect(header.getCell(15).value).toBe('Member');
    expect(header.getCell(16).value).toBe('Attachment');
    expect(header.getCell(17).value).toBe('OneWayLink');
    expect(header.getCell(18).value).toBe('Link');
    expect(header.getCell(19).value).toBe('Lookup');
    expect(header.getCell(20).value).toBe('Formula');
    expect(header.getCell(21).value).toBe('CreatedBy');
    expect(header.getCell(22).value).toBe('ModifiedBy');
    expect(header.getCell(23).value).toBe('CreatedTime');
    expect(header.getCell(24).value).toBe('ModifiedTime');
  });

  test('Generate Incremental Excel Template (zh-CN)', async () => {
    const workbook = new Excel.Workbook();
    const sheet = workbook.addWorksheet();
    ExcelImporter.generateIncrementalTemplate(sheet, excelFields, 'zh-CN');

    const { success } = ExcelImporter.validateSheetHeader(sheet, excelFields, 'zh-CN');
    expect(success).toBeTruthy();

    // 检查
    const header = sheet.getRow(1);
    expect(header.getCell(1).value).toBe('单行文本');
    expect(header.getCell(2).value).toBe('长文本');
    expect(header.getCell(3).value).toBe('电话');
    expect(header.getCell(4).value).toBe('邮箱');
    expect(header.getCell(5).value).toBe('网址');
    expect(header.getCell(6).value).toBe('数字');
    expect(header.getCell(7).value).toBe('货币');
    expect(header.getCell(8).value).toBe('百分比');
    expect(header.getCell(9).value).toBe('评分');
    expect(header.getCell(10).value).toBe('自动编号');
    expect(header.getCell(11).value).toBe('复选框');
    expect(header.getCell(12).value).toBe('日期时间');
    expect(header.getCell(13).value).toBe('单选');
    expect(header.getCell(14).value).toBe('多选');
    expect(header.getCell(15).value).toBe('成员');
    expect(header.getCell(16).value).toBe('附件');
    expect(header.getCell(17).value).toBe('单向关联');
    expect(header.getCell(18).value).toBe('关联');
    expect(header.getCell(19).value).toBe('查找');
    expect(header.getCell(20).value).toBe('公式');
    expect(header.getCell(21).value).toBe('创建者');
    expect(header.getCell(22).value).toBe('修改者');
    expect(header.getCell(23).value).toBe('创建时间');
    expect(header.getCell(24).value).toBe('修改时间');
  });
});

describe('Validate Excel Header', async () => {
  test('should validate Excel Header (success)', async () => {
    const { sheet } = testWorkbook();

    // databaseBO
    const database = testDatabaseBO();
    const fields = database.fields! as DatabaseFieldWithId[];

    // 检查excel模板是否符合，避免乱上传一个excel
    const { success } = ExcelImporter.validateSheetHeader(sheet, fields, 'en');
    expect(success).toBeTruthy();
  });

  test('should validate Excel Header (failed)', async () => {
    const { sheet } = testWorkbook();

    // databaseBO
    const database = testDatabaseBO();
    const fields = database.fields! as DatabaseFieldWithId[];

    // 修改 field name
    fields[1].name = 'LongText1';

    // 检查excel模板是否符合，避免乱上传一个excel
    const { success, reason } = ExcelImporter.validateSheetHeader(sheet, fields, 'en');
    expect(success).toBeFalsy();
    expect(reason).toEqual({
      columnKey: 'B1',
      field: fields[1],
      excelField: 'LongText',
    });
  });
});
