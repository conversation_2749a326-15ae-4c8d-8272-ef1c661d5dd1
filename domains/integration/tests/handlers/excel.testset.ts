import Excel from 'exceljs';
import { Database } from '@bika/types/database/bo';

export const testDatabaseBO = (): Database => ({
  name: 'Test Database Export',
  databaseType: 'DATUM',
  resourceType: 'DATABASE',
  views: [
    {
      id: 'view1',
      name: 'View 1',
      type: 'TABLE',
      fields: [
        // Text
        { id: 'singleText' },
        { id: 'longText' },
        { id: 'phone' },
        { id: 'email' },
        { id: 'url' },
        // Number
        { id: 'number' },
        { id: 'currency' },
        { id: 'percent' },
        { id: 'rating' },
        { id: 'autoNumber' },
        // Checkbox
        { id: 'checkbox' },
        // Datetime
        { id: 'datetime' },
        // Select
        { id: 'singleSelect' },
        { id: 'multiSelect' },
        // Member
        { id: 'member' },
        // Attachment
        { id: 'attachment' },
        // Link
        { id: 'oneWayLink' },
        { id: 'link' },
        { id: 'lookup' },
        // Formula
        { id: 'formula' },
        // AutoGenerated
        { id: 'createdBy' },
        { id: 'modifiedBy' },
        { id: 'createdTime' },
        { id: 'modifiedTime' },
      ],
    },
  ],
  fields: [
    // Text
    {
      id: 'singleText',
      name: {
        en: 'SingleText',
        'zh-CN': '单行文本',
      },
      type: 'SINGLE_TEXT',
    },
    {
      id: 'longText',
      name: {
        en: 'LongText',
        'zh-CN': '长文本',
      },
      type: 'LONG_TEXT',
    },
    {
      id: 'phone',
      name: {
        en: 'Phone',
        'zh-CN': '电话',
      },
      type: 'PHONE',
    },
    {
      id: 'email',
      name: {
        en: 'Email',
        'zh-CN': '邮箱',
      },
      type: 'EMAIL',
    },
    {
      id: 'url',
      name: {
        en: 'URL',
        'zh-CN': '网址',
      },
      type: 'URL',
    },

    // Number
    {
      id: 'number',
      name: {
        en: 'Number',
        'zh-CN': '数字',
      },
      type: 'NUMBER',
      property: {},
    },
    {
      id: 'currency',
      name: {
        en: 'Currency',
        'zh-CN': '货币',
      },
      type: 'CURRENCY',
      property: {},
    },
    {
      id: 'percent',
      name: {
        en: 'Percent',
        'zh-CN': '百分比',
      },
      type: 'PERCENT',
      property: {},
    },
    {
      id: 'rating',
      name: {
        en: 'Rating',
        'zh-CN': '评分',
      },
      type: 'RATING',
      property: { icon: { type: 'COLOR', color: '#FFF' }, max: 5 },
    },
    {
      id: 'autoNumber',
      name: {
        en: 'AutoNumber',
        'zh-CN': '自动编号',
      },
      type: 'AUTO_NUMBER',
      property: { nextId: 10 },
    },

    // Checkbox
    {
      id: 'checkbox',
      name: {
        en: 'Checkbox',
        'zh-CN': '复选框',
      },
      type: 'CHECKBOX',
    },

    // Datetime
    {
      id: 'datetime',
      name: {
        en: 'Datetime',
        'zh-CN': '日期时间',
      },
      type: 'DATETIME',
      property: { dateFormat: 'YYYY-MM-DD', includeTime: true },
    },

    // Select
    {
      id: 'singleSelect',
      name: {
        en: 'SingleSelect',
        'zh-CN': '单选',
      },
      type: 'SINGLE_SELECT',
      property: {
        options: [
          { id: 'opt1', name: 'Option 1' },
          { id: 'opt2', name: 'Option 2' },
        ],
      },
    },
    {
      id: 'multiSelect',
      name: {
        en: 'MultiSelect',
        'zh-CN': '多选',
      },
      type: 'MULTI_SELECT',
      property: {
        options: [
          { id: 'opt1', name: 'Option 1' },
          { id: 'opt2', name: 'Option 2' },
        ],
      },
    },

    // Member
    {
      id: 'member',
      name: {
        en: 'Member',
        'zh-CN': '成员',
      },
      type: 'MEMBER',
      property: { many: true },
    },

    // Attachment
    {
      id: 'attachment',
      name: {
        en: 'Attachment',
        'zh-CN': '附件',
      },
      type: 'ATTACHMENT',
    },

    // Link
    {
      id: 'oneWayLink',
      name: {
        en: 'OneWayLink',
        'zh-CN': '单向关联',
      },
      type: 'ONE_WAY_LINK',
      property: {},
    },
    {
      id: 'link',
      name: {
        en: 'Link',
        'zh-CN': '关联',
      },
      type: 'LINK',
      property: {},
    },
    {
      id: 'lookup',
      name: {
        en: 'Lookup',
        'zh-CN': '查找',
      },
      type: 'LOOKUP',
      property: {},
    },

    // Formula
    {
      id: 'formula',
      name: {
        en: 'Formula',
        'zh-CN': '公式',
      },
      type: 'FORMULA',
      property: {
        expression: '{singleText} + {number}',
      },
    },

    // AutoGenerated
    {
      id: 'createdBy',
      name: {
        en: 'CreatedBy',
        'zh-CN': '创建者',
      },
      type: 'CREATED_BY',
    },
    {
      id: 'modifiedBy',
      name: {
        en: 'ModifiedBy',
        'zh-CN': '修改者',
      },
      type: 'MODIFIED_BY',
    },
    {
      id: 'createdTime',
      name: {
        en: 'CreatedTime',
        'zh-CN': '创建时间',
      },
      type: 'CREATED_TIME',
      property: { dateFormat: 'YYYY-MM-DD', includeTime: true },
    },
    {
      id: 'modifiedTime',
      name: {
        en: 'ModifiedTime',
        'zh-CN': '修改时间',
      },
      type: 'MODIFIED_TIME',
      property: { dateFormat: 'YYYY-MM-DD', includeTime: true },
    },
  ],

  records: [
    {
      id: 'rec1',
      data: {
        singleText: 'Single Text 1',
        longText: 'Long Text 1',
        phone: '12345678',
        email: '<EMAIL>',
        url: 'https://www.google.com',
        number: 12345,
        currency: 12345,
        percent: 0.12345,
        rating: 3,
        autoNumber: 1,
        checkbox: true,
        datetime: '2020-01-01T00:00:00.000Z',
        singleSelect: ['opt1'],
        multiSelect: ['opt1', 'opt2'],
        member: ['member1', 'member2'],
        attachment: ['attachment1', 'attachment2'],
        oneWayLink: ['rec1', 'rec2'],
        link: ['rec10', 'rec11'],
        // lookup (AUTO_GENERATED)
        // formula (AUTO_GENERATED)
        createdBy: 'member1',
        modifiedBy: 'member1',
        createdTime: '2020-01-01T00:00:00.000Z',
        modifiedTime: '2020-01-01T00:00:00.000Z',
      },
    },
  ],
});

export const testWorkbook = (): {
  workbook: Excel.Workbook;
  sheet: Excel.Worksheet;
} => {
  const workbook = new Excel.Workbook();
  const sheet = workbook.addWorksheet();

  // Header
  sheet.columns = [
    { header: 'SingleText', key: 'singleText', width: 20 },
    { header: 'LongText', key: 'longText', width: 20 },
    { header: 'Phone', key: 'phone', width: 20 },
    { header: 'Email', key: 'email', width: 20 },
    { header: 'URL', key: 'url', width: 20 },
    { header: 'Number', key: 'number', width: 20 },
    { header: 'Currency', key: 'currency', width: 20 },
    { header: 'Percent', key: 'percent', width: 20 },
    { header: 'Rating', key: 'rating', width: 20 },
    { header: 'AutoNumber', key: 'autoNumber', width: 20 },
    { header: 'Checkbox', key: 'checkbox', width: 20 },
    { header: 'Datetime', key: 'datetime', width: 20 },
    { header: 'SingleSelect', key: 'singleSelect', width: 20 },
    { header: 'MultiSelect', key: 'multiSelect', width: 20 },
    { header: 'Member', key: 'member', width: 20 },
    { header: 'Attachment', key: 'attachment', width: 20 },
    { header: 'OneWayLink', key: 'oneWayLink', width: 20 },
    { header: 'Link', key: 'link', width: 20 },
    // AUTO_GENERATED
    { header: 'Lookup', key: 'lookup', width: 20 },
    { header: 'Formula', key: 'formula', width: 20 },
    { header: 'CreatedBy', key: 'createdBy', width: 20 },
    { header: 'ModifiedBy', key: 'modifiedBy', width: 20 },
    { header: 'CreatedTime', key: 'createdTime', width: 20 },
    { header: 'ModifiedTime', key: 'modifiedTime', width: 20 },
  ];

  // Rows
  sheet.addRow({
    singleText: '',
    longText: '',
    phone: '',
    email: '',
    url: '',
    number: '',
    currency: '',
    percent: '',
    rating: '',
    autoNumber: '',
    checkbox: '',
    datetime: '',
    singleSelect: '',
    multiSelect: '',
    member: '',
    attachment: '',
    oneWayLink: '',
    link: '',
    // AUTO_GENERATED (ignored)
    lookup: 'lookup1',
    formula: 'formula1',
    createdBy: 'member1',
    modifiedBy: 'member1',
    createdTime: '2020-01-01 00:00',
    modifiedTime: '2020-01-01 00:00',
  });
  sheet.addRow({
    singleText: 'Single Text 1',
    longText: 'Long Text 1',
    phone: '12345678',
    email: '<EMAIL>',
    url: 'https://www.google.com',
    number: 12345,
    currency: 12345,
    percent: 0.12345,
    rating: 3,
    autoNumber: 1,
    checkbox: true,
    datetime: '2020-01-01 00:00',
    singleSelect: 'Option 1',
    multiSelect: 'Option 1, Option 2',
    member: 'member1, member2',
    attachment: 'attachment1, attachment2',
    oneWayLink: 'rec1, rec2',
    link: 'rec10, rec11',
    // AUTO_GENERATED (ignored)
    lookup: 'lookup1',
    formula: 'formula1',
    createdBy: 'member1',
    modifiedBy: 'member1',
    createdTime: '2020-01-01 00:00',
    modifiedTime: '2020-01-01 00:00',
  });

  return { workbook, sheet };
};
