import { expect, test } from 'vitest';
import { MockContext, waitForMatchToBeMet } from '@bika/domains/__tests__/mock';
import { AutomationSO } from '@bika/domains/automation/server/automation-so';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { MissionSO } from '@bika/domains/mission/server/mission-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { AutomationSchema } from '@bika/types/automation/bo';
import { DashboardSchema } from '@bika/types/dashboard/bo';
import { DatabaseSchema } from '@bika/types/database/bo';
import { MirrorBOSchema, NodeResource } from '@bika/types/node/bo';
import { iStringParse } from '@bika/types/system';
import { TemplateRepoSO } from '../server/template-repo-so';

/**
 * OKR模板安装测试
 */
test('okr template use flow test', { timeout: 5 * 60 * 1000 }, async () => {
  const { user, space } = await MockContext.initUserContext();

  const userId = user.id;

  // 获取根节点
  const rootFolder = await space.getRootFolder();
  // 假设用户在安装模板前创建了一个节点
  const folderName = 'my first node';
  const folderNodeSO = await rootFolder.createFolderNode({ userId }, folderName);
  expect(folderNodeSO).toBeDefined();
  expect(folderNodeSO.name).toBe(folderName);
  // 应该是第一位
  expect(folderNodeSO.preNodeId).toBeNull();

  // install template
  const templateId = '@vika/okr';
  const templateSO = await TemplateRepoSO.init(templateId);
  expect(templateSO).toBeDefined();
  expect(templateSO.currentTemplate.resources).toHaveLength(7);
  const templateFolder = await space.installTemplateById(user, templateId);
  expect(templateFolder.name).toStrictEqual(templateSO.currentTemplate.name);

  // 之前创建的节点应该被挤到第二位
  const expectFolderNode = await NodeSO.init(folderNodeSO.id);
  expect(expectFolderNode).toBeDefined();
  // 在模板节点下面
  expect(expectFolderNode?.preNodeId).toBe(templateFolder.id);

  // check units data
  const roles = (await space.findRoles()).list;
  const unitBOs = templateSO.currentTemplate.presetUnits!;
  expect(unitBOs.length).toBe(roles.length - 1); // 有一个admin role，空间站时创建的，减掉
  for (const unitBO of unitBOs) {
    const foundRole = roles.find((roleSO) => roleSO.templateId === unitBO.templateId);
    expect(foundRole).toBeDefined();
    expect(foundRole!.getName()).toEqual(iStringParse(unitBO.name));
  }

  // check template nodes
  const templateChildren = await templateFolder.getChildren();

  const nodeConverter = (node: NodeResource) => {
    if (node.resourceType === 'DATABASE') {
      return DatabaseSchema.parse(node);
    }
    if (node.resourceType === 'AUTOMATION') {
      return AutomationSchema.parse(node);
    }
    if (node.resourceType === 'MIRROR') {
      return MirrorBOSchema.parse(node);
    }
    if (node.resourceType === 'DASHBOARD') {
      return DashboardSchema.parse(node);
    }
    return undefined;
  };

  // check template nodes count and sort
  for (let i = 0; i < templateChildren.length; i += 1) {
    const templateChild = templateChildren[i];
    const expectChild = templateSO.currentTemplate.resources[i];
    expect(templateChild.type).toBe(expectChild.resourceType);
    const expectNode = nodeConverter(expectChild);
    expect(expectNode).toBeDefined();
    expect(templateChild.name).toStrictEqual(expectNode!.name);
  }

  // check init mission count
  const { initMissions } = templateSO.currentTemplate;
  const missions: MissionSO[] = [];
  for (const initMission of initMissions!.toReversed()) {
    const missionSOs = await MissionSO.getMissionsBySpaceId(space.id, {
      type: initMission.type,
      status: 'PENDING',
    });
    missions.push(...missionSOs);
  }
  const missionNames = missions.map((mission) => mission.name);
  const initMissionNames = initMissions!.toReversed().map((mission) => mission.name);
  expect(missionNames).toStrictEqual(initMissionNames);
});

test('Initiate quarterly OKR collection, and members submit quarterly OKRs', async () => {
  const { user: adminUser, member: admin, space, rootFolder } = await MockContext.initUserContext();
  expect(rootFolder).toBeDefined();

  const templateId = '@vika/okr';
  const template = await TemplateRepoSO.init(templateId);
  expect(template).toBeDefined();
  const templateNode = await space.installTemplateById(adminUser, templateId);
  expect(templateNode.name).toStrictEqual(template.currentTemplate.name);

  // Invite a new user with role_okr_member

  // 寻找这个role
  const findRole = await space.getRoleByTemplateId('role_okr_member');
  expect(findRole).toBeDefined();

  const { user: memberUser } = await MockContext.createMockUser();
  const team = await space.getRootTeam();
  const member = await space.joinUser(memberUser.id, team.id, { roleIds: [findRole!.id] });
  expect(await space.getMemberCount()).toBe(2);

  const automationNode = await templateNode.findChildNodeByTemplateId('okr_write_automation');
  if (!automationNode) {
    throw new Error('automationNode not found');
  }
  const automationSO = await automationNode.toResourceSO<AutomationSO>();
  expect(await automationSO.getTriggerCount()).toBe(1);

  const addedTriggerSO = await automationSO.addTrigger(adminUser, {
    triggerType: 'MANUALLY',
  });
  expect(addedTriggerSO).toBeDefined();

  await automationSO.runWithoutTrigger(adminUser.id);
  // 成功创建了一个创建记录的任务
  const missionSOs = await member.getMissions({
    type: 'CREATE_RECORD',
    queryFilterType: 'PENDING',
  });
  expect(missionSOs.length).toBe(1);

  const databaseNodeSO = await templateNode.findChildNodeByTemplateId('okr_database');
  if (!databaseNodeSO) {
    throw new Error('databaseNodeSO not found');
  }

  // create record to complete mission by event
  const databaseSO = await databaseNodeSO.toResourceSO<DatabaseSO>();
  await databaseSO.createRecord(memberUser, member, {
    okr_quarter: ['opt1'],
    okr_review: 'test',
    okr_details: `Objective: 提升产品X的市场竞争力和用户满意度
KR1: 用户满意度调查得分提高至90%以上
KR2: 新功能上线后的用户留存率提升20%
KR3: 产品X的市场份额在目标市场增长15%`,
    okr_align_superior: [admin.id],
    okr_self_score: ['0'],
    okr_superior_score: ['0'],
  });
  // 等待一下，让创建记录的 Automation Event 执行完
  await waitForMatchToBeMet(
    async () => {
      // 完成创建记录的任务
      const completedMission = await member.getMissions({
        type: 'CREATE_RECORD',
        queryFilterType: 'COMPLETED',
      });
      return completedMission.length === 1 && completedMission[0].id === missionSOs[0].id;
    },
    10000,
    500,
  ) // 目标值为10，超时时间10秒，检查间隔500毫秒
    .then(() => console.log('Condition met within the timeout period.'))
    .catch((error: Error) => {
      throw new Error(error.message);
    });

  await waitForMatchToBeMet(
    async () => {
      // newMemberJoinMissions
      const newMemberMissions = await member.getMissions({
        type: 'REDIRECT_SPACE_NODE',
        queryFilterType: 'PENDING',
      });
      return newMemberMissions.length === 1;
    },
    10000,
    500,
  ) // 目标值为10，超时时间10秒，检查间隔500毫秒
    .then(() => console.log('Condition met within the timeout period.'))
    .catch((error: Error) => {
      throw new Error(error.message);
    });
}, 30000);
