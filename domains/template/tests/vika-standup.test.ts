import semver from 'semver';
import { expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { Automation } from '@bika/types/automation/bo';
import { Dashboard } from '@bika/types/dashboard/bo';
import { Database } from '@bika/types/database/bo';
import { NodeResource, Folder } from '@bika/types/node/bo';
import { TemplateRepoSO } from '../server/template-repo-so';

/**
 * Bika的首个上线里程碑，vika内部晨会
 */

test('vika/scrum-standup template use flow test', async () => {
  // 模拟用户创建完后，会默认创建一个空间站，系统会给默认空间站创建三个mission
  const { user, space } = await MockContext.initUserContext();

  const userId = user.id;

  // 获取根节点
  const rootFolder = await space.getRootFolder();
  // 假设用户在安装模板前创建了一个节点
  const folderName = 'my first node';
  const folderNodeSO = await rootFolder.createFolderNode({ userId }, folderName);
  expect(folderNodeSO).toBeDefined();
  expect(folderNodeSO.model.name).toBe(folderName);
  // 应该是第一位
  expect(folderNodeSO.model.preNodeId).toBeNull();

  // install template
  const templateId = '@vika/scrum-standup';
  const template = await TemplateRepoSO.init(templateId);
  expect(semver.valid(template.version) != null).toBe(true);
  expect(template).toBeDefined();
  expect(template.currentTemplate.resources).toHaveLength(4);
  const templateFolder = await space.installTemplateById(user, templateId);
  const templateNode = templateFolder.toNodeSO();
  expect(templateNode.name).toMatchObject(template.currentTemplate.name);

  // 之前创建的节点应该被挤到第二位
  const expectFolderNode = await NodeSO.init(folderNodeSO.model.id);
  expect(expectFolderNode).toBeDefined();
  // 在模板节点下面
  expect(expectFolderNode?.model.preNodeId).toBe(templateNode.id);

  // check template apply record
  const templateApplyRecords = await space.getTemplateInstallation(templateId);
  expect(templateApplyRecords).toHaveLength(1);
  expect(templateApplyRecords[0].templateId).toBe(templateId);

  // check template nodes
  // const templateFolder = await templateFolder.toResourceSO<TemplateFolderSO>();
  const templateChildren = await templateFolder.getChildren();

  const nodeConverter = (node: NodeResource) => {
    if (node.resourceType === 'FOLDER') {
      return node as Folder;
    }
    if (node.resourceType === 'DATABASE') {
      return node as Database;
    }
    if (node.resourceType === 'AUTOMATION') {
      return node as Automation;
    }
    if (node.resourceType === 'DASHBOARD') {
      return node as Dashboard;
    }
    throw new Error(`Unknown node type: ${node.resourceType}`);
    // return undefined;
  };

  expect(templateChildren.length).toBe(template.currentTemplate.resources.length);

  // check template nodes count and sort
  for (let i = 0; i < templateChildren.length; i += 1) {
    const templateChild = templateChildren[i];
    const expectChild = template.currentTemplate.resources[i];
    expect(templateChild.type).toBe(expectChild.resourceType);
    const expectNode = nodeConverter(expectChild);
    expect(expectNode).toBeDefined();
    expect(templateChild.name).toMatchObject(expectNode!.name);
  }

  // 检查员工表（模板已注释该表，暂时搁置）
  // const employeeNode = template.bo.resources
  //   .find((n) => n.resourceType === 'DATABASE' && (n as Database).templateId === 'employee');
  // expect(employeeNode).toBeDefined();
  // const employeeDatabase = employeeNode! as Database;
  // expect(employeeDatabase).toBeDefined();
  // const employDatabaseNodeSO = await templateFolder.findChildNodeByTemplateId(employeeDatabase!.templateId!);
  // expect(!employDatabaseNodeSO).toBe(false);
  // const database = await employDatabaseNodeSO!.toResourceSO<DatabaseSO>();

  // // check views
  // const views = await database.getViews();
  // for (let i = 0; i < views.length; i += 1) {
  //   const view = views[i];
  //   if (employeeDatabase!.views) {
  //     const definedView = employeeDatabase!.views![i];
  //     expect(view.name).toBe(definedView.name);
  //   } else {
  //     expect(view.name).toBe(DefaultViewName);
  //   }
  // }
  // // check fields
  // const employeeFields = employeeDatabase!.fields!;
  // const statusField = employeeFields.find((f) => f.templateId === 'status');
  // expect(statusField!.type).toBe('SINGLE_SELECT');

  // 检查mission
  const missions = await space.getAllMissions();
  expect(missions).toHaveLength(4);

  // 再装一次, 第一次安装的模板文件夹被挤下去
  const duplicatedTemplateFolder = await space.installTemplateById(user, templateId);
  const reloadedTemplateFolder = await NodeSO.init(templateFolder.id);
  expect(reloadedTemplateFolder.preNodeId).toBe(duplicatedTemplateFolder.id);
});
