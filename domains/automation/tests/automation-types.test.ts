import { expect, test } from 'vitest';
import { z } from 'zod';
import { SendEmailActionInput, ActionSchema, NativeTriggerSchema, TriggerSchema } from '@bika/types/automation/bo';
import { ResourceCreateDTOSchema } from '@bika/types/node/dto';
import { iStringParse } from '@bika/types/system';

test('Test Automation Types Convert Test', async () => {
  const n = z.coerce.number().parse(1);
  expect(n).toBe(1);
  const newInput = SendEmailActionInput.safeParse({
    type: 'SMTP',
  });

  expect(newInput.success).toBe(true);
  const i2 = SendEmailActionInput.safeParse({
    type: 'SERVICE',
  });
  expect(i2.success).toBe(true);

  const i3 = SendEmailActionInput.safeParse({
    type: 'SMTP_INTEGRATION',
  });
  expect(i3.success).toBe(true);

  const i6 = ResourceCreateDTOSchema.safeParse({
    name: 'test',
    resourceType: 'DATABASE',
  });
  expect(i6.success).toBe(true);

  // const i4 = ResourceCreateDTOSchema.safeParse({
  //   name: 'test',
  //   resourceType: 'VIEW',
  // });
  // expect(i4.success).toBe(true);

  const i5 = ResourceCreateDTOSchema.safeParse({
    name: 'test',
    resourceType: 'FORM',
  });
  expect(i5.success).toBe(true);

  const wecom = ActionSchema.safeParse({
    actionType: 'WECOM_WEBHOOK',
  });
  expect(wecom.success).toBe(true);
  expect(wecom.data?.actionType).toBe('WECOM_WEBHOOK');

  const telegram = ActionSchema.safeParse({
    actionType: 'TELEGRAM_SEND_MESSAGE',
  });
  expect(telegram.success).toBe(true);
  expect(telegram.data?.actionType).toBe('TELEGRAM_SEND_MESSAGE');

  const tweet = ActionSchema.safeParse({
    actionType: 'X_CREATE_TWEET',
  });
  expect(tweet.success).toBe(true);
  expect(tweet.data?.actionType).toBe('X_CREATE_TWEET');

  const randomAction = ActionSchema.safeParse({
    actionType: 'ROUND_ROBIN',
  });
  expect(randomAction.success).toBe(true);
  expect(randomAction.data?.actionType).toBe('ROUND_ROBIN');

  const findRecords = ActionSchema.safeParse({
    actionType: 'FIND_RECORDS',
    input: {
      type: 'DATABASE',
      databaseId: '',
    },
  });
  expect(findRecords.success).toBe(true);
  expect(findRecords.data?.actionType).toBe('FIND_RECORDS');

  const findMissions = ActionSchema.safeParse({
    actionType: 'FIND_MISSIONS',
  });
  expect(findMissions.success).toBe(true);
  expect(findMissions.data?.actionType).toBe('FIND_MISSIONS');

  const datetimeFieldReached = NativeTriggerSchema.safeParse({
    triggerType: 'DATETIME_FIELD_REACHED',
    input: {
      type: 'DATETIME_FIELD_REACHED',
      databaseId: '',
      fieldId: '',
      datetime: {
        type: 'TODAY',
        hour: 9,
        minute: 0,
      },
    },
  });
  expect(datetimeFieldReached.success).toBe(true);
  expect(datetimeFieldReached.data?.triggerType).toBe('DATETIME_FIELD_REACHED');

  const anyTriggerTest = TriggerSchema.parse(datetimeFieldReached.data);
  expect(anyTriggerTest).toBeDefined();

  const fallbackStr = iStringParse({
    'zh-TW': 'tw fallback',
    ja: 'Japanese fallback',
  });
  expect(fallbackStr).toBe('tw fallback');
});
