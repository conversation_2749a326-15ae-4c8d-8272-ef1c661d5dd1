import { describe, expect, test } from 'vitest';
import { ServerError, errors } from '@bika/contents/config/server/error';
import { MockContext, waitForMatchToBeMet } from '@bika/domains/__tests__/mock';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { AITextFieldProperty, AITextIntegrationProperty, DatabaseAITextField } from '@bika/types/database/bo';
import { ViewSO } from '../../server';
import { FieldSO } from '../../server/fields/field-so';

describe('create record test', async () => {
  test('create or delete record should change node state', async () => {
    const { user, member, rootFolder } = await MockContext.initUserContext();
    const databaseNode = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'database',
      },
      { createDefaultRecords: true },
    );
    await waitForMatchToBeMet(
      async () => {
        const node = await NodeSO.init(databaseNode.id);
        const numberState = node.state.find((state) => state.state === 'NUMBER');
        return numberState !== undefined && numberState.number === 3;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
    const databaseSO = await databaseNode.toResourceSO<DatabaseSO>();
    const fieldSO = await databaseSO.createField(user, {
      type: 'SINGLE_TEXT',
      name: 'field',
    });
    await databaseSO.createRecords(user, member, [
      {
        [fieldSO.id]: 'test',
      },
    ]);
    const newRecords = await databaseSO.createRecords(user, member, [
      {
        [fieldSO.id]: 'test2',
      },
    ]);
    await waitForMatchToBeMet(
      async () => {
        const node = await NodeSO.init(databaseSO.id);
        const numberState = node.state.find((state) => state.state === 'NUMBER');
        return numberState !== undefined && numberState.number === 5;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
    await databaseSO.deleteRecords(
      user,
      newRecords.map((record) => record.id),
    );
    await waitForMatchToBeMet(
      async () => {
        const node = await NodeSO.init(databaseSO.id);
        const numberState = node.state.find((state) => state.state === 'NUMBER');
        return numberState !== undefined && numberState.number === 4;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
  });
});

describe('Database exists record by field and data', async () => {
  const { user, member, rootFolder } = await MockContext.initUserContext();
  const node = await rootFolder.createChildSimple(user, {
    name: 'database',
    resourceType: 'DATABASE',
    fields: [
      {
        name: 'text',
        type: 'SINGLE_TEXT',
      },
    ],
  });

  const db = await node.toResourceSO<DatabaseSO>();
  const field = db.getFields()[0];
  await db.createRecord(user, member, { text: 'test' });

  test('exists record by field and data', async () => {
    const exists = await db.existsRecordByFieldIdAndData(field.id, 'test');
    expect(exists).toBe(true);
  });

  test('not exists record by field and data', async () => {
    const exists = await db.existsRecordByFieldIdAndData(field.id, 'test2');
    expect(exists).toBe(false);
  });
});

describe('Database delete field', async () => {
  const { user, rootFolder, space } = await MockContext.initUserContext();
  const node = await rootFolder.createChildSimple(user, {
    resourceType: 'DATABASE',
    // databaseType: 'DATUM',
    name: 'database',
    fields: [
      {
        templateId: 'field1',
        name: 'field1',
        type: 'SINGLE_TEXT',
      },
      {
        templateId: 'field2',
        name: 'field2',
        type: 'SINGLE_TEXT',
      },
      {
        templateId: 'field3',
        name: 'field3',
        type: 'SINGLE_TEXT',
      },
    ],
  });

  let db = await node.toResourceSO<DatabaseSO>();
  const field1 = db.getFieldByFieldKey('field1');
  expect(field1.primary).toBeTruthy();
  const field2 = db.getFieldByFieldKey('field2');
  const field3 = db.getFieldByFieldKey('field3');

  const view = await db.createView(user, {
    name: 'table',
    type: 'TABLE',
    databaseId: db.id,
    filters: {
      conjunction: 'And',
      conditions: [],
      conds: [
        {
          fieldId: field1.id,
          fieldType: 'SINGLE_TEXT',
          clause: {
            operator: 'Is',
            value: 'test',
          },
        },
        {
          fieldId: field2.id,
          fieldType: 'SINGLE_TEXT',
          clause: {
            operator: 'Is',
            value: 'test',
          },
        },
        {
          fieldId: field3.id,
          fieldType: 'SINGLE_TEXT',
          clause: {
            operator: 'Is',
            value: 'test',
          },
        },
      ],
    },
  });
  await view.update(user.id, {
    fields: [{ id: field1.id }, { id: field2.id }, { id: field3.id }],
  });

  db = await DatabaseSO.init(db.id);

  test('should not delete primary field', async () => {
    await expect(async () => field1.delete(user)).rejects.toThrow(
      new ServerError(errors.database.field_primary_delete),
    );
  });

  test('should delete a field', async () => {
    let dbSO = await DatabaseSO.init(db.id);
    let viewSO = await ViewSO.init(view.id);
    let fields = viewSO.getFields();

    // 检查字段 2 是否存在 (db)
    const dbField = dbSO.findFieldByFieldKey(field2.id);
    expect(dbField).toBeDefined();

    // 检查字段 2 是否存在 (view)
    const viewField = fields.find((field) => field.id === field2.id);
    expect(viewField).toBeDefined();

    // TODO: 删除 filter 中的 field 引用

    // Do 删除字段 2
    await field2.delete(user);

    dbSO = await DatabaseSO.init(db.id);
    viewSO = await ViewSO.init(view.id);
    fields = viewSO.getFields();

    // 检查字段 2 是否被删除 (db)
    await expect(async () => dbSO.getFieldByFieldKey(field2.id)).rejects.toThrowError();

    // 检查字段 2 是否被删除 (view)
    expect(fields.find((field) => field.id === field2.id)).toBeUndefined();
  });

  test('should delete a field and update view', async () => {
    const getFieldFromView = async (dbSO: DatabaseSO, fieldId: string): Promise<FieldSO | undefined> => {
      const views = await dbSO.getViews();
      const v = views[0];
      return v.getFields().find((field) => field.id === fieldId);
    };

    let dbSO = await DatabaseSO.init(db.id);
    expect(dbSO.findFieldByFieldKey(field3.id)).toBeDefined();
    expect(await getFieldFromView(dbSO, field3.id)).toBeDefined();

    // 删除字段 3
    await field3.delete(user);

    dbSO = await DatabaseSO.init(db.id);
    await expect(async () => dbSO.getFieldByFieldKey(field3.id)).rejects.toThrowError();
    expect(await getFieldFromView(dbSO, field3.id)).toBeUndefined();
  });

  test('delete ai_text field', async () => {
    const dbSO = await DatabaseSO.init(db.id);
    const integration = await space.createIntegration(user.id, {
      name: 'integration',
      type: 'OPENAI',
      apiKey: 'apiKey',
    });
    const field = await dbSO.createField(user, {
      type: 'AI_TEXT',
      name: 'ai_text',
      property: {
        type: 'INTEGRATION',
        integrationId: integration.id,
        model: 'gpt-4o-mini',
        prompt: 'hi: ',
      },
    });
    await field.delete(user);
    const newDB = await DatabaseSO.init(db.id);
    const fields = newDB.getFields();
    expect(fields.find((f) => f.id === field.id)).toBeUndefined();
  });
});

describe('Database create field', async () => {
  test('create AI_TEXT field -- ai model not fill', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await DatabaseSO.init(node.id);
    // invalidate field id in promot
    await expect(async () =>
      database.createField(user, {
        type: 'AI_TEXT',
        name: 'ai_text',
      } as unknown as DatabaseAITextField),
    ).rejects.toThrow(new ServerError(errors.database.field_property_ai_model_can_not_empty));
  });

  test('create AI_TEXT field -- ai model integration not found', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await DatabaseSO.init(node.id);
    await expect(async () =>
      database.createField(user, {
        type: 'AI_TEXT',
        name: 'ai_text',
        property: {
          type: 'INTEGRATION',
          integrationId: 'integrationId',
        },
      }),
    ).rejects.toThrow(
      new ServerError(errors.database.field_property_ai_model_not_found, {
        integrationId: 'integrationId',
      }),
    );
  });

  test('create AI_TEXT field -- field not found in database', async () => {
    const { user, rootFolder, space } = await MockContext.initUserContext();
    const integration = await space.createIntegration(user.id, {
      name: 'integration',
      type: 'OPENAI',
      apiKey: 'apiKey',
    });
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await DatabaseSO.init(node.id);
    await expect(async () =>
      database.createField(user, {
        type: 'AI_TEXT',
        name: 'ai_text',
        property: {
          type: 'INTEGRATION',
          integrationId: integration.id,
          model: 'gpt-4o-mini',
          prompt: '<%= text %>',
        },
      } as unknown as DatabaseAITextField),
    ).rejects.toThrow(
      new ServerError(errors.database.field_not_found, { key: 'text', databaseName: database.getName() }),
    );
  });

  test('create AI_TEXT field -- success', async () => {
    const { user, rootFolder, space } = await MockContext.initUserContext();
    const integration = await space.createIntegration(user.id, {
      name: 'integration',
      type: 'OPENAI',
      apiKey: 'apiKey',
    });
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await DatabaseSO.init(node.id);
    const fields = database.getFields();
    const field = await database.createField(user, {
      type: 'AI_TEXT',
      name: 'ai_text',
      property: {
        type: 'INTEGRATION',
        integrationId: integration.id,
        model: 'gpt-4o-mini',
        prompt: `hi: <%= ${fields[0].id} %>aaa`,
      },
    });
    expect(field).toBeDefined();
  });
});

describe('AI_TEXT field update', async () => {
  const { user, member, rootFolder, space } = await MockContext.initUserContext();
  const integration = await space.createIntegration(user.id, {
    name: 'integration',
    type: 'OPENAI',
    apiKey: 'apiKey',
  });
  test('update AI_TEXT field -- invalid property', async () => {
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await DatabaseSO.init(node.id);
    const fields = database.getFields();
    const field = await database.createField(user, {
      type: 'AI_TEXT',
      name: 'ai_text',
      property: {
        type: 'INTEGRATION',
        integrationId: integration.id,
        model: 'gpt-4o-mini',
        prompt: `hi: <%= ${fields[0].id} %>aaa`,
      },
    });
    const bo = field.toBO();
    await expect(async () =>
      field.update(user, {
        ...bo,
        property: {},
      } as unknown as DatabaseAITextField),
    ).rejects.toThrow(new ServerError(errors.database.field_property_invalid));
  });

  test('update AI_TEXT field -- name changed', async () => {
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await DatabaseSO.init(node.id);
    const fields = database.getFields();
    const field = await database.createField(user, {
      type: 'AI_TEXT',
      name: 'ai_text',
      property: {
        type: 'INTEGRATION',
        integrationId: integration.id,
        model: 'gpt-4o-mini',
        prompt: `<%= ${fields[0].id} %>aaa`,
      },
    });
    const bo = field.toBO();
    const updatedField = await field.update(user, {
      ...bo,
      name: 'ai_text_new',
    });
    expect(updatedField.name).toBe('ai_text_new');
  });

  test('update AI_TEXT field -- description changed', async () => {
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await DatabaseSO.init(node.id);
    const fields = database.getFields();
    const field = await database.createField(user, {
      type: 'AI_TEXT',
      name: 'ai_text',
      property: {
        type: 'INTEGRATION',
        integrationId: integration.id,
        model: 'gpt-4o-mini',
        prompt: `<%= ${fields[0].id} %>aaa`,
      },
    });
    const bo = field.toBO();
    const updatedField = await field.update(user, {
      ...bo,
      description: 'description_new',
    });
    expect(updatedField.description).toBe('description_new');
  });

  test('update AI_TEXT field -- prompt changed', async () => {
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await DatabaseSO.init(node.id);
    const fields = database.getFields();
    const field = await database.createField(user, {
      type: 'AI_TEXT',
      name: 'ai_text',
      property: {
        type: 'INTEGRATION',
        integrationId: integration.id,
        model: 'gpt-4o-mini',
        prompt: `<%= ${fields[0].id} %>aaa`,
      },
    });
    const bo = field.toBO();
    const updatedField = await field.update(user, {
      ...bo,
      property: {
        type: 'INTEGRATION',
        integrationId: integration.id,
        model: 'gpt-4o-mini',
        prompt: `<%= ${fields[1].id} %>bbb`,
      },
    } as unknown as DatabaseAITextField);
    expect((updatedField.property as AITextFieldProperty).prompt).toBe(`<%= ${fields[1].id} %>bbb`);
  });

  test('update AI_TEXT field -- ai model changed', async () => {
    const integration2 = await space.createIntegration(user.id, {
      name: 'integration2',
      type: 'OPENAI',
      apiKey: 'apiKey2',
    });
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await DatabaseSO.init(node.id);
    const fields = database.getFields();
    const field = await database.createField(user, {
      type: 'AI_TEXT',
      name: 'ai_text',
      property: {
        type: 'INTEGRATION',
        integrationId: integration.id,
        model: 'gpt-4o-mini',
        prompt: `<%= ${fields[0].id} %>aaa`,
      },
    });
    const bo = field.toBO();
    const updatedField = await field.update(user, {
      ...bo,
      property: {
        type: 'INTEGRATION',
        integrationId: integration2.id,
        model: 'gpt-4o-mini',
        prompt: `<%= ${fields[1].id} %>bbb`,
      },
    } as unknown as DatabaseAITextField);
    expect((updatedField.property as AITextIntegrationProperty).integrationId).toBe(integration2.id);
    expect((updatedField.property as AITextFieldProperty).prompt).toBe(`<%= ${fields[1].id} %>bbb`);
  });

  test('SINGLE_TEXT convert to AI_TEXT field', async () => {
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await DatabaseSO.init(node.id);
    const fields = database.getFields();
    const field = await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: 'single_text',
    });
    const record = await database.createRecord(user, member, {
      [field.id]: 'test',
    });
    // invalidate field property when change to AI_TEXT field
    await expect(async () =>
      field.update(user, {
        ...field.toBO(),
        type: 'AI_TEXT',
      } as unknown as DatabaseAITextField),
    ).rejects.toThrow(new ServerError(errors.database.field_property_invalid));

    const updatedField = await field.update(user, {
      ...field.toBO(),
      type: 'AI_TEXT',
      property: {
        type: 'INTEGRATION',
        integrationId: integration.id,
        model: 'gpt-4o-mini',
        prompt: `<%= ${fields[0].id} %>aaa`,
      },
    });
    expect(updatedField.toBO()).toStrictEqual({
      ...field.toBO(),
      type: 'AI_TEXT',
      property: {
        type: 'INTEGRATION',
        integrationId: integration.id,
        model: 'gpt-4o-mini',
        prompt: `<%= ${fields[0].id} %>aaa`,
      },
    });
    const recordNew = await database.getRecord(record.id);
    expect(recordNew.data[updatedField.id]).toBe('test');
    expect(recordNew.model.values[updatedField.id]).toBe('test');
  });

  test('SINGLE_SELECT convert to AI_TEXT field', async () => {
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await DatabaseSO.init(node.id);
    const fields = database.getFields();
    const field = await database.createField(user, {
      type: 'SINGLE_SELECT',
      name: 'single_select',
      property: {
        options: [{ name: 'test', id: 'testoption' }],
      },
    });
    const record = await database.createRecord(user, member, {
      [field.id]: ['testoption'],
    });
    expect(record.data[field.id]).toStrictEqual(['testoption']);
    expect(record.model.values[field.id]).toStrictEqual(['test']);

    const updatedField = await field.update(user, {
      ...field.toBO(),
      type: 'AI_TEXT',
      property: {
        type: 'INTEGRATION',
        integrationId: integration.id,
        model: 'gpt-4o-mini',
        prompt: `<%= ${fields[0].id} %>aaa`,
      },
    });
    expect(updatedField.toBO()).toStrictEqual({
      ...field.toBO(),
      type: 'AI_TEXT',
      property: {
        type: 'INTEGRATION',
        integrationId: integration.id,
        model: 'gpt-4o-mini',
        prompt: `<%= ${fields[0].id} %>aaa`,
      },
    });
    const recordNew = await database.getRecord(record.id);
    expect(recordNew.data[updatedField.id]).toBe('test');
    expect(recordNew.model.values[updatedField.id]).toBe('test');
  });
});
