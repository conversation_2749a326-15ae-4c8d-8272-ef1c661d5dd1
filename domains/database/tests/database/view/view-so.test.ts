import assert from 'assert';
import { describe, expect, test } from 'vitest';
import { MockContext, waitForMatchToBeMet } from '@bika/domains/__tests__/mock';
import { NodeSO } from '@bika/domains/node/server';
import { ViewField, ViewFilter, ViewGroupArray, ViewSortArray } from '@bika/types/database/bo';
import { DatabaseSO } from '../../../server/database-so';
import { ViewSO } from '../../../server/views/view-so';

describe('View Test - update crud', () => {
  test('test original field', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await node.toResourceSO<DatabaseSO>();
    await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: '1',
    });
    await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: '2',
    });
    const view = await database.firstView();
    const viewVO = view.toVO();
    expect(viewVO.columns[3].name).toBe('1');
    expect(viewVO.columns[4].name).toBe('2');
  });

  test('test view set hidden field ', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await node.toResourceSO<DatabaseSO>();
    const field1 = await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: '1',
    });
    const field2 = await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: '2',
    });
    const view = await database.firstView();
    await view.update(user.id, {
      fields: [
        {
          id: field1.id,
        },
        {
          id: field2.id,
          hidden: true,
        },
      ],
    });
    const updatedView = await ViewSO.init(view.id);
    const viewVO = updatedView.toVO();
    // 第一个字段是主键
    expect(viewVO.columns[1].name).toBe('1');
    expect(viewVO.columns[1].hidden).toBe(undefined);
    expect(viewVO.columns[2].name).toBe('2');
    expect(viewVO.columns[2].hidden).toBe(true);
  });

  test('test view set width of field', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const database = await node.toResourceSO<DatabaseSO>();
    const field1 = await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: '1',
    });
    const field2 = await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: '2',
    });

    const view = await database.firstView();
    await view.update(user.id, {
      fields: [
        {
          id: field1.id,
        },
        {
          id: field2.id,
          width: 100,
        },
      ],
    });
    const updatedView = await ViewSO.init(view.id);
    const viewVO = updatedView.toVO();

    const column2 = viewVO.columns.find((column) => column.name === '2');

    expect(viewVO.columns[0].width).toBe(undefined);
    expect(column2?.width).toBe(100);
  });

  test('should append fields in back if not in the view', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    let database = await node.toResourceSO<DatabaseSO>();

    const field1 = await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: 'A',
    });
    const field2 = await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: 'B',
    });

    const view = await database.firstView();
    await view.update(user.id, {
      fields: [
        {
          id: field1.id,
        },
        {
          id: field2.id,
        },
      ],
    });

    // 添加一个新字段, view 应该包含这个字段, 因为只有一个视图
    database = await DatabaseSO.init(database.id);
    await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: 'C',
    });

    const db = await DatabaseSO.init(database.id);
    const viewVO = (await db.firstView()).toVO();
    expect(viewVO.columns.length).toBe(6);
    expect(viewVO.columns[1].name).toBe('A');
    expect(viewVO.columns[1].hidden).toBeFalsy();
    expect(viewVO.columns[2].name).toBe('B');
    expect(viewVO.columns[2].hidden).toBeFalsy();
    expect(viewVO.columns[3].name).toBe('C');
    expect(viewVO.columns[3].hidden).toBeFalsy();
  });

  test('update view sort -- move to the first', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const databaseId = await rootFolder.createChildren(user, [
      {
        resourceType: 'DATABASE',
        name: 'database',
        views: [
          {
            type: 'TABLE',
            name: 'A',
          },
          {
            type: 'TABLE',
            name: 'B',
          },
          {
            type: 'TABLE',
            name: 'C',
          },
          {
            type: 'TABLE',
            name: 'D',
          },
        ],
      },
    ]);
    const database = await DatabaseSO.init(databaseId);
    const oldViews = await database.getViews();
    // move the last view to the first
    const view = await database.lastView();
    assert(view, 'last view should be defined');
    await view.update(user.id, {
      preViewId: null,
    });
    await database.reloadViews();
    const newViews = await database.getViews();
    const firstView = await database.firstView();
    expect(firstView.id).toBe(view.id);
    expect(firstView.preViewId).toBe(null);
    expect(newViews[1].preViewId).toBe(firstView.id);
    // 将倒数第二个移动到了最后一个
    expect(newViews[3].id).toBe(oldViews[2].id);
  });

  test('update view sort -- move on the middle', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const databaseId = await rootFolder.createChildren(user, [
      {
        resourceType: 'DATABASE',
        name: 'database',
        views: [
          {
            type: 'TABLE',
            name: 'A',
          },
          {
            type: 'TABLE',
            name: 'B',
          },
          {
            type: 'TABLE',
            name: 'C',
          },
          {
            type: 'TABLE',
            name: 'D',
          },
        ],
      },
    ]);
    const database = await DatabaseSO.init(databaseId);
    const oldViews = await database.getViews();
    // move the third to the second
    const view = oldViews[2];
    assert(view, 'last view should be defined');
    await view.update(user.id, {
      preViewId: oldViews[0].id,
    });
    await database.reloadViews();
    const newViews = await database.getViews();
    const firstView = await database.firstView();
    expect(firstView.id).toBe(oldViews[0].id);
    expect(newViews[1].id).toBe(view.id);
    expect(newViews[1].preViewId).toBe(oldViews[0].id);
    // 调换了位置
    expect(newViews[2].id).toBe(oldViews[1].id);
    expect(newViews[2].preViewId).toBe(view.id);
    // 最后一个的位置的前置ID
    expect(newViews[3].preViewId).toBe(oldViews[1].id);
  });

  test('update view sort -- move to the last', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const databaseId = await rootFolder.createChildren(user, [
      {
        resourceType: 'DATABASE',
        name: 'database',
        views: [
          {
            type: 'TABLE',
            name: 'A',
          },
          {
            type: 'TABLE',
            name: 'B',
          },
          {
            type: 'TABLE',
            name: 'C',
          },
          {
            type: 'TABLE',
            name: 'D',
          },
        ],
      },
    ]);
    const database = await DatabaseSO.init(databaseId);
    const oldViews = await database.getViews();
    // move the first to the last
    const view = oldViews[0];
    assert(view, 'last view should be defined');
    await view.update(user.id, {
      preViewId: oldViews[3].id,
    });
    await database.reloadViews();
    const newViews = await database.getViews();
    expect(newViews[0].id).toBe(oldViews[1].id);
    expect(newViews[1].preViewId).toBe(oldViews[1].id);
    // 调换了位置
    expect(newViews[3].id).toBe(oldViews[0].id);
    expect(newViews[3].preViewId).toBe(oldViews[3].id);
  });
});

describe('View Test - to vo', () => {
  test('view hidden columns', async () => {
    const { user, space } = await MockContext.initUserContext();
    const template = await space.installTemplateById(user, 'ai-automated-ticket-system');
    await waitForMatchToBeMet(
      async () => {
        const node = await NodeSO.init(template.id);
        console.log('Node state:', node.state);
        const numberState = node.state.find((state) => state.state === 'NUMBER');
        return numberState !== undefined && numberState.number === 5;
      },
      20000,
      200,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
    const children = await template.getChildren();
    const database = await children.find((i) => i.type === 'DATABASE')!.toResourceSO<DatabaseSO>();
    const view = await database.getViewByViewKey({ viewTemplateId: 'viwHexOqsrHFlvQf3FQQhK2U' });
    const viewVO = view!.toVO();
    expect(viewVO.columns[8].hidden).toBe(true);
  });
});

describe('View Test - link field', () => {
  test('create fields in special view', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();

    const databaseNode = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });

    let database = await databaseNode.toResourceSO<DatabaseSO>();

    // 创建一个特殊的视图
    let specialView = await database.createView(user, {
      name: 'view',
      type: 'TABLE',
      databaseId: database.id,
    });

    // 给数据库添加字段(Text), 默认全部视图都有
    const _fieldShowInAllViews = await database.createField(user, {
      type: 'SINGLE_TEXT',
      name: 'A',
    });

    // 检查两个视图的字段
    database = await DatabaseSO.init(database.id);
    const defaultView = await database.firstView();
    // 默认全字段
    expect(defaultView.fields.length).toBe(4);
    expect(defaultView.getFields().length).toBe(4);
    specialView = await database.getView(specialView.id);
    // 默认全字段
    expect(specialView.fields.length).toBe(4);
    expect(specialView.getFields().length).toBe(4);

    // 添加字段(Number)到特殊视图
    const _numberField = await database.createField(
      user,
      {
        type: 'NUMBER',
        name: 'B',
        property: {},
      },
      { viewId: specialView.id },
    );

    // 特殊视图应该有5个字段
    specialView = await database.getView(specialView.id);
    expect(specialView.fields).toHaveLength(5);
    expect(specialView.getFields().length).toBe(5);
    // 默认视图只有4个字段
    expect(defaultView.fields.length).toBe(4);
    expect(defaultView.getFields().length).toBe(5);
    specialView = await database.getView(specialView.id);
  });
});

describe('View Test - unlink field', async () => {
  const { user, rootFolder } = await MockContext.initUserContext();
  const node = await rootFolder.createChildSimple(user, {
    resourceType: 'DATABASE',
    // databaseType: 'DATUM',
    name: 'database',
    fields: [
      {
        templateId: 'field1',
        name: 'field1',
        type: 'SINGLE_TEXT',
      },
      {
        templateId: 'field2',
        name: 'field2',
        type: 'SINGLE_TEXT',
      },
    ],
  });

  const db = await node.toResourceSO<DatabaseSO>();
  const field1 = db.getFieldByFieldKey('field1');
  const field2 = db.getFieldByFieldKey('field2');

  const testView = await db.createView(user, {
    name: 'view',
    type: 'TABLE',
    databaseId: db.id,
    fields: [
      {
        id: field1.id,
      },
      {
        id: field2.id,
      },
    ],
    filters: {
      conjunction: 'And',
      conditions: [],
      conds: [
        {
          fieldType: 'SINGLE_TEXT',
          fieldId: field1.id,
          clause: {
            operator: 'Is',
            value: 'test',
          },
        },
        {
          fieldType: 'SINGLE_TEXT',
          fieldId: field2.id,
          clause: {
            operator: 'Is',
            value: 'test',
          },
        },
      ],
    },
    sorts: [
      {
        fieldId: field1.id,
        asc: true,
      },
      {
        fieldId: field2.id,
        asc: false,
      },
    ],
    groups: [
      {
        fieldId: field1.id,
        asc: true,
      },
      {
        fieldId: field2.id,
        asc: false,
      },
    ],
  });

  test('unlink field', async () => {
    let view = await db.getView(testView.id);

    let fields = view.model.fields as ViewField[];
    expect(fields.length).toBe(2);

    let filters = view.model.filters as ViewFilter;
    expect(filters!.conds?.length).toBe(2);

    let sorts = view.model.sorts as ViewSortArray;
    expect(sorts?.length).toBe(2);

    let groups = view.model.groups as ViewGroupArray;
    expect(groups?.length).toBe(2);

    // Unlink field1
    await view.unlinkFieldOperation(user.id, field1.id);

    view = await ViewSO.init(view.id);
    fields = view.model.fields as ViewField[];
    expect(fields.length).toBe(1);
    expect(fields[0].id).toBe(field2.id);

    filters = view.model.filters as ViewFilter;
    expect(filters!.conds?.length).toBe(1);
    expect(filters!.conds?.[0].fieldId).toBe(field2.id);

    sorts = view.model.sorts as ViewSortArray;
    expect(sorts?.length).toBe(1);
    expect(sorts?.[0].fieldId).toBe(field2.id);

    groups = view.model.groups as ViewGroupArray;
    expect(groups?.length).toBe(1);
    expect(groups?.[0].fieldId).toBe(field2.id);
  });
});
