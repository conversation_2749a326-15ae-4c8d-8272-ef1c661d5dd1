import { describe, expect, it } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { DefaultPercentFieldProperty, RecordData } from '@bika/types/database/bo';
import { DatabaseSO } from '../server/database-so';

describe('Database Search', async () => {
  const { user, member, rootFolder } = await MockContext.initUserContext();

  await user.updateUserInfo({ name: 'Test User' });

  const node = await rootFolder.createChildSimple(user, {
    resourceType: 'DATABASE',
    name: 'Full Field Database',
    templateId: 'databaseId',
    // databaseType: 'DATUM',
    fields: [
      // String
      {
        templateId: 'single_text',
        name: 'Single Text',
        type: 'SINGLE_TEXT',
      },
      {
        templateId: 'long_text',
        name: 'Long Text',
        type: 'LONG_TEXT',
      },
      {
        templateId: 'email',
        name: '<PERSON><PERSON>',
        type: 'EMAIL',
      },
      {
        templateId: 'phone',
        name: 'Phone',
        type: 'PHONE',
      },
      {
        templateId: 'url',
        name: 'URL',
        type: 'URL',
      },

      // Number
      {
        templateId: 'number',
        name: 'Number',
        type: 'NUMBER',
        property: {
          precision: 2,
        },
      },
      {
        templateId: 'percent',
        name: 'Percent',
        type: 'PERCENT',
        property: DefaultPercentFieldProperty,
      },
      {
        templateId: 'currency',
        name: 'Currency',
        type: 'CURRENCY',
        property: {
          precision: 2,
        },
      },
      {
        templateId: 'rating',
        name: 'Rating',
        type: 'RATING',
        property: {
          icon: {
            type: 'COLOR',
            color: 'red',
          },
          max: 5,
        },
      },
      {
        templateId: 'checkbox',
        name: 'Checkbox',
        type: 'CHECKBOX',
      },

      // Select
      {
        templateId: 'single_select',
        name: 'Single Select',
        type: 'SINGLE_SELECT',
        property: {
          options: [
            {
              templateId: '1',
              name: 'Option One',
              color: 'red',
            },
            {
              templateId: '2',
              name: 'Option Two',
              color: 'blue',
            },
          ],
        },
      },
      {
        templateId: 'multi_select',
        name: 'Multi Select',
        type: 'MULTI_SELECT',
        property: {
          options: [
            {
              templateId: '1',
              name: 'Option One',
              color: 'red',
            },
            {
              templateId: '2',
              name: 'Option Two',
              color: 'blue',
            },
          ],
        },
      },

      // Date
      {
        templateId: 'datetime',
        name: 'Datetime',
        type: 'DATETIME',
        property: {
          dateFormat: 'YYYY-MM-DD',
          includeTime: true,
        },
      },

      // Member
      {
        templateId: 'member',
        name: 'Member',
        type: 'MEMBER',
        property: {},
      },

      // Formula
      {
        templateId: 'formula',
        name: 'Formula',
        type: 'FORMULA',
        property: {
          expressionTemplate: 'REPLACE( {single_text}, 0, 7, "FORMULA" )',
        },
      },

      // More
      // TODO: ATTACHMENT, LINK, and LOOKUP
    ],
  });

  const db = await node.toResourceSO<DatabaseSO>();
  const view = await db.firstView();

  const records: RecordData[] = [
    {
      single_text: 'Single Text',
      long_text: 'Long Text',
      email: '<EMAIL>',
      phone: '99999999999',
      url: 'https://www.example.com',
      number: 22,
      percent: 33,
      currency: 44,
      rating: 5,
      checkbox: true,
      single_select: ['Option One'],
      multi_select: ['Option Two'],
      datetime: '2009-09-09T09:09:09.000Z',
      member: [member.id],
    },
    {
      single_text: 'Single Text',
    },
    {
      long_text: 'Long Text',
    },
    {
      email: '<EMAIL>',
    },
    {
      phone: '99999999999',
    },
    {
      url: 'https://www.example.com',
    },
    {
      number: 22,
    },
    {
      percent: 33,
    },
    {
      currency: 44,
    },
    {
      rating: 5,
    },
    {
      checkbox: true,
    },
    {
      single_select: ['Option One'],
    },
    {
      multi_select: ['Option Two'],
    },
    {
      datetime: '2009-09-09T00:00:00.000Z',
    },
    {
      member: [member.id],
    },
  ];

  for (const record of records) {
    await db.createRecord(user, member, record);
  }
  expect((await view.getRecords()).length).toBe(15);

  it('should return 2 records (Single Text)', async () => {
    view.setKeyword('Single Text');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Long Text)', async () => {
    view.setKeyword('Long Text');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Email)', async () => {
    view.setKeyword('<EMAIL>');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Phone)', async () => {
    view.setKeyword('99999999999');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (URL)', async () => {
    view.setKeyword('https://www.example.com');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Number)', async () => {
    view.setKeyword('22');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Percent)', async () => {
    view.setKeyword('33.00%');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Currency)', async () => {
    view.setKeyword('44');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Rating)', async () => {
    view.setKeyword('5');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Checkbox)', async () => {
    view.setKeyword('1');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Single Select)', async () => {
    view.setKeyword('Option One');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Multi Select)', async () => {
    view.setKeyword('Option Two');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Datetime)', async () => {
    view.setKeyword('2009-09-09');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Member)', async () => {
    view.setKeyword('Test User');
    expect((await view.getRecords()).length).toBe(2);
  });

  it('should return 2 records (Formula)', async () => {
    view.setKeyword('FORMULA Text');
    expect((await view.getRecords()).length).toBe(2);
  });
});
