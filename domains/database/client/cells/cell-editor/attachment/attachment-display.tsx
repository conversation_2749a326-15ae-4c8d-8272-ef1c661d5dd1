import React from 'react';
import { FileType } from './enum';
import { AttachmentVOExtends } from './interface';
import { isWhatFileType, renderFileIconUrl } from './utils/file-type';

export const AttachmentDisplay: React.FC<AttachmentVOExtends> = ({ name, mimeType, originFile, links }) => {
  const fileType = isWhatFileType({ name, type: mimeType });

  switch (fileType) {
    case FileType.Image:
      return (
        // eslint-disable-next-line @next/next/no-img-element, jsx-a11y/alt-text
        <img
          src={links?.thumbnailUrl || (originFile ? URL.createObjectURL(originFile) : '')}
          loading="lazy"
          className={'w-[24px] h-[24px] object-contain flex-shrink-0'}
        />
      );
    default:
      return (
        // eslint-disable-next-line @next/next/no-img-element
        <img
          loading="lazy"
          src={renderFileIconUrl({ name, type: mimeType })}
          alt={name}
          className={'w-[24px] h-[24px] object-contain flex-shrink-0'}
        />
      );
  }
};
