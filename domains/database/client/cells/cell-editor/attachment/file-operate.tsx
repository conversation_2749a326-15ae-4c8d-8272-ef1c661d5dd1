import React from 'react';
import { match } from 'ts-pattern';
import { useLocale } from '@bika/contents/i18n/context';
import { IconButton } from '@bika/ui/button';
import DeleteOutlined from '@bika/ui/icons/components/delete_outlined';
import DownloadOutlined from '@bika/ui/icons/components/download_outlined';
import ReloadOutlined from '@bika/ui/icons/components/reload_outlined';
import { LinearProgress } from '@bika/ui/progress';
import { Tooltip } from '@bika/ui/tooltip';
import { UploadStatus } from './interface';

interface IPendingUpload {
  status: UploadStatus.PENDING;
  progress: number;
}

interface ISuccessUpload {
  status: UploadStatus.SUCCESS;
}

interface IFailUpload {
  status: UploadStatus.FAIL;
  progress: number;
}

interface IUploading {
  status: UploadStatus.UPLOADING;
  progress: number;
}

interface IFileOperateProps {
  uploadStatus: ISuccessUpload | IFailUpload | IUploading | IPendingUpload;
  handleDeleteItem?: () => void;
  handleRetryUpload?: () => void;
  downloadAttachment?: () => void;
  disabled?: boolean;
}

export const FileOperate: React.FC<IFileOperateProps> = ({
  uploadStatus,
  handleDeleteItem,
  disabled,
  handleRetryUpload,
  downloadAttachment,
}) => {
  const { t } = useLocale();

  return (
    <div className={'flex'}>
      {(uploadStatus?.status === UploadStatus.FAIL ||
        uploadStatus?.status === UploadStatus.UPLOADING ||
        uploadStatus?.status === UploadStatus.PENDING) && (
        <div className={'mr-5 space-x-4 flex items-center'}>
          <LinearProgress
            determinate
            value={uploadStatus.progress}
            sx={{ width: 80 }}
            color={uploadStatus.status === UploadStatus.FAIL ? 'danger' : 'primary'}
          />
          {match(uploadStatus.status)
            .with(UploadStatus.UPLOADING, () => (
              <div className={'text-b4'}>{Math.min(Math.ceil(uploadStatus.progress), 99)}%</div>
            ))
            .with(UploadStatus.FAIL, () => <div className={'text-b4 text-[--textDangerDefault]'}>Failed</div>)
            .otherwise(() => null)}
        </div>
      )}

      {(uploadStatus.status === UploadStatus.SUCCESS || uploadStatus.status === UploadStatus.FAIL) && (
        <div className={'space-x-2 flex items-center opacity-0 cursor-pointer'} data-type="buttonGroup">
          {uploadStatus.status === UploadStatus.SUCCESS ? (
            <Tooltip title={t.global.download}>
              <span
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  downloadAttachment?.();
                }}
              >
                <IconButton color="neutral">
                  <DownloadOutlined />
                </IconButton>
              </span>
            </Tooltip>
          ) : (
            !disabled && (
              <Tooltip title={t.global.retry}>
                <span
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation();
                    handleRetryUpload?.();
                  }}
                >
                  <IconButton color="neutral">
                    <ReloadOutlined />
                  </IconButton>
                </span>
              </Tooltip>
            )
          )}
          {!disabled && (
            <Tooltip title={t.delete.delete}>
              <span
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  handleDeleteItem?.();
                }}
              >
                <IconButton color="neutral" variant="plain">
                  <DeleteOutlined />
                </IconButton>
              </span>
            </Tooltip>
          )}
        </div>
      )}
    </div>
  );
};
