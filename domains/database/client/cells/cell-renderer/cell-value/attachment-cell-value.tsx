import type React from 'react';
import { AttachmentCellVO } from '@bika/types/database/vo';
import { Tooltip } from '@bika/ui/tooltip-components';
import { FileType } from '../../cell-editor/attachment/enum';
import { isWhatFileType, renderFileIconUrl } from '../../cell-editor/attachment/utils/file-type';

interface AttachmentCellRenderProps {
  value: AttachmentCellVO[];
}

// TODO 移除 coldef 的依赖, 从 value 中获取
export const AttachmentCellValue: React.FC<AttachmentCellRenderProps> = ({ value: _values }) => (
  <div className={'flex space-x-1 items-center h-full'}>
    {Array.isArray(_values) &&
      _values.map((v, index) => {
        const attach = (_values as AttachmentCellVO[])[index];
        const fileType = isWhatFileType({ name: attach.name, type: attach.mimeType });

        return (
          <div key={index} className={'relative h-[22px] w-[22px] flex-shrink-0'}>
            <Tooltip title={attach.name} variant="solid" arrow color="neutral" placement="top">
              <img
                alt=""
                loading="lazy"
                // fill={true}
                src={
                  fileType === FileType.Image
                    ? attach.thumbnailUrl!
                    : renderFileIconUrl({ name: attach.name, type: attach.mimeType })
                }
                // objectFit="cover"
                className={'border-[1px] border-[--border-default] h-[22px] w-[22px]'}
              />
            </Tooltip>
          </div>
        );
      })}
  </div>
);
