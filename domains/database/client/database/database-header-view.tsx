import classNames from 'classnames';
import type React from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { DatabaseHeaderInfo } from '@bika/domains/node/client/header/node-header-info';
import { VisibilityAdapter } from '@bika/domains/shared/client/components/visibility-adapter';
import type { ViewType } from '@bika/types/database/bo';
import type { NodeDetailVO } from '@bika/types/node/vo';
import type { AccessPrivilege } from '@bika/types/permission/bo';
import { useSpaceContextForce, useSpaceRouter } from '@bika/types/space/context';
import type { iString } from '@bika/types/system';
import { BMenu } from '@bika/ui/bmenu';
import DeleteOutlined from '@bika/ui/icons/components/delete_outlined';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { Modal } from '@bika/ui/modal';
import { ResourceIconMap, ViewIconMap } from '@bika/ui/node/types-form/resource-icons-map';
import { EllipsisText } from '@bika/ui/text';
import { Typography } from '@bika/ui/texts';
import { DatabaseViewTopRightButtons } from './database-header-top-right-buttons';

interface IProps {
  name: string;
  className?: string;
  active: boolean;
  onClick?: () => void;
  viewId: string;
  databaseId: string;
  isView?: boolean;
  viewType?: ViewType;
  onClickTab?: (viewId: string) => void;
  isTemplatePreview?: boolean;
  disabled?: boolean;
}

export const TabItem: React.FC<IProps> = ({
  active,
  name,
  onClick,
  viewId,
  databaseId,
  className,
  viewType,
  disabled,
}) => {
  const spaceContext = useSpaceContextForce();
  const { trpc } = useApiCaller();
  const { t, i } = useLocale();
  const router = useSpaceRouter();

  const Icon = databaseId === 'view' ? ResourceIconMap.MIRROR : ViewIconMap[viewType || 'TABLE'];

  return (
    <Stack
      direction={'row'}
      alignItems={'center'}
      className={classNames(className, active ? 'cursor-pointer view-tag-active' : 'cursor-pointer')}
      sx={{
        ':hover': {
          backgroundColor: disabled ? 'inherit' : 'var(--bg-page)',
        },
        height: '39px',
        cursor: active ? 'default' : 'cursor',
        backgroundColor: viewType !== 'KANBAN' && active ? 'var(--bg-page)' : 'none',
        border: active ? `1px solid ${'var(--border-default)'}` : 'none',
        // 高亮的 tab 底部用背景色覆盖贯穿的 border，非高亮的 tab 没有底部边框
        borderBottomWidth: active ? '1px' : '0',
        // eslint-disable-next-line no-nested-ternary
        borderBottomColor: active ? (viewType === 'KANBAN' ? 'var(--bg-surface)' : 'var(--bg-page)') : 'transparent',
        borderBottomStyle: active ? 'solid' : 'none',
        maxWidth: '210px',
        borderLeftTopRadius: '8px',
        borderRightTopRadius: '8px',
        paddingLeft: '8px',
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        paddingRight: '8px',
        position: 'relative',
        zIndex: active ? 3 : 0, // 只有高亮的 tab 在贯穿 border 之上，非高亮的 tab 在 border 之下
      }}
    >
      <Icon
        onClick={onClick}
        size={16}
        className={'flex-none'}
        color={active ? 'var(--brand)' : 'var(--text-secondary)'}
      />
      <EllipsisText tooltip={name}>
        <Typography
          onClick={onClick}
          level={'b3'}
          sx={{
            marginLeft: '4px',
            marginRight: '4px',
            fontWeight: 'normal',
            color: active ? 'var(--brand)' : 'var(--text-primary)',
          }}
        >
          {i(name)}
        </Typography>
      </EllipsisText>
      {active && !disabled && (
        <VisibilityAdapter privilege="FULL_ACCESS">
          <BMenu
            buttonSlotProps={{
              sx: {
                '& > svg': {
                  fill: active ? 'var(--brand)' : 'var(--text-primary)',
                },
              },
            }}
            items={[
              [
                {
                  label: t.action.edit,
                  icon: <EditOutlined currentColor />,
                  onClick: (e) => {
                    spaceContext.showUIDrawer({
                      type: 'resource-editor',
                      props: {
                        screenType: 'DATABASE_VIEW',
                        viewId,
                        databaseId,
                      },
                    });
                    e.stopPropagation();
                  },
                },
                {
                  label: t.action.delete,
                  icon: <DeleteOutlined currentColor />,
                  onClick: () => {
                    Modal.show({
                      type: 'error',
                      title: t.resource.delete_view,
                      content: t('resource.delete_view_description', { name }),
                      okText: t.action.ok,
                      cancelText: t.action.cancel,
                      onOk: async () => {
                        await trpc.database.deleteView.mutate({
                          databaseId,
                          viewId,
                        });

                        router.push(`/space/${spaceContext.data?.id}/node/${databaseId}`);

                        spaceContext.refetch();
                      },
                    });
                  },
                },
              ],
            ]}
          />
        </VisibilityAdapter>
      )}
    </Stack>
  );
};

export const DatabaseHeaderComponent: React.FC<{
  name: string;
  databaseId: string;
  description?: iString;
  isView?: boolean;
  children?: React.ReactNode;
  permission?: AccessPrivilege;
  nodeDetail?: NodeDetailVO;
}> = ({ children, name, databaseId, description, permission, nodeDetail }) => {
  const spaceContext = useSpaceContextForce();

  const handleEditClick = () => {
    spaceContext.showUIDrawer({
      type: 'resource-editor',
      props: {
        screenType: 'NODE_RESOURCE',
        resourceType: 'DATABASE',
        nodeId: databaseId,
      },
    });
  };

  return (
    <Box
      sx={{
        width: '100%',
        height: '47px',
        padding: '0 16px',
        display: 'flex',
        overflowX: 'hidden',
        backgroundColor: 'var(--bg-surface)',
        transform: 'translateY(1px)',
        // 在整个同行区域底部添加贯穿的 border
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: '1px',
          backgroundColor: 'var(--border-default)',
          zIndex: 1,
        },
      }}
    >
      <DatabaseHeaderInfo
        name={name}
        databaseId={databaseId}
        description={description}
        permission={permission}
        onEdit={handleEditClick}
        icon={{ kind: 'node-resource', customIcon: nodeDetail?.icon || undefined, nodeType: 'DATABASE' }}
      />

      <Stack
        direction={'row'}
        display={'flex'}
        flex={'1 1 auto'}
        marginLeft="40px"
        width={'100%'}
        sx={{
          zIndex: 2,
          // transform: 'translateY(1px)',
          overflowX: 'auto',
          overflowY: 'visible',
          position: 'relative',
        }}
      >
        <Stack
          alignItems={'center'}
          className={'middle'}
          sx={{
            width: '100%',
            height: '47px',
            overflowX: 'hidden',
            overflowY: 'hidden',
            flex: '1 1 auto',
            // transform: 'translateY(1px)',
          }}
          direction={'row'}
          id="database-view-tabs-container"
        >
          <Stack
            style={{
              flexDirection: 'row',
              overflowX: 'hidden',
              marginTop: '8px',
              position: 'relative',
              zIndex: 2, // 确保 tabs 在 border 之上
              height: '40px', // 确保 tabs 容器有固定高度
            }}
            id="database-view-tabs"
          >
            {children}
          </Stack>
        </Stack>

        <Box alignItems={'center'} justifyContent={'center'} display={'flex'} className={'right'} flex={'0 none'}>
          {/* 这里开始是右边的按钮区域 */}
          {nodeDetail && !!permission && (
            <DatabaseViewTopRightButtons databaseId={databaseId} name={name} nodeDetail={nodeDetail} />
          )}
        </Box>
      </Stack>
      {/* </Stack> */}
    </Box>
  );
};
