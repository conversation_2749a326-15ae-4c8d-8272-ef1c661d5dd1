import assert from 'assert';
import { useUpdateEffect } from 'ahooks';
import { throttle } from 'lodash';
import React from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import { VisibilityAdapter } from '@bika/domains/shared/client/components/visibility-adapter';
import { useDatabaseVOContext } from '@bika/types/database/context';
import type { ViewVO } from '@bika/types/database/vo';
import type { NodeDetailVO } from '@bika/types/node/vo';
import { useSpaceContextForce, useSpaceRouter } from '@bika/types/space/context';
import { But<PERSON> } from '@bika/ui/button';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import ChevronDownOutlined from '@bika/ui/icons/components/chevron_down_outlined';
import ChevronUpOutlined from '@bika/ui/icons/components/chevron_up_outlined';
import { Box, Divider, Stack } from '@bika/ui/layouts';
import { Dropdown, <PERSON>u<PERSON><PERSON>on, <PERSON>u, MenuItem } from '@bika/ui/menu';
import { ViewIconMap } from '@bika/ui/node/types-form/resource-icons-map';
import { HeaderPageComponent } from '@bika/ui/web-layout';
import { DatabaseHeaderComponent, TabItem } from './database-header-view';
import { DatabaseViewVORenderer } from './database-view-vo-renderer';
import type { ICustomToolbar } from './interface';

interface Props extends ICustomToolbar {
  // value: DatabaseVO;
  // databaseId: string;
  params: {
    spaceId: string;
    viewId?: string;
    nodeVO?: NodeDetailVO;
  };
  isTemplatePreview?: boolean;
  onClickTab?: (viewId: string) => void;
}

export function DatabaseVORenderer(props: Props) {
  // const { value } = props;
  const context = useDatabaseVOContext();
  const value = context.value;
  const { t } = useLocale();

  assert(value);

  const views = value.views;
  const router = useSpaceRouter();

  const [collapseIndex, setCollapseIndex] = React.useState<number>(views.length);
  const [open, setOpen] = React.useState(false);

  const activeViewId = props.params.viewId;
  const activeViewIndex = views.findIndex((v) => (v.id || v.templateId) === activeViewId);
  const childListWidthRef = React.useRef<number[]>([]);

  const setMenuList = () => {
    if (childListWidthRef.current.length !== views.length) {
      return;
    }

    const tabs = document.querySelector('#database-view-tabs-container');
    const createTab = document.querySelector('#database-view-tabs-create-view');
    const SPACE_WIDTH = 180;
    const tabsWidth = (tabs?.clientWidth || 0) - SPACE_WIDTH - (createTab?.clientWidth || 0);
    let k = activeViewIndex;
    let totalWidth = 0;
    while (k <= activeViewIndex && k >= 0) {
      totalWidth += childListWidthRef.current[k] || 0;
      if (totalWidth > tabsWidth) {
        k++;
        // tabs 的宽度很小，只能显示一个tab
        if (k >= activeViewIndex) {
          k--;
        }
        break;
      }
      k--;
    }
    if (k === -1) {
      k = activeViewIndex;
      while (k < childListWidthRef.current.length) {
        k++;
        totalWidth += childListWidthRef.current[k];
        if (totalWidth > tabsWidth) {
          k--;
          break;
        }
      }
      // tabs 刚好显示 active view 前面所有的tabs
      if (k === activeViewIndex) {
        k = 0;
      }
    }

    setCollapseIndex(k);
  };

  const updateTabWidths = () => {
    const childrenList = document.querySelectorAll('.database-view-tab');
    let widthList: number[] = [];
    if (childrenList) {
      widthList = Array.from(childrenList).map((child) => child.clientWidth);
    }
    childListWidthRef.current = widthList;
  };

  useUpdateEffect(() => {
    setCollapseIndex(views.length);
  }, [activeViewId, views.length]);

  React.useLayoutEffect(() => {
    updateTabWidths();
    setMenuList();
  }, [collapseIndex]);

  // setMenuList with throttle
  React.useLayoutEffect(() => {
    const handleResize = throttle(() => {
      setTimeout(() => {
        setMenuList();
      });
    }, 200);
    const observer = new ResizeObserver(() => {
      handleResize();
    });
    const viewTabsElement = document.querySelector('#database-view-tabs-container');
    if (viewTabsElement) {
      observer.observe(viewTabsElement);
    }
    return () => {
      observer.disconnect();
    };
  }, [views.length]);

  const spaceContext = useSpaceContextForce();

  const handleAddView = () => {
    spaceContext.showUIDrawer({
      type: 'resource-editor',
      props: {
        screenType: 'DATABASE_VIEW',
        viewId: 'new',
        databaseId: value.id,
      },
    });
  };
  const currentView: ViewVO | undefined = views.find((v) => (v.id || v.templateId) === activeViewId) || views[0];
  if (!currentView) {
    return (
      <>
        ERROR: Cannot find View with ID: {activeViewId}, Views: {JSON.stringify(views)}
      </>
    );
  }

  // const isView = props.params.nodeVO?.type === 'VIEW';

  const isEmptyView = !views || views.length === 0;

  const sliceStart = collapseIndex > activeViewIndex ? 0 : collapseIndex + 1;
  const sliceEnd = collapseIndex >= activeViewIndex ? collapseIndex + 1 : activeViewIndex + 1;
  const hasPermissions = !!props.params.nodeVO?.permission;

  return (
    <HeaderPageComponent
      header={
        <DatabaseHeaderComponent
          name={value.name}
          description={value.description}
          databaseId={value.id}
          permission={props.params.nodeVO?.permission?.privilege}
          nodeDetail={props.params.nodeVO}
        >
          <Stack
            direction={'row'}
            alignItems={'center'}
            divider={
              <Divider
                orientation="vertical"
                sx={{
                  marginTop: '16px !important',
                  marginBottom: '10px !important',
                }}
              />
            }
          >
            {views.slice(sliceStart, sliceEnd).map((view, viewIndex) => (
              <TabItem
                className="database-view-tab"
                onClick={() => {
                  if (props.onClickTab) {
                    props.onClickTab(view.id);
                  } else {
                    if (props.isTemplatePreview) {
                      return;
                    }
                    // 默认，跳路由
                    router.push(`/space/${props.params.spaceId}/node/${value.id}/${view.id}`);
                  }
                }}
                key={view.id}
                name={view?.name}
                active={props.isTemplatePreview ? viewIndex === 0 : view.id === activeViewId}
                viewId={view.id}
                viewType={view.type}
                databaseId={value.id}
                disabled={props.isTemplatePreview}
              />
            ))}

            {views.length > collapseIndex && (
              <Dropdown>
                <MenuButton
                  onClick={() => setOpen(!open)}
                  sx={{
                    alignItems: 'center',
                    flexShrink: 0,
                    border: 'none',
                    fontSize: '13px',
                    fontWeight: '400',
                    '&:hover': {
                      backgroundColor: 'var(--bg-page)',
                    },
                  }}
                >
                  {t('resource.view_count', { count: views.length })}
                  <Box ml={0.5}>
                    {open ? (
                      <ChevronUpOutlined color="var(--text-secondary)" />
                    ) : (
                      <ChevronDownOutlined color="var(--text-secondary)" />
                    )}
                  </Box>
                </MenuButton>
                <Menu>
                  {views.map((view) => {
                    const Icon = ViewIconMap[view.type];
                    return (
                      <MenuItem
                        sx={{
                          color: view.id === activeViewId ? 'var(--brand) !important' : 'var(--text-primary)',

                          '& > svg': {
                            fill: view.id === activeViewId ? 'var(--brand)' : 'var(--text-primary)',
                          },
                        }}
                        selected={view.id === activeViewId}
                        key={view.id}
                        onClick={() => {
                          if (props.onClickTab) {
                            props.onClickTab(view.id);
                          } else {
                            // 默认，跳路由
                            router.push(`/space/${props.params.spaceId}/node/${value.id}/${view.id}`);
                          }
                        }}
                      >
                        <Icon size={16} />
                        {view.name}
                      </MenuItem>
                    );
                  })}
                </Menu>
              </Dropdown>
            )}

            {hasPermissions && (
              <VisibilityAdapter privilege="FULL_ACCESS">
                <Button
                  id="database-view-tabs-create-view"
                  variant="plain"
                  color="default"
                  sx={{
                    flex: '0 none',
                    mr: 1,
                    fontWeight: 'normal',
                    height: '40px',
                    '&:hover': {
                      backgroundColor: 'var(--bg-page)',
                    },
                  }}
                  onClick={handleAddView}
                  startDecorator={<AddOutlined color={'var(--text-secondary)'} />}
                >
                  {t.resource.create_view}
                </Button>
              </VisibilityAdapter>
            )}
          </Stack>
        </DatabaseHeaderComponent>
      }
    >
      <Box
        padding={'0 32px'}
        height={currentView.type === 'KANBAN' ? '100%' : undefined}
        sx={{
          borderTop: '1px solid var(--border-default)',
        }}
        bgcolor={currentView.type === 'KANBAN' ? 'var(--bg-surface)' : 'var(--bg-page)'}
      >
        {isEmptyView ? (
          <Box>{'No Views'}</Box>
        ) : (
          <DatabaseViewVORenderer {...props} value={currentView} params={props.params} />
        )}
      </Box>
    </HeaderPageComponent>
  );
}
