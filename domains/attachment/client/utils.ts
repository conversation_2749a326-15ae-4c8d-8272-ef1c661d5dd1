import React from 'react';
import { FileType } from '../../database/client/cells/cell-editor/attachment/enum';
import { isWhatFileType } from '../../database/client/cells/cell-editor/attachment/utils/file-type';

export function stopPropagation(e: React.MouseEvent | React.KeyboardEvent | React.WheelEvent) {
  e.stopPropagation();
  e.nativeEvent.stopImmediatePropagation();
}

export const NO_SUPPORT_IMG_MIME_TYPE = ['image/vnd.adobe.photoshop', 'image/tiff', 'image/vnd.dwg'];

export const isSupportImage = (mimeType: string) => !NO_SUPPORT_IMG_MIME_TYPE.includes(mimeType);

export const isWindowsOS = () => {
  const agent = navigator.userAgent.toLowerCase();
  if (agent.indexOf('win32') >= 0 || agent.indexOf('wow32') >= 0) {
    return true;
  }
  if (agent.indexOf('win64') >= 0 || agent.indexOf('wow64') >= 0) {
    return true;
  }
  return false;
};

export const isImage = (file: any) => {
  const fileType = isWhatFileType({ name: file.name, type: file.mimeType || file.type });
  return fileType === FileType.Image;
};
