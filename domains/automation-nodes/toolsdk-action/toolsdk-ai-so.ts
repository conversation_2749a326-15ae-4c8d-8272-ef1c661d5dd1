import { ToolSDKApiClient } from '@toolsdk.ai/sdk-ts/api';
import {
  AccountPackageConsumerPutBody,
  PackagePaginationQuery,
  PackageToolRunBody,
} from '@toolsdk.ai/sdk-ts/types/dto';
import { PackageDetailVO, PackageInstanceVO, PackagePageVO } from '@toolsdk.ai/sdk-ts/types/vo';

/**
 * ToolSDKAISO.
 */
export class ToolSDKAISO {
  private static _toolSDKAI?: ToolSDKApiClient;

  private static lazyInit() {
    if (this._toolSDKAI) {
      return;
    }
    const apiKey = process.env.TOOLSDK_AI_API_KEY;
    const baseURL = process.env.TOOLSDK_AI_BASE_URL;
    if (!apiKey) {
      throw new Error('ToolSDKAI api key is required');
    }
    this._toolSDKAI = new ToolSDKApiClient({ apiKey, baseURL });
  }

  /**
   * Fetch account token.
   *
   * @param accountKey
   * @param externalId  放到toolsdk的metadata里，存放bika user id
   * @returns
   */
  static async fetchAccountToken(accountKey: string, externalId: string): Promise<string> {
    this.lazyInit();
    return this._toolSDKAI!.account(accountKey).fetchToken(externalId);
  }

  static async getPackage(packageKey: string, packageVersion?: string): Promise<PackageDetailVO> {
    this.lazyInit();
    return this._toolSDKAI!.package(packageKey, packageVersion).info();
  }

  static async runPackageTool(args: {
    packageKey: string;
    packageVersion?: string;
    envs?: Record<string, string>;
    body: PackageToolRunBody;
  }) {
    this.lazyInit();
    const { packageKey, packageVersion, envs, body } = args;
    return this._toolSDKAI!.package(packageKey, packageVersion, envs).run(body);
  }

  static async getPackagePage(params?: PackagePaginationQuery): Promise<PackagePageVO> {
    this.lazyInit();
    return this._toolSDKAI!.packages.pages(params);
  }

  static async putPackageInstance(
    accountKey: string,
    consumerKey: string,
    body: AccountPackageConsumerPutBody,
  ): Promise<PackageInstanceVO> {
    this.lazyInit();
    return this._toolSDKAI!.account(accountKey).putPackageInstance(consumerKey, body);
  }

  static async runPackageInstance(instanceId: string, context?: Record<string, unknown>): Promise<unknown> {
    this.lazyInit();
    return this._toolSDKAI!.packageInstance(instanceId).run({ context });
  }
}
