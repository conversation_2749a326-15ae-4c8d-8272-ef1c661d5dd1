import type { ToolInvocation } from '@ai-sdk/ui-utils';
import React from 'react';
import { useLocale } from '@bika/contents/i18n';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { ArtifactContainer } from '../../ai/client/chat/artifacts/components/artifact-container';

interface FileArtifactProps {
  filePath?: string;
  content?: string;
  skillsets?: SkillsetSelectDTO[];
  tool?: ToolInvocation;
  expandable?: boolean;
}

export const FileArtifact = (props: FileArtifactProps) => {
  const { filePath, content, skillsets = [], tool, expandable = true } = props;
  const { t } = useLocale();

  // Create data object for ArtifactContainer
  const data = { filePath, content };

  // TODO integration for kfile preview
  return (
    <ArtifactContainer
      data={data}
      skillsets={skillsets}
      tool={tool}
      expandable={expandable}
      rowDataType="json"
      switchProps={{
        previewLabel: t.ai.artifact_preview,
      }}
    >
      <div className="flex flex-col w-full h-full p-4 overflow-auto">
        {filePath && (
          <div className="flex flex-col gap-2 items-center justify-center h-full">
            <img
              src={filePath}
              alt=""
              className="max-w-full max-h-full object-contain rounded-lg border border-gray-200"
            />
          </div>
        )}
      </div>
    </ArtifactContainer>
  );
};
