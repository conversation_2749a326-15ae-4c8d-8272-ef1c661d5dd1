import assert from 'assert';
import { AISO } from '@bika/domains/ai/server/ai-so';
// import { AISkillsetServerRegistry } from '@bika/domains/ai-skillset/server-registry';
import { AiNodeBO } from '@bika/types/ai/bo';
import { ArtifactAINodeAgentSchema, type ArtifactAINodeAgent, ArtifactVO } from '@bika/types/ai/vo';
import type { SkillsetSelectBO } from '@bika/types/skill/bo';
import { IStreamResult } from '../../ai/server/types';
import type { UserSO } from '../../user/server/user-so';
import { type AIArtifactSO } from '../ai-artifact-so';
import { IArtifactHandler } from '../interface';

export class AIAgentArtifactHandler implements IArtifactHandler {
  private _artifactSO: AIArtifactSO;

  public constructor(artifactSO: AIArtifactSO) {
    this._artifactSO = artifactSO;
  }

  async streamResult<T = ArtifactAINodeAgent>(user: UserSO): Promise<IStreamResult<T> | undefined> {
    assert(this._artifactSO.type === 'ai-agent', 'AIAgentArtifactHandler only support ai-agent type');
    const model = AISO.getSystemAIModel();

    // ======================================================
    // 1. 根据用户拿到 skillsets，之后塞入 system prompt 中 作为 few shot
    // 获取 search keywords
    //     const { result: skillsetSearchKeywordsStream } = await AISO.streamText(
    //       {
    //         system: `You are a Search Keywords Extractor. Extract keywords from user prompt for searching Bika.ai skillsets for AI Agent.
    // Return string must be a Comma-Separated one line string.
    // Format like: "keyword1, keyword2, keyword3".
    // `,
    //         prompt: `${this._artifactSO.prompt.system}
    // ${this._artifactSO.prompt.prompt}`,
    //         user,
    //       },
    //       {},
    //     );
    //     for await (const _ of skillsetSearchKeywordsStream.fullStream) {
    //       console.log(_);
    //     }
    //     const searchKeywordsStr = await skillsetSearchKeywordsStream.text;
    //     console.log('AIAgentArtifactHandler streamResult searchKeywords', searchKeywordsStr);

    //     const searchKeywords = searchKeywordsStr
    //       .split(',')
    //       .map((s) => s.trim())
    //       .filter((s) => s.length > 0);

    const skillsetsSelects: SkillsetSelectBO[] = [
      {
        kind: 'preset',
        key: 'default',
      },
    ];
    // for (const keyword of searchKeywords) {
    //   const searchResult = await AISkillsetServerRegistry.search(user, {
    //     query: keyword,
    //   });
    //   for (const skillsetVO of searchResult.data) {
    //     // vo 转 skillset select bo
    //   }
    // }

    console.log('prompt 在', this._artifactSO.prompt);

    // ======================================================
    // 2. 创建 AI agent 对象，包括 name, description, skillsets
    const streamResult = await AISO.streamObject(
      {
        ...this._artifactSO.prompt,
        system: `${this._artifactSO.prompt.system}
        
JSON data for the 'skillsets' fields: 
\`\`\`json
${JSON.stringify(skillsetsSelects)}
\`\`\`
`,
        prompt: this._artifactSO.prompt.prompt,
        schema: ArtifactAINodeAgentSchema,
        user,
      },
      {
        model,
      },
    );

    return {
      type: 'object',
      objectResult: streamResult, // streamResult,
    } as IStreamResult<T>;
  }

  async getValue(
    _user: UserSO,
    streamResult: IStreamResult | undefined,
    // dataStreamWriter: DataStreamWriter,
  ): Promise<ArtifactVO> {
    assert(streamResult, 'streamResult should not be undefined');
    assert(streamResult.type === 'object', 'SlidesArtifactHandler only support object type');
    const model = AISO.getSystemAIModel();

    const vo: ArtifactVO = {
      type: 'ai-agent',
      id: this._artifactSO.id,
      data: (await streamResult.objectResult.object) as AiNodeBO,
    };
    vo.usage = await AISO.parseAICreditCost(model, await streamResult.objectResult.usage);
    return vo;
  }
}
