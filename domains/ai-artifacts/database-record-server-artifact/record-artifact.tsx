import type { ToolInvocation } from '@ai-sdk/ui-utils';
import { useTheme } from '@mui/joy/styles';
import { useLocale } from '@bika/contents/i18n';
import { FieldVO, RecordVO } from '@bika/types/database/vo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { Box } from '@bika/ui/layouts';
import { ArtifactContainer } from '../../ai/client/chat/artifacts/components/artifact-container';
import { RecordVOComponent } from '../../database/client/record-detail/record-detail-component';
import { useDatabaseMeta } from '../../database/client/record-detail/use-database-meta';

export const RecordDetailComponent = (props: { data: RecordVO }) => {
  const theme = useTheme();
  console.log('theme', theme.palette.mode);

  const databaseMeta = useDatabaseMeta(props.data.databaseId || '');
  const fieldList: FieldVO[] = databaseMeta?.columns ?? [];

  return <RecordVOComponent data={props.data} fieldList={fieldList} />;
};

interface RecordArtifactProps {
  data: RecordVO[];
  fields: FieldVO[];
  skillsets: SkillsetSelectDTO[];
  tool: ToolInvocation;
}

export const RecordArtifact = (props: RecordArtifactProps) => {
  const { skillsets, tool, fields } = props;
  const { t } = useLocale();

  return (
    <ArtifactContainer
      expandable={false}
      data={props.data}
      tool={tool}
      skillsets={skillsets}
      switchProps={{
        rowDataLabel: t.ai.artifact_code,
        previewLabel: t.ai.artifact_preview,
      }}
    >
      <Box className="p-[12px] w-full">
        {props.data.map((record, key) => (
          <RecordVOComponent key={key} data={record} fieldList={fields} />
        ))}
      </Box>
    </ArtifactContainer>
  );
};
