import { generateNanoID } from 'sharelib/nano-id';
import { expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { FieldSO } from '@bika/domains/database/server/fields/field-so';
import { utils } from '@bika/domains/shared/server';
import { Automation } from '@bika/types/automation/bo';
import {
  CommentRecordMission,
  RedirectSpaceNodeMission,
  ReviewRecordMission,
  MissionType,
} from '@bika/types/mission/bo';
import { MissionHandlerFactory } from '../server/handlers/mission-handler-factory';
import { IReviewRecordMissionOutput } from '../server/handlers/types';
import { MissionSO } from '../server/mission-so';

test('Comment Record Mission Handler Test', async () => {
  // create user
  const { user, space } = await MockContext.initUserContext();

  const recId = generateNanoID('DB_REC_', 10);
  // create mission
  const missionSOs = await MissionSO.createMission({
    user,
    spaceId: space.id,
    missionTemplate: {
      name: '请你 Review 下这个季度 OKR 的最新完成情况',
      type: 'COMMENT_RECORD',
      databaseId: 'okr_database_id',
      recordId: '<%= record.id %>',
      to: [
        {
          type: 'ALL_MEMBERS',
        },
      ],
    },
    props: {
      record: {
        id: recId,
      },
    },
  });
  expect(missionSOs.length).toBe(1);
  const missionSO = missionSOs[0];
  const { recordId } = missionSO.model.bo as CommentRecordMission;
  expect(recordId).toBe(recId);
});

test('Review Record Mission Handler Test', async () => {
  // create user
  const { user, member, space, rootFolder } = await MockContext.initUserContext();

  const randomDatabaseTemplateId = generateNanoID('DB_TPL_', 10);
  const templateFolderSO = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);
  const databaseNodeSO = await templateFolderSO.createChildSimple(user, {
    name: 'database',
    templateId: randomDatabaseTemplateId,
    // databaseType: 'DATUM',
    fields: [
      {
        templateId: 'name',
        name: 'Name',
        type: 'LONG_TEXT',
      },
    ],
    resourceType: 'DATABASE',
  });
  const databaseSO = await databaseNodeSO.toResourceSO<DatabaseSO>();
  const nameFieldId = databaseSO.getFields().find((field: FieldSO) => field.templateId === 'name')!.id;
  const recordSO = await databaseSO.createRecord(user, member, {
    name: 'Tom',
  });
  expect(recordSO).toBeDefined();

  const randomName = utils.generateRandomString(10);
  const randomDescription = utils.generateRandomString(12);
  const recId = recordSO.model.id;
  const props = {
    record: {
      id: recId,
    },
    _mission: [
      {
        name: randomName,
        description: randomDescription,
      },
    ],
  };
  // create mission to all members
  const missionSOs = await MissionSO.createMission({
    user,
    spaceId: space.id,
    templateNodeId: templateFolderSO.id,
    missionTemplate: {
      type: 'REVIEW_RECORD',
      name: '<%= _mission[0].name %>',
      description: 'Mission Test Description: <%= _mission[0].description %>',
      databaseTemplateId: randomDatabaseTemplateId,
      recordId: '<%= record.id %>',
      to: [
        {
          type: 'ALL_MEMBERS',
        },
      ],
    },
    props,
  });
  expect(missionSOs.length).toBe(1);
  const missionSO = missionSOs[0];
  const { id, type, name, description, bo } = missionSO.model;
  const { databaseId, recordId } = bo as ReviewRecordMission;
  // test renderBO()
  expect(name).toBe(randomName);
  expect(description).toBe(`Mission Test Description: ${randomDescription}`);
  expect(recordId).toBe(recId);
  expect(databaseId).toBe(databaseSO.id);

  const missionHandler = MissionHandlerFactory.getMissionHandler(type as MissionType);
  // test output()
  const output = await missionHandler.output(missionSO);
  expect(output).toBeDefined();
  expect(output._mission).toBeDefined();
  expect(output._mission.id).toBe(id);
  expect(output._mission.name).toBe(randomName);
  const { record } = output as IReviewRecordMissionOutput;
  expect(record.id).toBe(recId);
  expect(record.cells[nameFieldId].data).toBe('Tom');
});

test('Redirect Space Node Mission Handler Test', async () => {
  // create user
  const { user, space, rootFolder } = await MockContext.initUserContext();

  const templateFolderSO = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);
  const randomNodeTemplateId = generateNanoID('DB_TPL_', 10);

  const automationTpl: Automation = {
    templateId: randomNodeTemplateId,
    name: 'Redirect Space Node Mission Handler Test Node',
    triggers: [],
    actions: [],
    status: 'INACTIVE',
    resourceType: 'AUTOMATION',
  };
  const nodeSO = await templateFolderSO.createChildSimple(user, automationTpl);

  const randomName = utils.generateRandomString(10);
  const randomDescription = utils.generateRandomString(12);
  const props = {
    _mission: [
      {
        name: randomName,
        description: randomDescription,
      },
    ],
  };
  // create mission
  const missionSOs = await MissionSO.createMission({
    user,
    spaceId: space.id,
    templateNodeId: templateFolderSO.id,
    missionTemplate: {
      type: 'REDIRECT_SPACE_NODE',
      name: '<%= _mission[0].name %>',
      description: 'Mission Test Description: <%= _mission[0].description %>',
      nodeId: nodeSO.id,
      to: [
        {
          type: 'ALL_MEMBERS',
        },
      ],
    },
    props,
  });
  expect(missionSOs.length).toBe(1);
  const missionSO = missionSOs[0];
  const { name, description, bo } = missionSO.model;
  const { nodeId } = bo as RedirectSpaceNodeMission;
  // test renderBO()
  expect(name).toBe(randomName);
  expect(description).toBe(`Mission Test Description: ${randomDescription}`);
  expect(nodeId).toBe(nodeSO.id);
});
