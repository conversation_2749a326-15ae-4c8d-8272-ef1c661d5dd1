import dayjs from 'dayjs';
import { expect, describe, test } from 'vitest';
import { MockContext, waitForMatchToBeMet } from '@bika/domains/__tests__/mock';
import { CommentSO } from '@bika/domains/database/server';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { FieldSO } from '@bika/domains/database/server/fields/field-so';
import { RecordSO } from '@bika/domains/database/server/record-so';
import { NotificationSO } from '@bika/domains/notification/server/notification-so';
import { generateEmail } from '@bika/domains/shared/server';
import { SchedulerSO } from '@bika/domains/system/server/scheduler-so';
import { MissionRelationType } from '@bika/domains/system/server/types';
import { Database } from '@bika/types/database/bo';
import { Mission } from '@bika/types/mission/bo';
import { MissionNotificationProperty } from '@bika/types/notification/type';
import { MissionSO } from '../server/mission-so';

test('Complete & Resolve Mission', async () => {
  // 创建一个管理员
  const { user, space } = await MockContext.initUserContext();
  const mainAdmin = await space.getOwner();

  // 创建一个成员
  const { user: user2 } = await MockContext.createMockUser({
    email: generateEmail('bika.ai'),
    name: 'kelvin',
  });
  const rootTeam = await space.getRootTeam();
  const member = await space.joinUser(user2.id, rootTeam.id);

  expect(await space.getMemberCount()).toBe(2);

  // create mission to all members
  const missionSO = await MissionSO.createMission({
    user,
    spaceId: space.id,
    missionTemplate: {
      type: 'CREATE_RECORD',
      name: 'Mission in Complete Mission Test',
      description: 'Mission Test Description, Create Record',
      databaseId: 'your_database_id',
      viewId: 'HERE will be ignored and render',
      assignType: 'DEDICATED',
      to: [
        {
          type: 'ALL_MEMBERS',
        },
      ],
      canReject: true,
      reminders: [
        {
          name: '马上要开始晨会啦，请尽快填写简报',
          description: '温馨提示：10点截止填写，系统会自动生成汇总报告给大家，请抓紧时间填写哦。',
          scheduler: {
            timezone: 'AUTO',
            datetime: '2021-08-01T09:30:00Z',
            repeat: {
              every: {
                type: 'DAY',
                interval: 1,
              },
            },
          },
          to: [],
        },
        {
          name: '你尚未提交简报，任务已过期',
          description: '10点截止填写简报，系统稍后将会生成日报给大家。如果你请假了，请忽略本消息。',
          scheduler: {
            timezone: 'AUTO',
            datetime: '2021-08-01T10:00:00Z',
            repeat: {
              every: {
                type: 'DAY',
                interval: 1,
              },
            },
          },
          // cron: '0 10 * * *',
          to: [],
        },
      ],
    },
  });
  expect(missionSO.length).toBe(2);

  // check reminder for main admin
  const reminders = await mainAdmin.getReminders();
  expect(reminders.length).toBe(2); // 两个mission，带了2个reminder
  expect(reminders[0].model.isActive).toBe(true);
  expect(reminders[1].model.isActive).toBe(true);

  // main admin complete mission
  await MissionSO.resolve(mainAdmin, {
    type: 'CREATE_RECORD',
    databaseId: 'your_database_id',
    recordId: 'your_record_id',
  });

  await waitForMatchToBeMet(
    async () => {
      const mainAdminMissions = await mainAdmin.getMissions({
        type: 'CREATE_RECORD',
        queryFilterType: 'PENDING',
      });
      return mainAdminMissions.length === 0;
    },
    5000,
    200,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });
  // recheck reminder after mission completed
  const reminderAfterMissionCompleted = await mainAdmin.getReminders();
  expect(reminderAfterMissionCompleted.length).toBe(0);

  // member's mission should be uncompleted
  const memberMissions = await member.getMissions({
    type: 'CREATE_RECORD',
    queryFilterType: 'PENDING',
  });
  expect(memberMissions.length).toBe(1);
  const memberReminder = await member.getReminders();
  expect(memberReminder.length).toBe(2);

  await member.rejectMission(memberMissions[0].id);
  await waitForMatchToBeMet(
    async () => {
      const reminderAfterMissionRejected = await member.getReminders();
      return reminderAfterMissionRejected.length === 0;
    },
    5000,
    200,
  ) // 目标值为0，超时时间5秒，检查间隔200毫秒
    .then(() => console.log('Condition met within the timeout period.'))
    .catch((error: Error) => {
      throw new Error(error.message);
    });
}, 15000);

test('Complete Mission by Event', async () => {
  // create user
  const { user, space, member, rootFolder } = await MockContext.initUserContext();

  const databaseTemplate: Database = {
    name: 'database',
    templateId: 'database_template_id',
    databaseType: 'DATUM',
    fields: [
      {
        templateId: 'field_template_id',
        name: 'Name',
        type: 'LONG_TEXT',
      },
    ],
    resourceType: 'DATABASE',
  };
  const missionTemplate: Mission = {
    type: 'CREATE_RECORD',
    name: 'Mission in Complete Mission by Event Test',
    assignType: 'DEDICATED',
    to: [{ type: 'ALL_MEMBERS' }],
    databaseTemplateId: 'database_template_id',
  };

  // duplicate create database and mission with different template
  const templateFolderSO = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);
  const databaseNodeSO = await templateFolderSO.createChildSimple(user, databaseTemplate);
  const missionSO = await MissionSO.createMission({
    user,
    spaceId: space.id,
    templateNodeId: templateFolderSO.model.id,
    missionTemplate,
  });
  expect(missionSO.length).toBe(1);
  const templateFolderSO2 = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);
  await templateFolderSO2.createChildSimple(user, databaseTemplate);
  const missionSO2 = await MissionSO.createMission({
    user,
    spaceId: space.id,
    templateNodeId: templateFolderSO2.model.id,
    missionTemplate,
  });
  expect(missionSO2.length).toBe(1);

  // create record to complete mission by event
  const databaseSO = await databaseNodeSO.toResourceSO<DatabaseSO>();
  const recordSO = await databaseSO.createRecord(user, member, {});

  const commentMissionSOs = await MissionSO.createMission({
    user,
    spaceId: space.id,
    templateNodeId: templateFolderSO.model.id,
    missionTemplate: {
      name: 'Comment Mission Complete by Event Test',
      type: 'COMMENT_RECORD',
      databaseTemplateId: 'database_template_id',
      recordId: '<%= record.id %>',
      to: [{ type: 'ALL_MEMBERS' }],
    },
    props: {
      record: { id: recordSO.id },
    },
  });
  expect(commentMissionSOs.length).toBe(1);
  // create comment to complete mission by event
  await CommentSO.create(
    user.id,
    'RECORD',
    recordSO.id,
    'Content in Comment Mission Complete by Event Test',
    recordSO.spaceId,
    member.id,
  );

  const updateRecordMissionSOs = await MissionSO.createMission({
    user,
    spaceId: space.id,
    templateNodeId: templateFolderSO.model.id,
    missionTemplate: {
      name: 'Update Record Mission Complete by Event Test',
      type: 'UPDATE_RECORD',
      databaseTemplateId: 'database_template_id',
      recordId: '<%= record.id %>',
      to: [{ type: 'ALL_MEMBERS' }],
    },
    props: {
      record: { id: recordSO.id },
    },
  });
  expect(updateRecordMissionSOs.length).toBe(1);
  // update record to complete mission by event
  await databaseSO.updateRecords(user, [
    {
      recordId: recordSO.id,
      cells: {
        field_template_id: 'Updated Name',
      },
    },
  ]);

  await waitForMatchToBeMet(
    async () => {
      const completedMissions = await member.getMissions({
        queryFilterType: 'COMPLETED',
      });
      return completedMissions.length === 3;
    },
    5000,
    200,
  ) // 目标值为3，超时时间5秒，检查间隔200毫秒
    .then(() => console.log('Condition met within the timeout period.'))
    .catch((error: Error) => {
      throw new Error(error.message);
    });

  // check mission completed status
  const completedMissions = await member.getMissions({
    queryFilterType: 'COMPLETED',
  });
  const createRecordMission = completedMissions.find((m) => m.model.type === 'CREATE_RECORD');
  expect(createRecordMission).toBeDefined();
  expect(createRecordMission!.id).toBe(missionSO[0].id);
  const commentMissionSO = completedMissions.find((m) => m.model.type === 'COMMENT_RECORD');
  expect(commentMissionSO).toBeDefined();
  expect(commentMissionSO!.id).toBe(commentMissionSOs[0].id);
  const updateRecordMissionSO = completedMissions.find((m) => m.model.type === 'UPDATE_RECORD');
  expect(updateRecordMissionSO).toBeDefined();
  expect(updateRecordMissionSO!.id).toBe(updateRecordMissionSOs[0].id);

  const uncompletedMission = await member.getMissions({
    type: 'CREATE_RECORD',
    queryFilterType: 'PENDING',
  });
  expect(uncompletedMission.length).toBe(1);
  expect(uncompletedMission[0].id).toBe(missionSO2[0].id);
}, 15000);

test('Mission - After Actions', async () => {
  // create user
  const { user, space, member, rootFolder } = await MockContext.initUserContext();

  const templateFolderSO = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);

  const databaseNodeSO = await templateFolderSO.createChildSimple(user, {
    name: 'OKR',
    templateId: 'okr_database',
    // databaseType: 'DATUM',
    fields: [
      {
        templateId: 'okr_review',
        name: 'What is your previous quarter progress?  Review it.',
        type: 'LONG_TEXT',
      },
      {
        templateId: 'okr_align_superior_confirm',
        name: 'confirm',
        type: 'CHECKBOX',
      },
    ],
    resourceType: 'DATABASE',
  });

  const databaseSO = await databaseNodeSO.toResourceSO<DatabaseSO>();

  const confirmFieldId = databaseSO
    .getFields()
    .find((field: FieldSO) => field.templateId === 'okr_align_superior_confirm')!.id;

  const recordSO = await databaseSO.createRecord(user, member, {
    okr_review: 'review',
    okr_align_superior_confirm: false,
  });
  expect(recordSO).toBeDefined();
  const recordVO = recordSO.toVO();
  expect(recordVO.cells[confirmFieldId].data).toBe(false);

  // 特意加多一个用户，确保share任务，向all members，但是只有一个mission
  const mockUser2 = await MockContext.createMockUser();
  const rootTeam = await space.getRootTeam();
  await space.joinUser(mockUser2.user.id, rootTeam.id);

  const missionSOs = await MissionSO.createMission({
    user,
    spaceId: space.id,
    templateNodeId: templateFolderSO.id,
    missionTemplate: {
      type: 'REVIEW_RECORD',
      name: 'Mission in After Actions Test',
      databaseId: databaseSO.id,
      recordId: recordSO.model.id,
      assignType: 'SHARE',
      to: [
        {
          type: 'ALL_MEMBERS',
        },
      ],
      canCompleteManually: true,
      afterActions: [
        {
          actionType: 'UPDATE_RECORD',
          input: {
            type: 'SPECIFY_RECORD_BODY',
            databaseTemplateId: 'okr_database',
            recordId: '<%= record.id %>',
            fieldKeyType: 'TEMPLATE_ID',
            data: {
              okr_align_superior_confirm: true,
            },
          },
        },
      ],
    },
  });
  expect(missionSOs.length).toBe(1); // share assign，2个人共享一个mission

  await missionSOs[0].completeManually(member);

  // wait for mission after actions to be completed
  await waitForMatchToBeMet(
    async () => {
      const recordSO2 = await RecordSO.init(recordSO.model.id, missionSOs[0].spaceId);
      const recordVO2 = await recordSO2.toVO();
      return recordVO2.cells[confirmFieldId].data === true;
    },
    5000,
    200,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });
});

describe('Sequence Mission', async () => {
  // create user
  const { user, space, member, rootFolder } = await MockContext.initUserContext();

  const rootTeam = await space.getRootTeam();

  const { user: user2 } = await MockContext.createMockUser();
  const member2 = await space.joinUser(user2.id, rootTeam.id);
  expect((await member2.getMissions()).length).toBe(0);

  const { user: user3 } = await MockContext.createMockUser();
  const member3 = await space.joinUser(user3.id, rootTeam.id);

  const { user: user4 } = await MockContext.createMockUser();
  const member4 = await space.joinUser(user4.id, rootTeam.id);

  const templateFolderSO = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);
  const databaseNodeSO = await templateFolderSO.createChildSimple(user, {
    name: 'Database',
    templateId: 'sequence_mission_database',
    // databaseType: 'DATUM',
    fields: [
      {
        templateId: 'sequence_mission_text_field',
        name: 'Text',
        type: 'LONG_TEXT',
      },
      {
        templateId: 'sequence_mission_member_field',
        name: 'Member',
        type: 'MEMBER',
        property: {},
      },
    ],
    resourceType: 'DATABASE',
  });

  const missionSOs = await MissionSO.createMission({
    user,
    spaceId: space.id,
    templateNodeId: templateFolderSO.model.id,
    missionTemplate: {
      type: 'SEQUENCE',
      name: 'Sequence Mission Test',
      assignType: 'SHARE',
      to: [], // 给不同人创建子任务，总任务可以不指定to
      missions: [
        {
          type: 'CREATE_RECORD',
          name: 'First Sub Mission in Sequence Mission',
          databaseTemplateId: 'sequence_mission_database',
          to: [{ type: 'SPECIFY_UNITS', unitIds: [member2.id] }],
        },
        {
          type: 'REVIEW_RECORD',
          name: 'Second Sub Mission in Sequence Mission',
          databaseTemplateId: 'sequence_mission_database',
          recordId: '<%= _missions[0].record.id %>', // 依赖于第一个任务的recordId
          to: [{ type: 'SPECIFY_UNITS', unitIds: [member3.id] }],
        },
        {
          type: 'REVIEW_RECORD',
          name: 'Third Sub Mission in Sequence Mission',
          databaseTemplateId: 'sequence_mission_database',
          recordId: '<%= _missions[0].record.id %>',
          to: [
            {
              type: 'SPECIFY_UNITS',
              // 依赖于第一个任务的记录数据
              unitIds: ['<%= _missions[0].record.cells.sequence_mission_member_field.data[0] %>'],
            },
          ],
        },
      ],
    },
  });
  // 一个总任务 + 第一个子任务。后续的子任务可能依赖于第一个任务输出，所以不会创建
  expect(missionSOs.length).toBe(2);
  const missions = await member.getMissions({ type: 'SEQUENCE', queryFilterType: 'MY_CREATED' });
  expect(missions.length).toBe(1);
  const sequenceMission = await MissionSO.init(missions[0].id);
  expect(sequenceMission.model.status).toBe('PENDING');
  // 子任务数量为1
  const subMissions = await sequenceMission.getSubMissions();
  expect(subMissions.length).toBe(1);

  expect((await member2.getMissions()).length).toBe(1); // 第一个子任务
  expect((await member3.getMissions()).length).toBe(0); // 第二个子任务待创建

  // 成员2创建记录，完成第一个子任务
  const databaseSO = await databaseNodeSO.toResourceSO<DatabaseSO>();
  const recordSO = await databaseSO.createRecord(user2, member2, {
    sequence_mission_text_field: 'text~',
    sequence_mission_member_field: [member4.id],
  });
  // 等待异步事件完成，创建第二个子任务
  await waitForMatchToBeMet(
    async () => {
      const subs = await sequenceMission.getSubMissions();
      return subs.length === 2;
    },
    2000,
    100,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });
  // 第一个子任务已完成
  expect((await member2.getMissions({ queryFilterType: 'COMPLETED' })).length).toBe(1);

  // 总任务状态仍为进行中
  const sequenceMission2 = await MissionSO.init(sequenceMission.id);
  expect(sequenceMission2.model.status).toBe('PENDING');

  // 第二个子任务已被创建
  const missions2 = await member3.getMissions();
  expect(missions2.length).toBe(1);
  const secondMission = missions2[0];
  expect(secondMission.model.bo.recordId).toBe(recordSO.id);
  // 第三个子任务待创建
  expect((await member4.getMissions()).length).toBe(0);

  // 成员3完成第二个子任务
  await member3.completeMissionManually(secondMission.id);
  // 等待异步事件完成，创建第三个子任务
  await waitForMatchToBeMet(
    async () => {
      const subs = await sequenceMission.getSubMissions();
      return subs.length === 3;
    },
    2000,
    100,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });
  // 第二个子任务已完成
  expect((await member3.getMissions({ queryFilterType: 'COMPLETED' })).length).toBe(1);
  // 总任务状态仍为进行中
  const sequenceMission3 = await MissionSO.init(sequenceMission.id);
  expect(sequenceMission3.model.status).toBe('PENDING');

  // 第三个子任务被创建
  const missions3 = await member4.getMissions();
  expect(missions3.length).toBe(1);
  const thirdMission = missions3[0];
  expect(thirdMission.model.bo.recordId).toBe(recordSO.id);

  // 成员4完成第三个子任务
  await member4.completeMissionManually(thirdMission.id);
  // 等待异步事件完成，完成总任务
  await waitForMatchToBeMet(
    async () => {
      const sequenceMission4 = await MissionSO.init(sequenceMission.id);
      return sequenceMission4.model.status === 'COMPLETED';
    },
    2000,
    200,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });
  // 第三个子任务已完成
  expect((await member4.getMissions({ queryFilterType: 'COMPLETED' })).length).toBe(1);

  // === 测试过期 ===
  test('sequence mission due', async () => {
    const missionSOs2 = await MissionSO.createMission({
      user,
      spaceId: space.id,
      templateNodeId: templateFolderSO.model.id,
      missionTemplate: {
        type: 'SEQUENCE',
        name: 'Sequence Mission with Due Date Test',
        assignType: 'DEDICATED',
        // 子任务没有to，沿用父任务的to。相当于同一个人，连续完成任务（类似游戏闯关）
        to: [{ type: 'ADMIN' }],
        missions: [
          {
            type: 'REVIEW_RECORD',
            name: 'First Sub Mission',
            databaseTemplateId: 'sequence_mission_database',
            recordId: recordSO.id,
            to: [],
          },
          {
            type: 'COMMENT_RECORD',
            name: 'Second Sub Mission',
            databaseTemplateId: 'sequence_mission_database',
            recordId: '<%= _missions[0].record.id %>',
            to: [],
          },
        ],
        dueDate: {
          end: dayjs().add(5, 'minute').toISOString(), // 5分钟后过期
        },
      },
    });
    expect(missionSOs2.length).toBe(2);
    const firstSubMission = missionSOs2.find((m) => m.model.type === 'REVIEW_RECORD');
    expect(firstSubMission).toBeDefined();
    // 完成第一个子任务
    await member.completeMissionManually(firstSubMission!.id);
    await waitForMatchToBeMet(
      async () => {
        const sequenceSubMission = missionSOs2.find((m) => m.model.type === 'SEQUENCE');
        const subs = await sequenceSubMission?.getSubMissions();
        return subs?.length === 2;
      },
      5000,
      100,
    ) // 目标值为2，超时时间5秒，检查间隔100毫秒
      .then(() => console.log('Condition met within the timeout period.'))
      .catch((error: Error) => {
        throw new Error(error.message);
      });
    // 模拟时间过去了，执行Scheduler
    await SchedulerSO.scanRunnableSchedulers(dayjs().add(10, 'minute').toDate(), 10, MissionRelationType);
    // 总任务过期，最后一个子任务也过期
    await waitForMatchToBeMet(
      async () => {
        const dueMissions = await MissionSO.getMissionsBySpaceId(space.id, { status: 'DUE' });
        return dueMissions.length === 2;
      },
      5000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });

    const missionSOs3 = await MissionSO.createMission({
      user,
      spaceId: space.id,
      templateNodeId: templateFolderSO.model.id,
      missionTemplate: {
        type: 'SEQUENCE',
        name: 'Sequence Mission Test2',
        to: [{ type: 'SPECIFY_UNITS', unitIds: [member2.id] }],
        missions: [
          {
            type: 'REVIEW_RECORD',
            name: 'Sub Mission with Due Date',
            databaseTemplateId: 'sequence_mission_database',
            recordId: recordSO.id,
            to: [],
            dueDate: {
              end: dayjs().add(5, 'minute').toISOString(), // 5分钟后过期
            },
          },
        ],
      },
    });
    expect(missionSOs3.length).toBe(2);
    // 模拟时间过去了，执行Scheduler
    await SchedulerSO.scanRunnableSchedulers(dayjs().add(10, 'minute').toDate(), 10, MissionRelationType);
    // 子任务过期，总任务也过期
    await waitForMatchToBeMet(
      async () => {
        const dueMissions = await member2.getMissions({ queryFilterType: 'DUE' });
        return dueMissions.length === 2;
      },
      5000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
  });

  // === 测试拒绝 ===
  test('sequence mission reject', async () => {
    const missionSOs4 = await MissionSO.createMission({
      user,
      spaceId: space.id,
      templateNodeId: templateFolderSO.model.id,
      missionTemplate: {
        type: 'SEQUENCE',
        name: 'Sequence Mission canReject Test',
        to: [{ type: 'SPECIFY_UNITS', unitIds: [member3.id] }],
        missions: [
          {
            type: 'REVIEW_RECORD',
            name: 'First Sub Mission',
            databaseTemplateId: 'sequence_mission_database',
            recordId: recordSO.id,
            to: [],
          },
        ],
        canReject: true,
      },
    });
    const canRejectSequenceMission = missionSOs4.find((m) => m.model.type === 'SEQUENCE');
    expect(canRejectSequenceMission).toBeDefined();
    await member3.rejectMission(canRejectSequenceMission!.id);
    // 总任务被拒绝，子任务当已完成处理（参考企微撤回审批逻辑）
    const rejectedMissions = await member3.getMissions({ queryFilterType: 'REJECTED' });
    expect(rejectedMissions.length).toBe(1);
    expect(rejectedMissions[0].id).toBe(canRejectSequenceMission!.id);
    const subs = await rejectedMissions[0].getSubMissions();
    expect(subs.length).toBe(1);
    expect(subs[0].model.status).toBe('COMPLETED');

    const missionSOs5 = await MissionSO.createMission({
      user,
      spaceId: space.id,
      templateNodeId: templateFolderSO.model.id,
      missionTemplate: {
        type: 'SEQUENCE',
        name: 'Sequence Mission Test3',
        to: [{ type: 'ADMIN' }, { type: 'SPECIFY_UNITS', unitIds: [member4.id] }],
        missions: [
          {
            type: 'REVIEW_RECORD',
            name: 'Sub Mission canReject',
            databaseTemplateId: 'sequence_mission_database',
            recordId: recordSO.id,
            to: [],
            canReject: true,
          },
        ],
      },
    });
    const canRejectSubMission = missionSOs5.find((m) => m.model.type === 'REVIEW_RECORD');
    expect(canRejectSubMission).toBeDefined();
    await member4.rejectMission(canRejectSubMission!.id);
    // 子任务被拒绝，总任务也被拒绝
    const rejectedMissions2 = await member4.getMissions({ queryFilterType: 'REJECTED' });
    expect(rejectedMissions2.length).toBe(2);
    for (const mission of rejectedMissions2) {
      const recipients = await mission.getRecipients();
      expect(recipients.length).toBe(2);
      if (mission.model.type === 'SEQUENCE') {
        // 总任务的 recipient.missionStatus 为 REJECTED，recipientStatus 为 PENDING
        expect(recipients[0].model.state.missionStatus).toBe('REJECTED');
        expect(recipients[0].model.state.recipientStatus).toBe('PENDING');
        expect(recipients[1].model.state.missionStatus).toBe('REJECTED');
        expect(recipients[1].model.state.recipientStatus).toBe('PENDING');
      } else if (mission.model.type === 'REVIEW_RECORD') {
        // 子任务，操作者的missionStatus、recipientStatus 均为 REJECTED，其他人的同上
        for (const rec of recipients) {
          if (rec.model.recipientId === member4.id) {
            expect(rec.model.state.missionStatus).toBe('REJECTED');
            expect(rec.model.state.recipientStatus).toBe('REJECTED');
          } else {
            expect(rec.model.state.missionStatus).toBe('REJECTED');
            expect(rec.model.state.recipientStatus).toBe('PENDING');
          }
        }
      }
    }
  });

  // === 测试删除 ===
  test('sequence mission delete', async () => {
    await NotificationSO.markAllAsRead(user.id);
    await NotificationSO.markAllAsRead(user4.id);
    const missionSOs6 = await MissionSO.createMission({
      user,
      spaceId: space.id,
      templateNodeId: templateFolderSO.model.id,
      missionTemplate: {
        type: 'SEQUENCE',
        name: 'Sequence Mission Delete Test',
        assignType: 'DEDICATED',
        to: [{ type: 'ADMIN' }, { type: 'SPECIFY_UNITS', unitIds: [member4.id] }],
        missions: [
          {
            type: 'COMMENT_RECORD',
            name: 'Sub Mission Delete Test',
            databaseTemplateId: 'sequence_mission_database',
            recordId: recordSO.id,
            to: [],
          },
        ],
      },
    });
    expect(missionSOs6.length).toBe(4);
    const missionIds = missionSOs6.map((m) => m.id);
    // 确认异步通知已完成，防止CI删除任务后，新建mission的通知还没结束
    await waitForMatchToBeMet(
      async () => {
        let count = 0;
        const [notifications, notifications4] = await Promise.all([
          NotificationSO.find(user.id, { hasRead: false }),
          NotificationSO.find(user4.id, { hasRead: false }),
        ]);
        if (notifications.length < 2 || notifications4.length < 2) {
          return false;
        }
        [...notifications, ...notifications4].forEach((n) => {
          if (n.type !== 'MISSION_CREATED') {
            return;
          }
          const { missionId } = n.property as MissionNotificationProperty;
          if (missionIds.includes(missionId)) {
            count++;
          }
        });
        return count === 4;
      },
      2000,
      200,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });

    const sequenceMissions = await member.getMissions({ type: 'SEQUENCE', queryFilterType: 'PENDING' });
    expect(sequenceMissions.length).toBe(1);
    expect(sequenceMissions[0].model.name).toBe('Sequence Mission Delete Test');
    const commentRecordMissions = await member.getMissions({ type: 'COMMENT_RECORD', queryFilterType: 'PENDING' });
    expect(commentRecordMissions.length).toBe(1);
    expect(commentRecordMissions[0].model.name).toBe('Sub Mission Delete Test');
    // 删除总任务，子任务也被删除
    await sequenceMissions[0].delete(member);
    const sequenceMissionAfterDelete = await MissionSO.initMaybeNull(sequenceMissions[0].id);
    expect(sequenceMissionAfterDelete).toBeNull();
    const subMissionAfterDelete = await MissionSO.initMaybeNull(commentRecordMissions[0].id);
    expect(subMissionAfterDelete).toBeNull();

    const sequenceMissions2 = await member4.getMissions({ type: 'SEQUENCE', queryFilterType: 'PENDING' });
    expect(sequenceMissions2.length).toBe(1);
    expect(sequenceMissions2[0].model.name).toBe('Sequence Mission Delete Test');
    const commentRecordMissions2 = await member4.getMissions({ type: 'COMMENT_RECORD', queryFilterType: 'PENDING' });
    expect(commentRecordMissions2.length).toBe(1);
    expect(commentRecordMissions2[0].model.name).toBe('Sub Mission Delete Test');
    // 删除子任务，总任务失效
    await commentRecordMissions2[0].delete(member4);
    const sequenceMissionAfterDelete2 = await MissionSO.init(sequenceMissions2[0].id);
    expect(sequenceMissionAfterDelete2.model.status).toBe('INVALID');
    const subMissionAfterDelete2 = await MissionSO.initMaybeNull(commentRecordMissions2[0].id);
    expect(subMissionAfterDelete2).toBeNull();
  });
});
