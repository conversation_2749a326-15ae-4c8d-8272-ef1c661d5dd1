import { expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { RoleSO } from '@bika/domains/unit/server/role-so';
import { RecipientParser } from '../server/recipient-parser';

test('Recipient Parser to Members by To Method', async () => {
  // create user
  const { user, space, member, rootFolder } = await MockContext.initUserContext();
  const rootTeam = await space.getRootTeam();
  const subTeamSO = await rootTeam.createTeam(user.id, 'Sub Team');
  const roleSO = await RoleSO.create(user.id, space.id, {
    type: 'ROLE',
    name: 'A Role',
  });
  await roleSO.addUnit(user.id, member.id);

  const roleSO2 = await RoleSO.create(user.id, space.id, {
    type: 'ROLE',
    templateId: 'role_template_id',
    name: 'Another Role',
  });
  await roleSO2.addUnit(user.id, member.id);

  // create new user and join space
  const { user: user2 } = await MockContext.createMockUser();
  const member2 = await space.joinUser(user2.id, subTeamSO.id);
  expect(await space.getMemberCount()).toBe(2);

  // assignee parser
  const recipientParser = new RecipientParser(space.model.id);

  // test admin
  const members101 = await recipientParser.parseToMembers([
    {
      type: 'ADMIN',
    },
  ]);
  expect(members101.length).toBe(1);
  expect(members101[0].id).toBe(member.id);

  // test current operator
  const members201 = await recipientParser.parseToMembers([
    {
      type: 'CURRENT_OPERATOR',
    },
  ]);
  expect(members201.length).toBe(0);
  const members202 = await recipientParser.parseToMembers(
    [
      {
        type: 'CURRENT_OPERATOR',
      },
    ],
    { userId: user2.id },
  );
  expect(members202.length).toBe(1);
  expect(members202[0].id).toBe(member2.id);

  // test all members
  const members301 = await recipientParser.parseToMembers([
    {
      type: 'ALL_MEMBERS',
    },
  ]);
  expect(members301.length).toBe(2);

  // test specify units
  const members401 = await recipientParser.parseToMembers([
    {
      type: 'SPECIFY_UNITS',
      unitIds: [member.id],
    },
  ]);
  expect(members401.length).toBe(1);
  expect(members401[0].id).toBe(member.id);
  const members402 = await recipientParser.parseToMembers([
    {
      type: 'SPECIFY_UNITS',
      unitIds: [subTeamSO.id],
    },
  ]);
  expect(members402.length).toBe(1);
  expect(members402[0].id).toBe(member2.id);
  const members403 = await recipientParser.parseToMembers([
    {
      type: 'SPECIFY_UNITS',
      unitIds: [roleSO.id, member.id],
    },
  ]);
  expect(members403.length).toBe(1); // member exists in role, so only one member
  // test specify units with variables
  const renderProps = {
    record: {
      cells: {
        okr_creator: {
          data: member.userId,
        },
      },
    },
  };
  const members404 = await recipientParser.parseToMembers(
    [
      {
        type: 'USER',
        userId: '<%= record.cells.okr_creator.data %>',
      },
    ],
    {
      props: renderProps,
    },
  );
  expect(members404.length).toBe(1);
  expect(members404[0].id).toBe(member.id);
  // test specify units with require role
  const members405 = await recipientParser.parseToMembers([
    {
      type: 'SPECIFY_UNITS',
      unitIds: [member.id, member2.id],
      requireRoleTemplateId: 'role_template_id',
    },
  ]);
  // only member1 has role, so only member1 will be included
  expect(members405.length).toBe(1);
  expect(members405[0].id).toBe(member.id);

  // test unit role
  const members501 = await recipientParser.parseToMembers([
    {
      type: 'UNIT_ROLE',
      roleId: roleSO.id,
    },
  ]);
  expect(members501.length).toBe(1);
  const members502 = await recipientParser.parseToMembers([
    {
      type: 'UNIT_ROLE',
      roleTemplateId: 'role_template_id',
    },
  ]);
  expect(members502.length).toBe(1);

  // test unit team
  const members601 = await recipientParser.parseToMembers([
    {
      type: 'UNIT_TEAM',
      teamId: [subTeamSO.id],
    },
  ]);
  expect(members601.length).toBe(1);

  // test unit member
  const members701 = await recipientParser.parseToMembers([
    {
      type: 'UNIT_MEMBER',
      memberId: [member.id, member2.id],
    },
  ]);
  expect(members701.length).toBe(2);

  // test user
  const members801 = await recipientParser.parseToMembers([
    {
      type: 'USER',
      userId: user2.id,
    },
  ]);
  expect(members801.length).toBe(1);

  // test member field
  const templateFolderSO = await MockContext.initEmptyTemplateNodeFolder(user.id, rootFolder);
  const databaseNode = await templateFolderSO.createChildSimple(user, {
    name: 'Database for Parse Member Test',
    templateId: 'parse_member_database',
    // databaseType: 'DATUM',
    fields: [
      {
        templateId: 'created_by_field',
        name: 'Created By',
        type: 'CREATED_BY',
      },
      {
        templateId: 'member_field',
        name: 'Member Field',
        type: 'MEMBER',
        property: {
          many: true,
        },
      },
    ],
    resourceType: 'DATABASE',
  });
  const databaseSO = await databaseNode.toResourceSO<DatabaseSO>();
  await databaseSO.createRecord(user, member, {
    member_field: [subTeamSO.id],
  });
  const members901 = await recipientParser.parseToMembers(
    [
      {
        type: 'MEMBER_FIELD',
        databaseTemplateId: 'parse_member_database',
        fieldTemplateId: 'member_field', // member2 在 subTeamSO 中
      },
      {
        type: 'MEMBER_FIELD',
        databaseTemplateId: 'parse_member_database',
        fieldTemplateId: 'created_by_field', // user1(member1) 是记录创建人
      },
    ],
    { templateNodeId: templateFolderSO.id },
  );
  expect(members901.length).toBe(2);
});
