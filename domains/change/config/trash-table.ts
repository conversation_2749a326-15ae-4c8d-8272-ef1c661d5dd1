import { getServerDictionary } from '@bika/contents/i18n/server';
import { $Enums, Prisma } from '@bika/server-orm';
import {
  DatabaseField,
  FilterCondition,
  View,
  SortedField,
  // FilterMember,
  BaseSelectFieldProperty,
  Database,
} from '@bika/types/database/bo';
import { CellRenderVO, DatabaseVO, ViewFieldVO, ViewVO } from '@bika/types/database/vo';
import { i18n, iString, iStringParse, Locale } from '@bika/types/i18n/bo';
import { DateTimeFieldProperty } from '@bika/types/system/datetime';
import { formatDateTimeCellValue } from '@bika/types/system/formatting';
import { RenderOption } from '@bika/types/system/render-option';
import { isNullOrUndefined } from '../../shared/shared';

export const trashExpireDays = 30;

export type TrashModel = Prisma.TrashGetPayload<
  Prisma.TrashDefaultArgs & {
    include: {
      user: true;
    };
  }
>;

export const trashDatabaseId = (spaceId: string) => `${spaceId}_trash`;

export const getSpaceIdFromTrashDatabaseId = (databaseId: string): string => databaseId.split('_')[0];

export const getSpaceIdFromTrashDatabaseViewId = (databaseViewId: string): string => databaseViewId.split('_')[0];

export const trashDatabaseViewId = (spaceId: string) => `${trashDatabaseId(spaceId)}_table`;
type TrashTableField = DatabaseField & {
  filter?: (condition: FilterCondition) => Prisma.TrashWhereInput;
  sort?: (sort: SortedField) => Prisma.TrashOrderByWithRelationInput;
  toCellRendVO?: (field: DatabaseField, trash: TrashModel, opts?: RenderOption) => CellRenderVO;
};

export const trashTableFields = (locale?: Locale): TrashTableField[] => [
  {
    id: 'name',
    name: {
      en: 'Item',
      'zh-CN': '条目',
      ja: '項目',
      'zh-TW': '條目',
    },
    type: 'SINGLE_TEXT',
    privilege: 'READ_ONLY',
    primary: true,
    filter: (condition: FilterCondition) => {
      if (condition.fieldType !== 'SINGLE_TEXT') {
        return {};
      }
      const { operator, value } = condition.clause;
      if (isNullOrUndefined(value)) {
        return {};
      }
      switch (operator) {
        case 'Is':
          return {
            OR: [
              { name: { equals: value } },
              ...i18n.locales.map((lang) => ({ name: { path: [lang], equals: value } })),
            ],
          };
        case 'IsNot':
          return {
            OR: [
              { NOT: { name: { string_contains: value } } },
              {
                NOT: i18n.locales.map((lang) => ({ name: { path: [lang], equals: value } })),
              },
            ],
          };
        case 'Contains':
          return {
            OR: [
              { name: { string_contains: value } },
              ...i18n.locales.map((lang) => ({ name: { path: [lang], string_contains: value } })),
            ],
          };
        case 'DoesNotContain':
          return {
            OR: [
              { NOT: { name: { string_contains: value } } },
              {
                NOT: i18n.locales.map((lang) => ({ name: { path: [lang], string_contains: value } })),
              },
            ],
          };
        default:
          return {};
      }
    },
    sort: (_sort: SortedField) => {
      throw new Error('Not supported to sort by name');
    },
    toCellRendVO: (field: DatabaseField, model: TrashModel, opts?: RenderOption) => ({
      id: field.id!,
      name: iStringParse(field.name, opts?.locale),
      value: iStringParse(model.name as iString, opts?.locale),
      data: iStringParse(model.name as iString, opts?.locale),
    }),
  },
  {
    id: 'type',
    name: {
      en: 'Type',
      'zh-CN': '类型',
      ja: 'タイプ',
      'zh-TW': '類型',
    },
    type: 'SINGLE_SELECT',
    privilege: 'READ_ONLY',
    primary: false,
    property: {
      options: [
        {
          id: 'DATABASE',
          name: {
            en: 'Database',
            'zh-CN': '数据表',
            ja: 'データベース',
            'zh-TW': '數據表',
          },
        },
        {
          id: 'AUTOMATION',
          name: {
            en: 'Automation',
            'zh-CN': '自动化',
            ja: '自動化',
            'zh-TW': '自動化',
          },
        },
        {
          id: 'DOCUMENT',
          name: {
            en: 'Document',
            'zh-CN': '文档',
            ja: 'ドキュメント',
            'zh-TW': '文檔',
          },
        },
        {
          id: 'FOLDER',
          name: {
            en: 'Folder',
            'zh-CN': '文件夹',
            ja: 'フォルダ',
            'zh-TW': '文件夾',
          },
        },
        {
          id: 'FORM',
          name: {
            en: 'Form',
            'zh-CN': '表单',
            ja: 'フォーム',
            'zh-TW': '表單',
          },
        },
        {
          id: 'MIRROR',
          name: {
            en: 'Mirror',
            'zh-CN': '镜像',
            ja: 'ミラー',
            'zh-TW': '鏡像',
          },
        },
        {
          id: 'DASHBOARD',
          name: {
            en: 'Dashboard',
            'zh-CN': '仪表盘',
            ja: 'ダッシュボード',
            'zh-TW': '儀表板',
          },
        },
        {
          id: 'RECORD',
          name: {
            en: 'Record',
            'zh-CN': '记录',
            ja: 'レコード',
            'zh-TW': '記錄',
          },
        },
        {
          id: 'TRIGGER',
          name: {
            en: 'Trigger',
            'zh-CN': '触发器',
            ja: 'トリガー',
            'zh-TW': '觸發器',
          },
        },
        {
          id: 'ACTION',
          name: {
            en: 'action',
            'zh-CN': '执行器',
            ja: 'アクション',
            'zh-TW': '執行器',
          },
        },
      ],
    },
    filter: (condition: FilterCondition) => {
      if (condition.fieldType !== 'SINGLE_SELECT') {
        return {};
      }
      const { operator, value } = condition.clause;
      if (isNullOrUndefined(value)) {
        return {};
      }
      switch (operator) {
        case 'Is':
          if (value !== 'TRIGGER' && value !== 'RECORD' && value !== 'ACTION') {
            return {
              trashType: 'NODE_RESOURCE',
              resourceType: value as $Enums.NodeResourceType,
            };
          }
          return { trashType: value };
        case 'IsNot':
          if (value !== 'TRIGGER' && value !== 'RECORD' && value !== 'ACTION') {
            return { trashType: 'NODE_RESOURCE', resourceType: { not: value[0] as $Enums.NodeResourceType } };
          }
          return { trashType: { not: value as $Enums.TrashType } };
        case 'Contains': {
          const isMatch = value.some((v) => v === 'TRIGGER' || v === 'RECORD' || v === 'ACTION');
          if (isMatch) {
            return { trashType: 'NODE_RESOURCE', resourceType: { in: value as $Enums.NodeResourceType[] } };
          }
          return { trashType: { in: value as $Enums.TrashType[] } };
        }
        case 'DoesNotContain': {
          const isMatch = value.some((v) => v !== 'TRIGGER' && v !== 'RECORD' && v !== 'ACTION');
          if (isMatch) {
            return { trashType: 'NODE_RESOURCE', resourceType: { notIn: value as $Enums.NodeResourceType[] } };
          }
          return { trashType: { notIn: value as $Enums.TrashType[] } };
        }
        default:
          return {};
      }
    },
    sort: (_sort: SortedField) => {
      throw new Error('Not supported to sort by type');
    },
    toCellRendVO: (field: DatabaseField, model: TrashModel, opts?: RenderOption) => {
      const property = field.property as BaseSelectFieldProperty;
      const option = property.options.find((o) => o.id === model.resourceType || o.id === model.trashType);
      if (option && option.id) {
        return {
          id: field.id!,
          name: iStringParse(field.name, opts?.locale),
          data: [option.id],
          value: [iStringParse(option.name, opts?.locale)],
        };
      }
      return {
        id: field.id!,
        name: iStringParse(field.name, opts?.locale),
      };
    },
  },
  {
    id: 'path',
    name: {
      en: 'Orignal Path',
      'zh-CN': '原路径',
      ja: '元のパス',
      'zh-TW': '原路徑',
    },
    type: 'SINGLE_TEXT',
    privilege: 'READ_ONLY',
    primary: false,
    filter: (condition: FilterCondition) => {
      if (condition.fieldType !== 'SINGLE_TEXT') {
        return {};
      }
      const { operator, value } = condition.clause;
      if (isNullOrUndefined(value)) {
        return {
          path: {},
        };
      }
      switch (operator) {
        case 'IsEmpty':
          return {
            path: { equals: Prisma.JsonNullValueFilter.DbNull },
          };
        case 'Is':
          return {
            OR: [
              { path: { equals: value } },
              ...i18n.locales.map((lang) => ({ path: { path: ['$[*]', lang], equals: value } })),
            ],
          };
        case 'IsNot':
          return {
            OR: [
              { NOT: { path: { string_contains: value } } },
              {
                NOT: i18n.locales.map((lang) => ({ path: { path: ['$[*]', lang], equals: value } })),
              },
            ],
          };
        case 'Contains':
          return {
            OR: [
              { path: { string_contains: value } },
              ...i18n.locales.map((lang) => ({
                path: {
                  path: ['$*', lang],
                  string_contains: value,
                },
              })),
            ],
          };
        case 'DoesNotContain':
          return {
            OR: [
              { NOT: { path: { string_contains: value } } },
              {
                NOT: i18n.locales.map((lang) => ({ path: { path: ['$[*]', lang], string_contains: value } })),
              },
            ],
          };
        default:
          return {};
      }
    },
    sort: (_sort: SortedField) => {
      throw new Error('Not supported to sort by path');
    },
    toCellRendVO: (field: DatabaseField, model: TrashModel, opts?: RenderOption) => {
      const dict = getServerDictionary(opts?.locale);
      const pathList = model.unitId ? [dict.navbar.private] : [dict.navbar.team_resources];
      const path = model.path as { id: string; name: iString }[] | null;
      if (path) {
        path.reverse().forEach((p) => {
          pathList.push(iStringParse(p.name, opts?.locale));
        });
      }
      const value = pathList.join('/');
      return {
        id: field.id!,
        name: iStringParse(field.name, opts?.locale),
        data: `/${value}`,
        value: `/${value}`,
      };
    },
  },
  {
    id: 'letf_time',
    name: {
      en: 'Remaining day(s)',
      'zh-CN': '剩余时间',
      ja: '残り時間',
      'zh-TW': '剩餘時間',
    },
    type: 'NUMBER',
    privilege: 'READ_ONLY',
    primary: false,
    property: {
      symbolAlign: 'right',
      symbol: iStringParse(
        {
          en: 'day',
          'zh-CN': '天',
          ja: '日',
          'zh-TW': '天',
        },
        locale,
      ),
      precision: 0,
      commaStyle: '',
    },
    filter: (condition: FilterCondition) => {
      if (condition.fieldType !== 'NUMBER') {
        return {};
      }
      const { operator, value } = condition.clause;
      if (isNullOrUndefined(value)) {
        return {};
      }
      const expireDay = typeof value === 'string' ? Number(value) : value;
      if (expireDay > trashExpireDays) {
        throw new Error(`The expire day should be less than ${trashExpireDays}`);
      }
      const deletedTime = new Date(Date.now() - (trashExpireDays - expireDay) * 24 * 60 * 60 * 1000);
      const startOfDeleteTime = new Date(deletedTime.getFullYear(), deletedTime.getMonth(), deletedTime.getDate());
      const endOfDeleteTime = new Date(startOfDeleteTime.getTime() + 24 * 60 * 60 * 1000);
      switch (operator) {
        case 'Is': {
          return {
            createdAt: {
              gte: startOfDeleteTime,
              lt: endOfDeleteTime,
            },
          };
        }
        case 'IsGreater': {
          return {
            createdAt: {
              gt: endOfDeleteTime,
            },
          };
        }
        case 'IsGreaterEqual': {
          return {
            createdAt: {
              gte: startOfDeleteTime,
            },
          };
        }
        case 'IsLess': {
          return {
            createdAt: {
              lt: startOfDeleteTime,
            },
          };
        }
        case 'IsLessEqual': {
          return {
            createdAt: {
              lte: endOfDeleteTime,
            },
          };
        }
        default:
          return {};
      }
    },
    sort: (sort: SortedField) => {
      if (sort.asc) {
        return {
          createdAt: 'asc',
        };
      }
      return {
        createdAt: 'desc',
      };
    },
    toCellRendVO: (field: DatabaseField, model: TrashModel, opts?: RenderOption) => {
      const expireDate = new Date(model.createdAt.getTime() + trashExpireDays * 24 * 60 * 60 * 1000);
      const today = new Date();
      // 计算都从0点开始
      const expireStartOfDay = new Date(expireDate.getFullYear(), expireDate.getMonth(), expireDate.getDate());
      const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      return {
        id: field.id!,
        name: iStringParse(field.name, opts?.locale),
        data: Math.ceil((expireStartOfDay.getTime() - startOfToday.getTime()) / (24 * 60 * 60 * 1000)),
        value: Math.ceil((expireStartOfDay.getTime() - startOfToday.getTime()) / (24 * 60 * 60 * 1000)),
      };
    },
  },
  {
    id: 'delete_time',
    name: {
      en: 'Deletion date',
      'zh-CN': '删除时间',
      ja: '削除時間',
      'zh-TW': '刪除時間',
    },
    type: 'DATETIME',
    privilege: 'READ_ONLY',
    primary: false,
    property: {
      timeZone: 'AUTO',
      dateFormat: 'YYYY-MM-DD',
      timeFormat: 'HH:mm',
      includeTime: true,
    },
    sort: (sort: SortedField) => ({
      createdAt: sort.asc ? 'asc' : 'desc',
    }),
    toCellRendVO: (field: DatabaseField, model: TrashModel, opts?: RenderOption) =>
      ({
        id: field.id!,
        name: iStringParse(field.name, opts?.locale),
        data: model.createdAt.toISOString(),
        value: formatDateTimeCellValue(model.createdAt.toISOString(), field.property as DateTimeFieldProperty, opts),
      }) as CellRenderVO,
  },
  {
    id: 'delete_user',
    name: {
      en: 'Delete Member',
      'zh-CN': '删除人',
      ja: '削除ユーザー',
      'zh-TW': '刪除人',
    },
    type: 'CREATED_BY',
    privilege: 'READ_ONLY',
    primary: false,
    property: {},
    filter: (condition: FilterCondition) => {
      if (condition.fieldType !== 'CREATED_BY') {
        return {};
      }
      const { operator, value } = condition.clause;
      if (isNullOrUndefined(value)) {
        return {};
      }
      switch (operator) {
        case 'Is': {
          return {
            createdBy: value,
          };
        }
        case 'IsNot': {
          return {
            createdBy: { not: value },
          };
        }
        case 'Contains': {
          return {
            createdBy: { in: value },
          };
        }
        case 'DoesNotContain': {
          return {
            createdBy: { notIn: value },
          };
        }
        default:
          return {};
      }
    },
    toCellRendVO: (field: DatabaseField, model: TrashModel, opts?: RenderOption) => {
      const user = model.user!;
      return {
        id: field.id!,
        name: iStringParse(field.name, opts?.locale),
        data: '',
        value: {
          id: user.id,
          name: user.name,
          avatar: user.avatar,
        },
      } as CellRenderVO;
    },
  },
];

export const trashTableFieldsVO = (spaceId: string, locale?: Locale): ViewFieldVO[] =>
  trashTableFields(locale).map(
    (field) =>
      ({
        id: field.id!,
        type: field.type,
        privilege: field.privilege,
        primary: field.primary || false,
        databaseId: trashDatabaseId(spaceId),
        name: iStringParse(field.name, locale),
        property: field.property,
      }) as ViewFieldVO,
  );

const trashTableView: View = {
  name: {
    en: 'Table',
    'zh-CN': '表格',
    ja: 'テーブル',
    'zh-TW': '表格',
  },
  type: 'TABLE',
};

export const trashTableDatabase = (spaceId: string): Database => ({
  id: trashDatabaseId(spaceId),
  name: {
    en: 'Trash',
    'zh-CN': '回收站',
    ja: 'ゴミ箱',
    'zh-TW': '回收站',
  },
  description: {
    en: `Trash will display the files deleted within the last ${trashExpireDays} days. You can only view files with the permission of "Can Manage"`,
    'zh-CN': `回收站会显示最近 ${trashExpireDays} 天内删除的文件，你仅可查看具有「可以管理」权限的文件`,
    ja: `ゴミ箱には、最近${trashExpireDays}日間に削除されたファイルが表示されます。 「管理できる」権限を持つファイルのみ表示できます。`,
    'zh-TW': `回收站會顯示最近 ${trashExpireDays} 天內刪除的文件，你僅可查看具有「可以管理」權限的文件`,
  },
  databaseType: 'DATUM',
  resourceType: 'DATABASE',
  fields: trashTableFields().map(
    (field) =>
      ({
        id: field.id!,
        type: field.type,
        name: field.name,
        property: field.property,
      }) as DatabaseField,
  ),
  views: [trashTableView],
});

export const trashTableViewVO = (spaceId: string, locale?: Locale): ViewVO => ({
  id: trashDatabaseViewId(spaceId),
  name: iStringParse(trashTableView.name, locale),
  type: trashTableView.type,
  databaseId: trashDatabaseId(spaceId),
  columns: trashTableFieldsVO(spaceId, locale),
});

export const trashDatabaseVO = (spaceId: string, locale?: Locale): DatabaseVO => {
  const trashDatabase = trashTableDatabase(spaceId);
  return {
    id: trashDatabaseId(spaceId),
    spaceId,
    name: iStringParse(trashDatabase.name, locale),
    description: iStringParse(trashDatabase.description, locale),
    views: [trashTableViewVO(spaceId, locale)],
  };
};
