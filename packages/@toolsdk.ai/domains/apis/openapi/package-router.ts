import { z } from '@hono/zod-openapi';
import { Context, MiddlewareHandler } from 'hono';
import { convertToNumber } from '@bika/domains/shared/server';
import { ResponseVOBuilder } from '@bika/types/openapi/vo';
import { PackageSO } from '@toolsdk.ai/domain/server/package-so';
import { InputFieldSchema } from '@toolsdk.ai/sdk-ts/types/bo';
import {
  InputDataBody,
  InputDataBodySchema,
  PackageListRequestScope,
  PackagePaginationQuerySchema,
  PackageToolRunBodyDTO,
  PackageToolRunBodyDTOSchema,
} from '@toolsdk.ai/sdk-ts/types/dto';
import {
  ActionTemplateVOSchema,
  PackageDetailVOSchema,
  PackagePageVOSchema,
  ToolAppConfigurationTemplateVOSchema,
} from '@toolsdk.ai/sdk-ts/types/vo';
import { buildRouter, PackageKeyType, parsePackageKeyPath } from './helper';
import { ContextVariable, PackageToolKeyParamsSchema } from './types';

// query package pagination
export const queryPackagePaginationRouter = (middleware: MiddlewareHandler) =>
  buildRouter({
    middleware,
    method: 'get',
    path: '/v1/packages/pages',
    query: PackagePaginationQuerySchema.extend({
      pageNo: z.string().optional(),
      pageSize: z.string().optional(),
    }),
    data: PackagePageVOSchema,
    description: 'Query package pagination',
  });
export const queryPackagePaginationHandler = async (c: Context<{ Variables: ContextVariable }>) => {
  const { developer } = c.get('context');
  const { query, pageNo, pageSize, scope } = c.req.query();
  const data = await PackageSO.search({
    query,
    pagination: {
      pageNo: convertToNumber(pageNo),
      pageSize: convertToNumber(pageSize),
    },
    developerId: developer.id,
    scope: scope as PackageListRequestScope,
  });
  return c.json(ResponseVOBuilder.success(data), 200);
};

// get package detail
export const getPackageRouter = (middleware: MiddlewareHandler, keyType: PackageKeyType) => {
  const { path, params } = parsePackageKeyPath('', keyType);
  return buildRouter({
    middleware,
    method: 'get',
    path,
    params,
    data: PackageDetailVOSchema,
    description: 'Get package detail info',
  });
};
export const getPackageHandler = async (c: Context<{ Variables: ContextVariable }>) => {
  const { developer } = c.get('context');
  const { packageKey, packageVersion, packageKeyGroup, packageKeyName } = c.req.param();
  const fullKey = packageKeyGroup ? `${packageKeyGroup}/${packageKeyName}` : packageKey;

  const pkg = await PackageSO.getByKey(fullKey, packageVersion);
  // check private package
  if (pkg.visibility === 'PRIVATE' && pkg.createdBy !== developer.id) {
    throw new Error('Unauthorized');
  }
  const data = await pkg.toDetailVO();
  return c.json(ResponseVOBuilder.success(data), 200);
};

// get package configuration
export const getPackageConfigurationRouter = (middleware: MiddlewareHandler, keyType?: PackageKeyType) => {
  const { path, params } = parsePackageKeyPath('/configuration', keyType);
  return buildRouter({
    middleware,
    method: 'get',
    path,
    params,
    data: ToolAppConfigurationTemplateVOSchema.optional(), // response,
    description: 'Get package configuration',
  });
};
export const getPackageConfigurationHandler = async (c: Context<{ Variables: ContextVariable }>) => {
  const { developer } = c.get('context');
  const { packageKey, packageVersion } = c.req.param();
  const pkg = await PackageSO.getByKey(packageKey, packageVersion);

  // check private package
  if (pkg.visibility === 'PRIVATE' && pkg.createdBy !== developer.id) {
    throw new Error('Unauthorized');
  }

  const configuration = await pkg.getConfiguration();
  return c.json(ResponseVOBuilder.success(configuration?.toVO()), 200);
};

// get package tools
export const getPackageToolsRouter = (middleware: MiddlewareHandler, keyType: PackageKeyType) => {
  const { path, params } = parsePackageKeyPath('/tools', keyType);
  return buildRouter({
    middleware,
    method: 'get',
    path,
    params,
    data: z.array(ActionTemplateVOSchema), // response,
    description: 'Get package tools',
  });
};
export const getPackageToolsHandler = async (c: Context<{ Variables: ContextVariable }>) => {
  const { developer } = c.get('context');
  const { packageKey, packageVersion, packageKeyName, packageKeyGroup } = c.req.param();
  const realPackageKey = packageKeyGroup ? `${packageKeyGroup}/${packageKeyName}` : packageKey;
  const pkg = await PackageSO.getByKey(realPackageKey, packageVersion);
  // check private package
  if (pkg.visibility === 'PRIVATE' && pkg.createdBy !== developer.id) {
    throw new Error('Unauthorized');
  }
  const tools = await pkg.listTools();
  const data = await Promise.all(tools.map((tool) => tool.toVO()));
  return c.json(ResponseVOBuilder.success(data), 200);
};

// run package tool dynamic fields
export const runPackageToolDynamicFieldsRouter = (middleware: MiddlewareHandler, keyType?: PackageKeyType) => {
  const { path, params } = parsePackageKeyPath(
    '/tools/{toolKey}/runDynamicFields',
    keyType,
    PackageToolKeyParamsSchema,
  );
  return buildRouter({
    middleware,
    method: 'post',
    path,
    params,
    bodySchema: InputDataBodySchema,
    data: z.array(InputFieldSchema),
    description: 'Run package tool dynamic fields',
  });
};
export const runPackageToolDynamicFieldsHandler = async (c: Context<{ Variables: ContextVariable }>) => {
  const { developer } = c.get('context');
  const { packageKey, packageVersion, toolKey } = c.req.param();
  const pkg = await PackageSO.getByKey(packageKey, packageVersion);
  // check private package
  if (pkg.visibility === 'PRIVATE' && pkg.createdBy !== developer.id) {
    throw new Error('Unauthorized');
  }
  const body = await c.req.json<InputDataBody>();
  const data = await pkg.runToolDynamicFields(toolKey, body.inputData);
  return c.json(ResponseVOBuilder.success(data), 200);
};

// run package tool
export const runPackageToolRouter = (middleware: MiddlewareHandler, keyType?: PackageKeyType) => {
  const { path, params } = parsePackageKeyPath('/runTool', keyType);
  return buildRouter({
    middleware,
    method: 'post',
    path,
    params,
    bodySchema: PackageToolRunBodyDTOSchema,
    data: z.unknown(), // response,
    description: 'Run package tool',
  });
};
export const runPackageToolHandler = async (c: Context<{ Variables: ContextVariable }>) => {
  const { developer, externalId } = c.get('context');
  const { packageKey, packageVersion } = c.req.param();
  const pkg = await PackageSO.getByKey(packageKey, packageVersion);
  // check private package
  if (pkg.visibility === 'PRIVATE' && pkg.createdBy !== developer.id) {
    throw new Error('Unauthorized');
  }
  const body = await c.req.json<PackageToolRunBodyDTO>();
  const data = await pkg.runTool(body, { externalId });
  return c.json(ResponseVOBuilder.success(data), 200);
};

export const listMyPackagesRouter = (middleware: MiddlewareHandler) =>
  buildRouter({
    middleware,
    method: 'get',
    path: '/v1/my/packages',
    // query: CommonSearchDTOSchema,
    //   params: z.object({
    //     packageKey: z.string().openapi({ example: 'Package Key' }),
    //   }),
    //   bodySchema: PackageInstanceCreateBodySchema,
    data: z.array(PackagePageVOSchema), // response,
    description: 'Get my package',
  });

export const listMyPackagesHandler = async (c: Context<{ Variables: ContextVariable }>) => {
  const {
    developer,
    // account, externalId
  } = c.get('context');
  // const body = await c.req.json<PackageInstaneCreateBody>();
  //   const { developerId } = c.req.param();
  // await PackageSO.search(())
  const pages = await PackageSO.listByDeveloper(developer.id);
  return c.json(ResponseVOBuilder.success(pages), 200);
};
