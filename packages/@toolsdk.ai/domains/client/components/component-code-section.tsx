import { useState } from 'react';
import { useFormappLocale } from '@bika/contents/i18n';
import { Box, Stack, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { Markdown } from '@bika/ui/markdown';

// 样式化的标签按钮
const StyledTabButton = styled('button')<{ active: boolean }>(({ active }) => ({
  padding: '12px 24px',
  backgroundColor: active ? 'linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%)' : 'rgba(255, 255, 255, 0.05)',
  color: active ? 'white' : 'rgba(255, 255, 255, 0.8)',
  border: active ? 'none' : '1px solid rgba(255, 255, 255, 0.1)',
  borderRadius: '12px',
  cursor: 'pointer',
  fontSize: '14px',
  fontWeight: 600,
  transition: 'all 0.3s ease',
  backdropFilter: 'blur(10px)',
  position: 'relative',
  overflow: 'hidden',

  '&:hover': {
    backgroundColor: active ? 'linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%)' : 'rgba(255, 255, 255, 0.1)',
    transform: 'translateY(-2px)',
    boxShadow: active ? '0 8px 32px rgba(139, 92, 246, 0.4)' : '0 4px 16px rgba(0, 0, 0, 0.2)',
  },

  '&:active': {
    transform: 'translateY(0)',
  },

  // 渐变背景
  background: active ? 'linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%)' : 'rgba(255, 255, 255, 0.05)',
}));

// 主容器样式
const StyledMainContainer = styled(Box)(() => ({
  width: '100%',
  maxWidth: '900px',
  margin: '24px auto',
  padding: '24px 32px',
  backgroundColor: 'rgba(255, 255, 255, 0.02)',
  borderRadius: '24px',
  border: '1px solid rgba(255, 255, 255, 0.08)',
  backdropFilter: 'blur(20px)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
  position: 'relative',

  // 添加微妙的渐变背景
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%)',
    borderRadius: '24px',
    zIndex: -1,
  },
}));

function CodeExampleSection() {
  const [activeTab, setActiveTab] = useState<'AISDK' | 'OPENAI'>('OPENAI');
  const { t } = useFormappLocale();

  const CODE_EXAMPLES = {
    AISDK: `
\`\`\`typescript
import { generateText } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { ToolSDKApiClient } from 'toolsdk/api';

// Initialize Client
const toolSDK = new ToolSDKApiClient({ apiKey: process.env.TOOLSDK_AI_API_KEY });
const openai  = createOpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Get Tools
const searchMCP = await toolSDK.package('@toolsdk.ai/tavily-mcp', {
  TAVILY_API_KEY: process.env.TAVILY_API_KEY,
});
const emailMCP  = await toolSDK.package('@toolsdk.ai/mcp-send-email', {
  RESEND_API_KEY: process.env.RESEND_API_KEY,
});
const searchTool = await searchMCP.getAISDKTool('tavily-search');
const emailTool  = await emailMCP.getAISDKTool('send-email');

// Generate Result with Tools
const completion = await generateText({
  model: openai('gpt-4'),
  messages: [{
    role: 'user',
    content: 'Help me search for the latest AI news and send <NAME_EMAIL>',
  }],
  tools: { searchTool, emailTool },
});

console.log(completion);
\`\`\`
  `.trim(),

    OPENAI: `
\`\`\`typescript
import OpenAI from 'openai';
import { ToolSDKApiClient } from 'toolsdk/api';

// Initialize Client
const toolSDK = new ToolSDKApiClient({ apiKey: process.env.TOOLSDK_AI_API_KEY });
const openai  = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Get Tools
const searchMCP = await toolSDK.package('@toolsdk.ai/tavily-mcp', {
  TAVILY_API_KEY: process.env.TAVILY_API_KEY,
});
const emailMCP  = await toolSDK.package('@toolsdk.ai/mcp-send-email', {
  RESEND_API_KEY: process.env.RESEND_API_KEY,
});
const searchTool = await searchMCP.getOpenAISDKTool('tavily-search');
const emailTool  = await emailMCP.getOpenAISDKTool('send-email');

const messages = [{
  role: 'user',
  content: 'Help me search for the latest AI news and send <NAME_EMAIL>',
}];

const completion = await openai.chat.completions.create({
  model: 'gpt-4',
  messages,
  tools: [searchTool, emailTool],
});

const toolMap = { 'tavily-search': searchMCP, 'send-email': emailMCP };

// Execute Tool Calls
for (const toolCall of completion.choices[0].message.tool_calls) {
  const { name: toolKey, arguments: argsStr } = toolCall.function;
  const inputData = JSON.parse(argsStr);

  const toolContent = await toolMap[toolKey].run({ toolKey, inputData });

  messages.push(
    { role: 'assistant', tool_calls: [toolCall] },
    { role: 'tool', content: JSON.stringify(toolContent), tool_call_id: toolCall.id },
  );
}

const finalResponse = await openai.chat.completions.create({
  model: 'gpt-4',
  messages,
});

console.log(finalResponse);
\`\`\`
  `.trim(),
  };

  return (
    <StyledMainContainer>
      <Typography
        variant="h3"
        sx={{
          fontSize: '32px',
          fontWeight: 700,
          textAlign: 'center',
          mt: 0,
          mb: 2,
          background: 'linear-gradient(135deg, #FFFFFF 0%, #E2E8F0 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
        }}
      >
        Quick Starting
      </Typography>

      {/* 标签切换 */}
      <Stack
        direction="row"
        spacing={2}
        sx={{
          display: 'flex',
          justifyContent: 'center',
          width: '100%',
          mb: 2,
        }}
      >
        <StyledTabButton active={activeTab === 'OPENAI'} onClick={() => setActiveTab('OPENAI')}>
          OpenAI SDK
        </StyledTabButton>
        <StyledTabButton active={activeTab === 'AISDK'} onClick={() => setActiveTab('AISDK')}>
          AI SDK
        </StyledTabButton>
      </Stack>

      <Box sx={{ fontSize: '16px' }}>
        <Markdown markdown={CODE_EXAMPLES[activeTab]} />
      </Box>

      {/* 底部提示 */}
      <Typography
        variant="body2"
        sx={{
          textAlign: 'center',
          mt: 0,
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: '12px',
        }}
      >
        {t.toolsdk.component.codeSection.description}
      </Typography>
    </StyledMainContainer>
  );
}

export default CodeExampleSection;
