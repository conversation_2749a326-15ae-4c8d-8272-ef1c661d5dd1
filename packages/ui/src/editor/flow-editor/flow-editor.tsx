'use client';

import type { ColorMode, NodeTypes } from '@xyflow/react';
import { ReactFlowProvider, ReactFlow, Background } from '@xyflow/react';
import { useState, useEffect, CSSProperties } from 'react';
import { createPortal } from 'react-dom';
import { NodeResourceType } from '@bika/types/node/bo';
import type { EditorScreenProps } from '@bika/types/template/type';
import type { OrgChartUnit } from '@bika/ui/editor/flow-editor/nodes/unit';
import { Stack } from '@bika/ui/layouts';
import { FlowControl } from './control';
import { AIAgentNode } from './nodes/ai-agent';
import { AutomationNode } from './nodes/automation';
import { DashboardNode } from './nodes/dashboard';
import { DatabaseNode } from './nodes/database';
import { FolderNode } from './nodes/folder';
import { FormNode } from './nodes/form';
import { IntegrationNode } from './nodes/integration';
import { MirrorNode } from './nodes/mirror';
import { MissionNode } from './nodes/mission';
import { ReportNode } from './nodes/report';
import { UnitNode } from './nodes/unit';
import { TeamControl } from './team-control';
import { useFlowData, type IFlowEditorData } from './use-flow-data';

import '@xyflow/react/dist/style.css';
import './index.css';

type NodeTypesValue = NodeTypes[string];

export type FlowNodeTypeDef = NodeResourceType | 'INTEGRATION' | 'MISSION' | 'REPORT' | 'UNIT';
export type FlowNodeMap = Record<FlowNodeTypeDef, NodeTypesValue>;

interface Props {
  data: IFlowEditorData;
  theme?: string;
  /** 显示一些数据 给截图和浏览器用 */
  showInfo?: boolean;
  showControl?: boolean;
  readonly?: boolean;
  // 控制工具栏，是否添加打开新窗按钮？需要的话放个URL
  openNewWindowButtonAndUrl?: string;
  onClick?: (data: EditorScreenProps) => void | ((data: OrgChartUnit) => void);
  showAnimationSpeed?: number;
  fitView?: boolean;
  backgroundStyle?: CSSProperties;
  getMoreUnits?: (unit: OrgChartUnit) => void;

  showTeamControl?: boolean;
  viewMode?: 'all' | 'member' | 'ai';
  setViewMode?: (mode: 'all' | 'member' | 'ai') => void;
}

/**
 * Template Resources 工作流程编辑器
 *
 * @param props
 * @returns
 */
export function FlowEditor(props: Props) {
  const { data } = props;

  const { nodes, edges, info } = useFlowData({
    data,
    delay: props.showAnimationSpeed || 0,
    onClick: props.onClick,
    getMoreUnits: props.getMoreUnits,
  });
  const [isFullScreen, setIsFullScreen] = useState(false);

  useEffect(() => {
    if (props.showInfo && info) {
      // 创建两个隐藏的input 把X和Y记录在div里面
      const inputX = document.createElement('input');
      inputX.type = 'hidden';
      inputX.value = info.maxX.toString();
      inputX.id = 'graph-editor-x';
      document.body.appendChild(inputX);
      const inputY = document.createElement('input');
      inputY.type = 'hidden';
      inputY.value = info.maxY.toString();
      inputY.id = 'graph-editor-y';
      document.body.appendChild(inputY);
    }
    return () => {
      // 查找这两个ID 删除他们
      const inputX = document.getElementById('graph-editor-x');
      if (inputX) {
        inputX.remove();
      }
      const inputY = document.getElementById('graph-editor-y');
      if (inputY) {
        inputY.remove();
      }
    };
  }, [info, props.showInfo]);

  const nodeTypes: FlowNodeMap = {
    AUTOMATION: AutomationNode,
    DATABASE: DatabaseNode,
    FOLDER: FolderNode,
    MIRROR: MirrorNode,
    FORM: FormNode,
    // VIEW: ViewNode,
    INTEGRATION: IntegrationNode,
    MISSION: MissionNode,
    REPORT: ReportNode,
    UNIT: UnitNode,
    DASHBOARD: DashboardNode,
    AI: AIAgentNode,
    // below types TODO:
    ROOT: DatabaseNode,
    TEMPLATE: DatabaseNode,
    PAGE: DatabaseNode,
    EMBED: DatabaseNode,
    DATAPAGE: DatabaseNode,
    CANVAS: DatabaseNode,
    ALIAS: DatabaseNode,
    DOCUMENT: DatabaseNode,
    FILE: DatabaseNode,
    REPORT_TEMPLATE: DatabaseNode,
  };
  const content = (
    <ReactFlowProvider>
      <Stack
        sx={
          isFullScreen
            ? {
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: 99999,
                flex: 1,
                backgroundColor: 'var(--bg-surface)',
              }
            : {
                flex: 1,
                height: '100%',
              }
        }
      >
        <ReactFlow
          style={{ flex: 1, height: '100%', width: '100%' }}
          edgesFocusable={false}
          edgesReconnectable={false}
          colorMode={props.theme as ColorMode}
          proOptions={{ hideAttribution: true }}
          nodes={nodes}
          edges={edges}
          fitView={props.fitView}
          nodesDraggable={false}
          // 禁止缩放
          // zoomOnScroll={!props.readonly}
          // 隐藏那个锁按钮
          nodeTypes={nodeTypes}
        >
          <Background style={props.backgroundStyle} />
          {props.showControl && (
            <FlowControl
              openNewWindowButtonAndUrl={props.openNewWindowButtonAndUrl}
              isFullScreen={isFullScreen}
              setIsFullScreen={setIsFullScreen}
            />
          )}
          {props.showTeamControl && <TeamControl viewMode={props.viewMode} setViewMode={props.setViewMode} />}
        </ReactFlow>
      </Stack>
    </ReactFlowProvider>
  );
  if (typeof window !== 'undefined') {
    const body = document.querySelector('body') as HTMLBodyElement;
    return isFullScreen ? createPortal(content, body) : content;
  }
  return content;
}
