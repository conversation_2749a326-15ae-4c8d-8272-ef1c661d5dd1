import assert from 'assert';
import dagre from '@dagrejs/dagre';
import type { Node, Edge } from '@xyflow/react';
import { useMemo, useState, useEffect } from 'react';
import { getActionTypesConfig } from '@bika/contents/config/client/automation/actions';
import { useLocale, type ILocaleContext } from '@bika/contents/i18n/context';
import { AiNodeBO } from '@bika/types/ai/bo';
import type { BaseCreateMissionAction, SendReportAction } from '@bika/types/automation/bo';
import { AutomationSchema } from '@bika/types/automation/bo';
import { DashboardSchema } from '@bika/types/dashboard/bo';
import { ViewSchema, DatabaseSchema } from '@bika/types/database/bo';
import { MirrorBOSchema } from '@bika/types/node/bo';
import type { NodeResource } from '@bika/types/node/bo';
import { iStringParse } from '@bika/types/system';
import type { EditorScreenProps } from '@bika/types/template/type';
import type { OrgChartUnit } from '@bika/ui/editor/flow-editor/nodes/unit';
// const rootData = useCallback(async () => {
//   const data: TeamSubListPageVO = await trpc.team.subList.query({

export type IFlowEditorData =
  | {
      type: 'node-resources';
      resources: NodeResource[];
    }
  | {
      type: 'units';
      units: OrgChartUnit[];
      currentTeam?: OrgChartUnit;
    };

interface Props {
  data: IFlowEditorData;
  delay: number; // 默认10ms，大于0则有动画

  onClick?: (data: EditorScreenProps) => void | ((data: OrgChartUnit) => void);
  getMoreUnits?: (unit: OrgChartUnit) => void;
}
type FlowEditorResult = { nodes: Node[]; edges: Edge[]; info: { maxY: number; maxX: number } };
// newEdges,
// {
//   maxY: Math.max(automationY, databaseY, viewY) + 50,
//   maxX: x - 56 + (integration.length || mission.length || report.length ? 400 : 0),
// },

function parseFlowEditorData(data: IFlowEditorData, localeCtx: ILocaleContext, props: Props): FlowEditorResult {
  const { t, lang: locale } = localeCtx;
  const newNodes: Node[] = [];

  const newEdges: Edge[] = [];
  const integration: { type: string }[] = [];
  // 任务
  const mission: BaseCreateMissionAction[] = [];
  // 报告
  const report: SendReportAction[] = [];

  let folderY = 0;
  let automationY = 0;
  let databaseY = 0;
  let viewY = 0;
  let x = 0;
  let y = 0;
  //
  const nodeSpacing = 16;
  const nodeWidth = 144;
  const nodeYSpacing = 300; // 每个节点的Y轴间距

  if (data.type === 'node-resources') {
    const resources: NodeResource[] = data.resources;

    // FOLDER
    for (let index = 0; index < resources.length; index++) {
      const resource = resources[index];
      if (resource.resourceType === 'FOLDER') {
        const rd = resource;
        const folderId = `folder_${resource.id || resource.templateId}`;
        newNodes.push({
          id: folderId,
          position: { x, y: folderY },
          type: 'FOLDER',
          data: { ...rd, onClick: props.onClick },
        });
        folderY += 120;
      }
    }
    if (resources.some((resource) => resource.resourceType === 'FOLDER')) {
      x += 400;
    }

    // AI Agents
    for (const resource of resources) {
      if (resource.resourceType === 'AI') {
        const baseHeight = 125;
        const aiResource = resource as AiNodeBO;
        assert(resource.resourceType === 'AI', 'Here Resource type must be AI');
        const agentId = `ai_agent_${resource.id || resource.templateId}`;
        newNodes.push({
          id: agentId,
          position: { x, y: automationY },
          type: 'AI',
          data: aiResource,
        });
        let spacingY = 0;
        for (const skillset of aiResource.skillsets || []) {
          spacingY += 90;
          if ((skillset.kind === 'preset' || skillset.kind === 'toolsdk') && skillset.includes) {
            if (skillset.includes?.length && skillset.includes.length > 1) {
              spacingY += 38 * (skillset.includes.length - 1);
            }
          }
        }

        automationY += baseHeight + spacingY;
      }
    }

    // Dashboards
    for (const resource of resources) {
      if (resource.resourceType === 'DASHBOARD') {
        newNodes.push({
          id: `dashboard_${resource.id || resource.templateId}`,
          position: { x, y: automationY },
          type: 'DASHBOARD',
          data: resource,
        });
        const widgets = DashboardSchema.safeParse(resource);
        const widgetCount = widgets.success ? widgets.data.widgets.length : 0;
        automationY += nodeSpacing + 90 + widgetCount * 48; // 90 for header + 48 for each widget
      }
    }
    // Automation
    for (let index = 0; index < resources.length; index++) {
      const resource = resources[index];
      if (resource.resourceType === 'AUTOMATION') {
        const rd = AutomationSchema.parse(resource);
        const automationId = `automation_${resource.id || resource.templateId}`;
        newNodes.push({
          id: automationId,
          position: { x, y: automationY },
          type: 'AUTOMATION',
          data: { ...rd, onClick: props.onClick },
        });
        automationY += 300 + rd.triggers.length * 76 - 24 + rd.actions.length * 76 - 24;
        for (let idx = 0; idx < rd.actions.length; idx++) {
          const action = rd.actions[idx];
          const id = action.templateId || `automation_${resource.templateId}_action_${idx}`;
          const sourceHandle = `automation_${resource.id || resource.templateId}_action_${idx}`;
          const actionsConfig = getActionTypesConfig(localeCtx);
          const actionConfig = actionsConfig[action.actionType];
          for (const inteType of actionConfig.integrations || []) {
            integration.push({ type: inteType });
            const integrationId = `integration_${inteType}`;
            newEdges.push({
              label: t.editor.integration_line_text,
              source: automationId,
              target: 'integration',
              id: `${id}_integration_${action.templateId}`,
              sourceHandle,
              targetHandle: integrationId,
              animated: true,
              labelShowBg: false,
              focusable: false,
              labelStyle: {
                fill: 'var(--text-primary)',
              },
              // 虚线
              // type: 'smoothstep',
              zIndex: 1,
            });
          }
          if (action.actionType === 'CREATE_RECORD') {
            const databaseId = `database_${action.input.databaseId || action.input.databaseTemplateId}`;
            if (databaseId) {
              newEdges.push({
                label: t.editor.create_record_line_text,
                source: automationId,
                target: databaseId,
                id: `${id}_create_record_${databaseId}`,
                sourceHandle,
                labelShowBg: false,
                focusable: false,
                labelStyle: {
                  fill: 'var(--text-primary)',
                },
                // 直线
                // type: 'smoothstep',
                zIndex: 1,
              });
            }
          } else if (action.actionType === 'FIND_RECORDS') {
            const databaseId = `database_${action.input.databaseId || action.input.databaseTemplateId}`;
            if (databaseId) {
              newEdges.push({
                label: t.editor.find_records_line_text,
                source: automationId,
                target: databaseId,
                id: `${id}_find_record_${databaseId}`,
                sourceHandle,
                labelShowBg: false,
                focusable: false,
                labelStyle: {
                  fill: 'var(--text-primary)',
                },
                // 直线
                // type: 'smoothstep',
                zIndex: 1,
              });
            }
            // } else if (Object.keys(INTEGRATION_LIST).includes(action.actionType)) {
          } else if (action.actionType === 'CREATE_MISSION') {
            mission.push(action);
            const missionId = `mission_${action.templateId}`;
            newEdges.push({
              label: t.editor.create_mission_line_text,
              source: automationId,
              target: 'mission',
              id: `${id}_mission_${action.templateId}`,
              sourceHandle: `automation_${resource.templateId}_action_${idx}`,
              targetHandle: missionId,
              animated: true,
              labelShowBg: false,
              focusable: false,
              labelStyle: {
                fill: 'var(--text-primary)',
              },
              // 虚线
              // type: 'smoothstep',
              zIndex: 1,
            });
          } else if (action.actionType === 'SEND_REPORT') {
            report.push(action);
            const reportId = `report_${action.templateId}`;
            newEdges.push({
              label: t.automation.action.send_report.name,
              source: automationId,
              target: 'report',
              id: `${id}_report_${action.templateId}`,
              sourceHandle: `automation_${resource.templateId}_action_${idx}`,
              targetHandle: reportId,
              animated: true,
              labelShowBg: false,
              focusable: false,
              labelStyle: {
                fill: 'var(--text-primary)',
              },
              // 虚线
              // type: 'smoothstep',
              zIndex: 1,
            });
          }
        }
      }
    }

    if (resources.some((resource) => resource.resourceType === 'AUTOMATION')) {
      x += 400;
    }

    // Database
    for (let index = 0; index < resources.length; index++) {
      const resource = resources[index];
      if (resource.resourceType === 'DATABASE') {
        const parsedData = DatabaseSchema.parse(resource);
        const databaseId = `database_${resource.id || resource.templateId}`;
        newNodes.push({
          id: databaseId,
          position: { x, y: databaseY },
          type: 'DATABASE',
          data: { ...parsedData, onClick: props.onClick },
        });
        databaseY += 200 + (parsedData.fields || []).length * 42 - 8;
        if (parsedData.views) {
          for (let vid = 0; vid < parsedData.views.length; vid++) {
            const view = parsedData.views[vid];
            const v = ViewSchema.parse(view);
            if (v.filters?.conds && v.filters.conds.length) {
              const fields = parsedData.fields;
              const viewId = view.id || `${index}_view_${vid}`;
              // push edge
              newEdges.push({
                id: `${databaseId}_${viewId}`,
                source: databaseId,
                target: viewId,
                labelShowBg: false,
                focusable: false,
                labelStyle: {
                  fill: 'var(--text-primary)',
                },
                type: 'smoothstep',
                zIndex: 10,
              });
              newNodes.push({
                id: viewId,
                position: { x: 800, y: viewY },
                type: 'VIEW',
                data: {
                  ...v,
                  fields,
                  name: `${iStringParse(parsedData.name, locale)} - ${iStringParse(v.name, locale)}`,
                  onClick: props.onClick,
                  databaseId: resource.templateId,
                },
              });
              viewY += 150 + (v.filters?.conds || []).length * 42 - 8;
            }
          }
        }
      } else if (resource.resourceType === 'MIRROR') {
        const parsedData = MirrorBOSchema.parse(resource);
        const databaseId = `mirror_${resource.id || resource.templateId}`;
        const viewRef = resources.find((r) => {
          // @ts-expect-error ignore
          if (r.resourceType === 'DATABASE' && r.id === parsedData.databaseId) {
            const dat = DatabaseSchema.parse(r);
            // @ts-expect-error ignore
            if (parsedData.viewId) {
              // @ts-expect-error ignore
              return (dat.views || []).find((v) => v.id === parsedData.viewId);
            }
            return true;
          }
          return false;
        });
        if (viewRef) {
          newNodes.push({
            id: databaseId,
            position: { x, y: databaseY },
            type: 'MIRROR',
            // @ts-expect-error ignore
            data: { ...parsedData, onClick: props.onClick, fields: viewRef?.fields },
          });
          // @ts-expect-error ignore
          databaseY += 200 + (viewRef?.fields || []).length * 42 - 8;
        }
      } else if (resource.resourceType === 'FORM') {
        const data = resource;
        const databaseId = `form_${resource.id || resource.templateId}`;
        // @ts-ignore
        const databaseRef = resources.find((r) => r.resourceType === 'DATABASE' && r.id === data.databaseId);
        if (databaseRef) {
          newNodes.push({
            id: databaseId,
            position: { x, y: databaseY },
            type: 'FORM',
            // @ts-expect-error ignore
            data: { ...data, onClick: props.onClick, fields: databaseRef.fields },
          });
          // @ts-expect-error ignore
          databaseY += 200 + (databaseRef.fields || []).length * 42 - 8;
        }
      }
    }

    if (resources.some((resource) => resource.resourceType === 'DATABASE')) {
      x += 400;
    }
    // Integrations
    if (integration.length > 0) {
      // integration
      newNodes.push({
        id: 'integration',
        type: 'INTEGRATION',
        // @ts-expect-error ignore
        data: integration,
        position: { x, y: viewY },
      });
      viewY += 150 + integration.length * 42 - 8;
    }

    // Missions
    if (mission.length > 0) {
      // mission
      newNodes.push({
        id: 'mission',
        type: 'MISSION',
        // @ts-expect-error ignore
        data: mission,
        position: { x, y: viewY },
      });
      viewY += 150 + mission.length * 42 - 8;
    }

    // Reports
    if (report.length > 0) {
      newNodes.push({
        id: 'report',
        type: 'REPORT',
        // @ts-expect-error ignore
        data: report,
        position: { x, y: viewY },
      });
      viewY += 150 + report.length * 42 - 8;
    }
    console.log('newNodes', newNodes, newEdges);

    return {
      nodes: newNodes,
      edges: newEdges,
      info: {
        maxY: Math.max(automationY, databaseY, viewY) + 50,
        maxX: x - 56 + (integration.length || mission.length || report.length ? 400 : 0),
      },
    };
  }

  function setOrgChartNodes(parentId: string, unitsData: OrgChartUnit[], nodes: Node[], edges: Edge[]) {
    unitsData.forEach((unit) => {
      const nodeId = `${parentId}_${unit.id}`;

      nodes.push({
        id: nodeId,
        type: 'UNIT',
        position: { x: 0, y: 0 },
        data: {
          ...unit,
          label: unit.name,
          parentId: unit.id,
          onClick: props.onClick,
          getMoreUnits: props.getMoreUnits,
        },
      });
      edges.push({
        id: `edge_${nodeId}`,
        source: parentId,
        target: nodeId,
        hidden: unit.disabled,
        type: 'smoothstep',
        style: {
          stroke: 'var(--borderBrandDefault)',
        },
      });
      if (unit.type === 'Team' && unit.children?.length && unit.isExpanded) {
        setOrgChartNodes(nodeId, unit.children, nodes, edges);
      }
    });
  }

  if (data.type === 'units') {
    if (!data.currentTeam) {
      return {
        nodes: [],
        edges: [],
        info: { maxY: 0, maxX: 0 },
      };
    }

    const unitsData: OrgChartUnit[] = data.units;
    y = 40;
    const currentTeam = data.currentTeam;

    const memberCount = unitsData.reduce((count, unit) => {
      if (unit.type !== 'Team') {
        return count + 1;
      }
      return count;
    }, 0);
    newNodes.push({
      id: currentTeam.id,
      type: 'UNIT',
      position: { x, y },
      data: {
        ...currentTeam,
        label: currentTeam.name,
        memberCount,
      },
    });

    if (unitsData.length > 0) {
      setOrgChartNodes(currentTeam.id, unitsData, newNodes, newEdges);
    }

    const graph = new dagre.graphlib.Graph().setDefaultEdgeLabel(() => ({}));
    const autoLayout = (nodes: Node[], edges: Edge[]) => {
      graph.setGraph({ nodesep: nodeSpacing, ranksep: 0 });
      nodes.forEach((node) => {
        graph.setNode(node.id, { width: nodeWidth, height: nodeYSpacing });
      });
      edges.forEach((edge) => {
        graph.setEdge(edge.source, edge.target, { height: 20 });
      });
      dagre.layout(graph);

      const layoutNodes = nodes.map((node) => {
        const nodeWithPosition = graph.node(node.id);
        const newNode = {
          ...node,
          position: {
            x: nodeWithPosition.x - nodeWidth / 2,
            y: nodeWithPosition.y - nodeYSpacing / 2,
          },
        };
        return newNode;
      });
      return { nodes: layoutNodes, edges };
    };

    const { nodes: layoutNodes, edges: layoutEdges } = autoLayout(newNodes, newEdges);

    return {
      nodes: layoutNodes,
      edges: layoutEdges,
      info: {
        maxY: 0,
        maxX: 0,
      },
    };
  }

  return {
    nodes: newNodes,
    edges: newEdges,
    info: {
      maxY: Math.max(automationY, databaseY, viewY) + 50,
      maxX: x - 56 + (integration.length || mission.length || report.length ? 400 : 0),
    },
  };
}

/**
 * 获取Resource的内容，动态地，每100ms递加，转换成ReactFlow的数据结构
 */
export function useFlowData(props: Props) {
  const { data, delay } = props;

  const [aniNodes, setAniNodes] = useState<Node[]>([]);
  const [timeoutId, setTimeoutId] = useState<ReturnType<typeof setInterval> | null>(null);
  const localeCtx = useLocale();
  const { t } = localeCtx;
  const locale = localeCtx.lang;

  // 获取真实的所有数据
  const { nodes, edges, info } = useMemo(() => parseFlowEditorData(data, localeCtx, props), [data]);

  useEffect(() => {
    setAniNodes([]);
    if (timeoutId) {
      clearInterval(timeoutId);
    }
    if (!nodes || nodes.length === 0) return;

    if (props.delay <= 0) {
      setAniNodes(nodes);
      return;
    }

    const id = setInterval(() => {
      setAniNodes((prev) => {
        if (prev.length === nodes.length) {
          clearInterval(id);
          return prev;
        }
        const theNode = nodes[prev.length];
        return [...prev, theNode];
      });
    }, props.delay);

    setTimeoutId(id);
    // eslint-disable-next-line consistent-return
    return () => {
      if (timeoutId) {
        clearInterval(timeoutId);
      }
    };
  }, [nodes]);
  // 每100ms递加，动态地从真实数据里抽取数据，暴露到外面的React Flow变成动画
  return { nodes: aniNodes, edges, info };
}
