'use client';

import { useLocale } from '@bika/contents/i18n';
import type { AiNodeBO } from '@bika/types/ai/bo';
import { useNodeResourceApiContext } from '@bika/types/node/context';
import { SkillsetsSelectBOInput } from '@bika/ui/ai/type-form/skillsets-select-bo-input';
import { useUIFrameworkContext } from '@bika/ui/framework';
import { Stack } from '@bika/ui/layouts';
import { NodeHeader } from '../header';
import { NodePanel } from '../node-panel';

interface Props {
  data: AiNodeBO;
}

export function AIAgentNode(props: Props) {
  const { data } = props;
  const api = useNodeResourceApiContext();

  const frameworkCtx = useUIFrameworkContext();
  const { Image } = frameworkCtx;
  const { i } = useLocale();

  return (
    <NodePanel embed={false}>
      <NodeHeader type="AI" title={i(data.name)} description={i(data.description)} />

      <Stack>
        {data?.skillsets?.map((skillset) => (
          <SkillsetsSelectBOInput key={skillset.key} api={api} value={skillset} readonly onChange={() => {}} />
        ))}
      </Stack>

      {/* {!props.embed && (
        <Handle type="source" position={Position.Right} isConnectable={true} id={`ai_agent_${data.id}`} />
      )} */}
    </NodePanel>
  );
}
