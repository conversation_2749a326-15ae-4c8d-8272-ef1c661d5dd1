import { useRef, useEffect, useMemo } from 'react';
import type { ILocaleContext } from '@bika/contents/i18n';
import type { EditorNodeFolderDTO } from '@bika/types/editor/dto';
import { useNodeResourceApiContext } from '@bika/types/node/context';
import type { AvatarLogo } from '@bika/types/system';
import { Box } from '@bika/ui/layouts';
import { useAttachmentUpload } from '../../components/image-crop-upload/use-bika-attachment-upload';
import { AvatarLogoBOInput } from '../../shared/types-form/avatar-logo-bo-input';
import { MarkdownStringInput } from '../../shared/types-form/markdown-string-input';
import { NameDescriptionBOInput } from '../../shared/types-form/name-description-bo-input';

interface Props {
  value: EditorNodeFolderDTO;
  onChange: (value: EditorNodeFolderDTO) => void;
  locale: ILocaleContext;
  setErrors?: (errors?: Record<string, string>) => void;
  setShowImageUpload: (show: boolean) => void;
  cover?: AvatarLogo;
}

export const FolderBoInput = (props: Props) => {
  const editorContext = useNodeResourceApiContext();
  const { unsplashDownload } = useAttachmentUpload();
  const { value, locale, setErrors, setShowImageUpload, onChange, cover: _cover } = props;
  const { t, i } = locale;
  const { data } = value;
  const cover = _cover ||
    data?.cover || {
      type: 'COLOR',
      color: 'BLUE',
    };
  const dataRef = useRef(value);

  useEffect(() => {
    dataRef.current = value;
  }, [value]);

  // local onChange

  const handelChange = (newData: Partial<EditorNodeFolderDTO['data']>) => {
    onChange({
      ...dataRef.current,
      data: {
        ...dataRef.current.data,
        ...newData,
      },
    });
  };

  const readme = useMemo(() => {
    if (data.readme) {
      return data.readme;
    }
    if (value.nodeFolderType === 'TEMPLATE') {
      return value.template.readme;
    }
    return '';
  }, [data.readme, value.nodeFolderType]);

  return (
    <Box>
      <AvatarLogoBOInput
        name={i(data.name)}
        value={cover}
        onChange={(_value) => {}}
        locale={locale}
        upload={(file: File) => editorContext.folder.uploadFolderLogo({ file, filePrefix: 'folder' })}
        unsplashDownload={unsplashDownload}
        onClickChangeCover={() => setShowImageUpload(true)}
        avatarFilePrefix="folder"
      />
      <NameDescriptionBOInput
        labels={{
          name: t.resource.folder_name,
          description: t.resource.folder_description,
        }}
        locale={locale}
        value={{
          name: data.name,
          description: data.description,
        }}
        onChange={handelChange}
        setErrors={setErrors}
        context={{
          resource: data,
        }}
      />
      <MarkdownStringInput
        locale={locale}
        isMultiLang
        label={t.resource.folder_readme}
        value={readme || ''}
        onChange={(readme) => handelChange({ readme })}
        setErrors={setErrors}
        height={readme ? '300px' : '160px'}
        // aiWriter={[
        //   {
        //     type: 'RESOURCE_README',
        //     folder: {
        //       resourceType: 'FOLDER',
        //       id: data.id as string,
        //       name: data.name,
        //     },
        //   },
        //   {
        //     type: 'TRANSLATE',
        //   },
        //   {
        //     type: 'I18N_STRING',
        //   },
        // ]}
      />
    </Box>
  );
};
