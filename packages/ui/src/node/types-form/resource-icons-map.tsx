import React from 'react';
import type { ViewType } from '@bika/types/database/bo';
import type { NodeResourceType } from '@bika/types/node/bo';
import AutomationOutlined from '../../icons/components/automation_outlined';
import DashboardOutlined from '../../icons/components/dashboard_outlined';
import DatasheetFilled from '../../icons/components/datasheet_filled';
import FileOutlined from '../../icons/components/file_outlined';
import FolderNormalOutlined from '../../icons/components/folder_normal_outlined';
import FormOutlined from '../../icons/components/form_outlined';
import GalleryOutlined from '../../icons/components/gallery_outlined';
import GridOutlined from '../../icons/components/grid_outlined';
import ImageOutlined from '../../icons/components/image_outlined';
import LogOutlined from '../../icons/components/log_outlined';
import MirrorOutlined from '../../icons/components/mirror_outlined';
import RobotOutlined from '../../icons/components/robot_outlined';
import EmbedOutlined from '../../icons/doc_hide_components/embed_outlined';
import GanttOutlined from '../../icons/doc_hide_components/gantt_outlined';
import KanbanOutlined from '../../icons/doc_hide_components/kanban_outlined';
import type { IIconProps } from '../../icons/utils/icon';

export const ResourceIconMap: Record<NodeResourceType, React.FC<IIconProps>> = {
  FOLDER: FolderNormalOutlined,
  TEMPLATE: GalleryOutlined,
  DATABASE: DatasheetFilled,
  DATAPAGE: FolderNormalOutlined,
  CANVAS: ImageOutlined,
  ALIAS: RobotOutlined,
  DOCUMENT: FileOutlined,
  FILE: FileOutlined,
  // VIEW: MirrorOutlined,

  AUTOMATION: AutomationOutlined,
  DASHBOARD: DashboardOutlined,
  REPORT_TEMPLATE: LogOutlined,
  EMBED: EmbedOutlined,
  ROOT: FolderNormalOutlined,
  // CAMPAIGN: FolderNormalOutlined,
  FORM: FormOutlined,
  AI: RobotOutlined,
  MIRROR: MirrorOutlined,
  PAGE: EmbedOutlined,
  // APP_PAGE: EmbedOutlined,
};

export const ViewIconMap: Record<ViewType, React.FC<IIconProps>> = {
  TABLE: GridOutlined,
  KANBAN: KanbanOutlined,
  FORM: FormOutlined,
  GALLERY: GalleryOutlined,
  GANTT: GanttOutlined,
};
