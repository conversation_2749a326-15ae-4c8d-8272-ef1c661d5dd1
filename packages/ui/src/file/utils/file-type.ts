import accept from 'attr-accept';
import mime from 'mime-types';
import { DocType, FileType } from './enum';

const IconImg = '/assets/attachment/attachment_img_small_placeholder_filled.png'; // img
const IconTxt = '/assets/attachment/database_img_attachment_text_placeholder.png'; // txt
const IconZip = '/assets/attachment/database_img_attachment_compressed_placeholder.png'; // zip
const IconExcel = '/assets/attachment/database_img_attachment_excel_placeholder.png'; // excel
const IconOther = '/assets/attachment/database_img_attachment_other_placeholder.png';
const IconPdf = '/assets/attachment/database_img_attachment_pdf_placeholder.png'; // pdf
const IconPpt = '/assets/attachment/database_img_attachment_ppt_placeholder.png'; // ppt
const IconAudio = '/assets/attachment/database_img_attachment_video_placeholder.png';
const IconWord = '/assets/attachment/database_img_attachment_word_placeholder.png';

export const NO_SUPPORT_IMG_MIME_TYPE = ['image/vnd.adobe.photoshop', 'image/tiff', 'image/vnd.dwg'];

const WORD_MIME_TYPE = [
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
  'application/vnd.ms-word.document.macroEnabled.12',
];

const PPT_MIME_TYPE = [
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'application/vnd.openxmlformats-officedocument.presentationml.template',
  'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
  'application/vnd.ms-powerpoint.addin.macroEnabled.12',
  'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
  'application/vnd.ms-powerpoint.template.macroEnabled.12',
  'application/vnd.ms-powerpoint.slideshow.macroEnabled.12',
];

const EXCEL_MIME_TYPE = [
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
  'application/vnd.ms-excel.sheet.macroEnabled.12',
  'application/vnd.ms-excel.template.macroEnabled.12',
  'application/vnd.ms-excel.addin.macroEnabled.12',
  'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
  'text/csv',
];

export const DOC_MIME_TYPE = [...WORD_MIME_TYPE, ...EXCEL_MIME_TYPE, ...PPT_MIME_TYPE];

const MEDIA_TYPE = ['audio/*', 'video/*'];

// ts suffix files are recognized as video/mp2t video format by default
const INVALID_MEDIA_TYPE = ['video/mp2t'];

const ZIPPED_TYPE = ['application/zip', 'application/x-7z-compressed', 'application/x-rar-compressed'];

export function isPdf(file: { name: string; type: string }) {
  return accept(file, 'application/pdf');
}

export function isGif(file: { name: string; type: string }) {
  return accept(file, 'image/gif');
}

export function isWebp(file: { name: string; type: string }) {
  return accept(file, 'image/webp');
}

export function isImage(file: { name: string; type: string }) {
  return accept(file, 'image/*');
}

export function isSvg(file: { name: string; type: string }) {
  return accept(file, 'image/svg+xml');
}

export function isWhatFileType(file: { name: string; type: string }) {
  if (isImage(file)) {
    return FileType.Image;
  }

  if (isPdf(file)) {
    return FileType.Pdf;
  }

  const inferredType = mime.lookup(file.name) as string;

  if (accept(file, DOC_MIME_TYPE) || DOC_MIME_TYPE.includes(inferredType)) {
    return FileType.Doc;
  }

  if (accept(file, MEDIA_TYPE) && !INVALID_MEDIA_TYPE.includes(inferredType)) {
    return FileType.Media;
  }

  if (accept(file, ZIPPED_TYPE)) {
    return FileType.Zip;
  }

  if (accept(file, 'text/plain')) {
    return FileType.Txt;
  }

  return FileType.Other;
}

export function isDocType(file: { name: string; type: string }) {
  const inferredType = mime.lookup(file.name) as string;
  if (accept(file, WORD_MIME_TYPE) || WORD_MIME_TYPE.includes(inferredType)) {
    return DocType.Word;
  }
  if (accept(file, PPT_MIME_TYPE) || PPT_MIME_TYPE.includes(inferredType)) {
    return DocType.PPT;
  }
  if (accept(file, EXCEL_MIME_TYPE) || EXCEL_MIME_TYPE.includes(inferredType)) {
    return DocType.Excel;
  }
  return '';
}

export function renderFileIconUrl({ name, type }: { name: string; type: string }): string {
  const fileType = isWhatFileType({ name, type });
  switch (fileType) {
    case FileType.Image: {
      return IconImg;
    }
    case FileType.Media: {
      return IconAudio;
    }
    case FileType.Doc: {
      const docType = isDocType({ name, type });
      if (docType === DocType.Word) {
        return IconWord;
      }
      if (docType === DocType.Excel) {
        return IconExcel;
      }
      if (docType === DocType.PPT) {
        return IconPpt;
      }
      return IconOther;
    }
    case FileType.Pdf: {
      return IconPdf;
    }
    case FileType.Zip: {
      return IconZip;
    }
    case FileType.Txt: {
      return IconTxt;
    }
    case FileType.Other:
    default: {
      return IconOther;
    }
  }
}
