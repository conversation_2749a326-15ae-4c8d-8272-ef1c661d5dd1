// import fs from 'fs';
import assert from 'assert';
import { test, expect } from 'vitest';
import type { BikafileData } from '@bika/types/bikafile/bo';
import { iStringParse } from '@bika/types/i18n/bo';
import { Bikafile } from './bikafile';
import { defaultTemplate } from '@bika/types/utils';

/**
 * create new empty bika file, for template.json
 */
test('new bikafile : template', async () => {
  // 写入Template，内存模式

  const bikafile2 = new Bikafile();
  await bikafile2.setData({
    format: 'TEMPLATE',
    readme: 'readme',
    template: defaultTemplate,
    releases: [],
  });
  expect(bikafile2.format).toBe('TEMPLATE');
  expect(bikafile2.data.format).toBe('TEMPLATE');
  assert(bikafile2.data.format === 'TEMPLATE'); // 触发ts推断
  expect(bikafile2.data.template.templateId).toBe('default-template');
  expect(iStringParse(bikafile2.data.readme)).toBe('readme');

  const file1 = await bikafile2.writeFile('/tmp/bikafile.bika');
  console.log('write to bikafile path:', file1);

  // 重新读回来，读文件
  const bikafile3 = new Bikafile('/tmp/bikafile.bika');

  expect(bikafile3.format).toBe('TEMPLATE');
  assert(bikafile3.data.format === 'TEMPLATE'); // 触发ts推断
  expect(bikafile3.data.template.templateId).toBe('default-template');

  // 继续增加个asset
  bikafile3.addLocalFileAsAsset('testAsset', `${__dirname}/bikafile.ts`);
  expect(bikafile3.assets.length).toBe(1);
});

test('new bikafile : resources', async () => {
  const data: BikafileData = {
    format: 'RESOURCES',
    resources: [
      {
        resourceType: 'DATABASE',
        templateId: 'people',
        databaseType: 'DATUM',
        name: 'People',
        records: [],
        fields: [
          {
            // TODO: 专属Name，带LastName、FirstName
            type: 'SINGLE_TEXT',
            templateId: 'name',
            name: 'Name',
          },
          {
            type: 'EMAIL',
            templateId: 'email',
            name: 'Email',
          },
          {
            type: 'LONG_TEXT',
            templateId: 'description',
            name: '描述',
          },
          {
            type: 'PHONE',
            templateId: 'phone',
            name: '电话号码',
          },
          {
            type: 'LONG_TEXT',
            templateId: 'department',
            name: '部门',
          },
          {
            type: 'LONG_TEXT',
            templateId: 'position',
            name: '职位',
          },
          {
            type: 'LONG_TEXT',
            templateId: 'description',
            name: '描述',
          },
          {
            type: 'ONE_WAY_LINK',
            templateId: 'organization',
            // 关联的组织
            name: 'Organization',
            property: {
              foreignDatabaseTemplateId: 'base-crm:organization',
            },
          },
          {
            templateId: 'created_time',
            name: 'Created Time',
            type: 'CREATED_TIME',
            property: {
              dateFormat: 'YYYY-MM-DD',
              timeFormat: 'HH:mm',
              includeTime: false,
            },
          },
        ],
      },
    ],
  };
  // 写入Resource BO，内存模式
  const bikafile5 = new Bikafile(data);

  await bikafile5.writeFile('/tmp/bikafile5.bika');

  const readFile = new Bikafile('/tmp/bikafile5.bika');
  expect(readFile.format).toBe('RESOURCES');

  // const assets1 = bikafile5.assets;
});
