# 临时，用就不用了，批量改 template 版本号
import os
import re

def increment_version(version):
    major, minor, patch = map(int, version.split('.'))
    patch += 1  # Increment patch version
    return f"{major}.{minor}.{patch}"

def update_template_version(directory, search_string):
    for root, _, files in os.walk(directory):
        for file in files:
            if file == 'template.ts':
                file_path = os.path.join(root, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                print('Found: ' + file_path)
                if search_string in content:
                    # Find the current version
                    version_match = re.search(r'version:\s*\'(\d+\.\d+\.\d+)\'', content)
                    if version_match:
                        current_version = version_match.group(1)
                        new_version = increment_version(current_version)
                        updated_content = content.replace(current_version, new_version)

                        # Write the updated content back to the file
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(updated_content)

                        print(f"Updated {file_path}: {current_version} -> {new_version}")

# 使用示例
directory_to_search = "../../contents/templates"  # 当前目录
search_string = "databaseType: 'DATUM',"
update_template_version(directory_to_search, search_string)