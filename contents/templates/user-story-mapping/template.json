{"templateId": "user-story-mapping", "name": "User Story Mapping", "description": "This folder contains the Bika.ai User Story Mapping Template, designed to help teams effectively organize and prioritize user stories. With this template, you can clearly visualize the user journey, identify key features, and ensure that new capabilities align with user needs.", "cover": "/assets/template/user-story-mapping/user-story-mapping.png", "author": "<PERSON> <<EMAIL>>", "category": ["project"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "0.1.1", "resources": [{"resourceType": "DATABASE", "templateId": "database_releases", "name": "Releases", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "view_releases", "name": "Releases", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "release_number", "width": 287}, {"templateId": "features_released", "width": 226}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "release_number", "privilege": "TYPE_EDIT", "name": "Release Number", "primary": true}, {"type": "LINK", "templateId": "features_released", "privilege": "NAME_EDIT", "name": "Features Released", "property": {"foreignDatabaseTemplateId": "user_stories", "brotherFieldTemplateId": "user_stories:field_release"}, "primary": false}], "records": [{"templateId": "version_2.0", "data": {"release_number": "Version 2.0", "features_released": ["change_billing_address"]}, "values": {"release_number": "Version 2.0", "features_released": ["Change Billing Address"]}}, {"templateId": "version_1.2", "data": {"release_number": "Version 1.2", "features_released": ["save_for_later"]}, "values": {"release_number": "Version 1.2", "features_released": ["Save for Later"]}}, {"templateId": "version_1.1", "data": {"release_number": "Version 1.1", "features_released": ["brand_filter"]}, "values": {"release_number": "Version 1.1", "features_released": ["Brand Filter"]}}, {"templateId": "version_1.0", "data": {"release_number": "Version 1.0", "features_released": ["show_product_details"]}, "values": {"release_number": "Version 1.0", "features_released": ["Show Product Details"]}}]}, {"resourceType": "DATABASE", "templateId": "user_stories", "name": "User stories", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "all_features", "name": "All Features", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "field_story", "hidden": false}, {"templateId": "usage_sequence", "hidden": false, "width": 348}, {"templateId": "field_necessity", "hidden": false, "width": 198}, {"templateId": "field_release", "hidden": false, "width": 270}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "field_story", "privilege": "TYPE_EDIT", "name": "Story", "primary": true}, {"type": "SINGLE_SELECT", "templateId": "usage_sequence", "privilege": "NAME_EDIT", "name": "Usage Sequence", "property": {"options": [{"id": "searching_for_a_product", "name": "Searching for a Product", "color": "deepPurple4"}, {"id": "product_page", "name": "Product Page", "color": "orange4"}, {"id": "checkout_screen", "name": "Checkout Screen", "color": "pink4"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "field_necessity", "privilege": "NAME_EDIT", "name": "Necessity", "property": {"options": [{"id": "must_have", "name": "Must have", "color": "green5"}, {"id": "should_have", "name": "Should have", "color": "deepPurple"}, {"id": "could_have", "name": "Could have", "color": "teal4"}, {"id": "wont_have", "name": "Won't have", "color": "blue1"}], "defaultValue": ""}, "primary": false}, {"type": "LINK", "templateId": "field_release", "privilege": "NAME_EDIT", "name": "Release", "property": {"foreignDatabaseTemplateId": "database_releases", "brotherFieldTemplateId": "database_releases:features_released"}, "primary": false}], "records": [{"templateId": "change_billing_address", "data": {"field_necessity": "must_have", "field_story": "Change Billing Address", "usage_sequence": "checkout_screen", "field_release": ["version_2.0"]}, "values": {"field_necessity": "Must have", "field_story": "Change Billing Address", "usage_sequence": "Checkout Screen", "field_release": ["Version 2.0"]}}, {"templateId": "show_product_details", "data": {"field_necessity": "must_have", "field_story": "Show Product Details", "usage_sequence": "product_page", "field_release": ["version_1.0"]}, "values": {"field_necessity": "Must have", "field_story": "Show Product Details", "usage_sequence": "Product Page", "field_release": ["Version 1.0"]}}, {"templateId": "save_for_later", "data": {"field_necessity": "could_have", "field_story": "Save for Later", "usage_sequence": "product_page", "field_release": ["version_1.2"]}, "values": {"field_necessity": "Could have", "field_story": "Save for Later", "usage_sequence": "Product Page", "field_release": ["Version 1.2"]}}, {"templateId": "brand_filter", "data": {"field_necessity": "should_have", "field_story": "Brand Filter", "usage_sequence": "searching_for_a_product", "field_release": ["version_1.1"]}, "values": {"field_necessity": "Should have", "field_story": "Brand Filter", "usage_sequence": "Searching for a Product", "field_release": ["Version 1.1"]}}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}