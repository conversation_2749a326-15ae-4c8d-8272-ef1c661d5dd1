import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'pr-media-crm',
  name: 'PR & media CRM',
  description:
    'This template helps manage media and public relations, track important articles, publications, and media contacts, and facilitate effective communication and collaboration.',
  cover: '/assets/template/pr-media-crm/cover.jpg',
  author: ' <PERSON><PERSON> <<EMAIL>>',
  category: ['sales', 'marketing'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  keywords: 'PR Media CRM, Relevant Articles tracking, Media Contacts management, Publications database',
  useCases:
    'Record relevant articles, track releases, maintain media contact information, manage publications database, plan public relations activities, organize media communication records, schedule press releases, monitor media reports, manage press release distribution, generate public relations activity reports',
  version: '1.0.4',
  resources: [
    {
      resourceType: 'DATABASE',
      templateId: 'relevant_articles',
      name: 'Relevant articles',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'all_articles',
          name: 'All articles',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [
            {
              fieldTemplateId: 'date_published',
              asc: false,
            },
          ],
          fields: [
            {
              templateId: 'headline',
              hidden: false,
            },
            {
              templateId: 'publication',
              hidden: false,
            },
            {
              templateId: 'author',
              hidden: false,
            },
            {
              templateId: 'date_published',
              hidden: false,
            },
            {
              templateId: 'about_porchcam',
              hidden: false,
            },
            {
              templateId: 'link_to_article',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'about_porchcam_base',
          name: 'About PorchCam',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'about_porchcam',
                fieldType: 'CHECKBOX',
                clause: {
                  operator: 'Is',
                  value: true,
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'headline',
              hidden: false,
            },
            {
              templateId: 'publication',
              hidden: false,
            },
            {
              templateId: 'author',
              hidden: false,
            },
            {
              templateId: 'date_published',
              hidden: false,
            },
            {
              templateId: 'about_porchcam',
              hidden: false,
            },
            {
              templateId: 'link_to_article',
              hidden: false,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'headline',
          privilege: 'TYPE_EDIT',
          name: 'Headline',
          primary: true,
        },
        {
          type: 'LINK',
          templateId: 'publication',
          privilege: 'NAME_EDIT',
          name: 'Publication',
          property: {
            foreignDatabaseTemplateId: 'publications',
            brotherFieldTemplateId: 'publications:relevant_articles',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'author',
          privilege: 'NAME_EDIT',
          name: 'Author',
          property: {
            foreignDatabaseTemplateId: 'press_media_contacts',
            brotherFieldTemplateId: 'press_media_contacts:articales',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'date_published',
          privilege: 'NAME_EDIT',
          name: 'Date published',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'CHECKBOX',
          templateId: 'about_porchcam',
          privilege: 'NAME_EDIT',
          name: 'About PorchCam',
          primary: false,
        },
        {
          type: 'URL',
          templateId: 'link_to_article',
          privilege: 'NAME_EDIT',
          name: 'Link to article',
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'record_1',
          data: {
            publication: ['record_c'],
            about_porchcam: true,
            link_to_article: 'https://example.com/article1',
            date_published: '2024-02-11T16:00:00.000Z',
            author: ['re_4'],
            headline: 'How to Launch a Successful PR Campaign',
          },
          values: {
            publication: ['Fashion Forward'],
            about_porchcam: '1',
            link_to_article: 'https://example.com/article1',
            date_published: '2024-02-11',
            author: ['Michael Tan'],
            headline: 'How to Launch a Successful PR Campaign',
          },
        },
        {
          templateId: 'record_2',
          data: {
            publication: ['record_b'],
            link_to_article: 'https://example.com/article2',
            date_published: '2024-01-14T16:00:00.000Z',
            author: ['re_5'],
            headline: 'Breaking into Top Media Outlets',
          },
          values: {
            publication: ['Green Living News'],
            link_to_article: 'https://example.com/article2',
            date_published: '2024-01-14',
            author: ['Lisa Zhang'],
            headline: 'Breaking into Top Media Outlets',
          },
        },
        {
          templateId: 'record_3',
          data: {
            publication: ['record_d'],
            about_porchcam: true,
            link_to_article: 'https://example.com/article3',
            date_published: '2023-12-09T16:00:00.000Z',
            author: ['re_2'],
            headline: 'The Power of Storytelling in PR',
          },
          values: {
            publication: ['Business Daily'],
            about_porchcam: '1',
            link_to_article: 'https://example.com/article3',
            date_published: '2023-12-09',
            author: ['James Wood'],
            headline: 'The Power of Storytelling in PR',
          },
        },
        {
          templateId: 'record_4',
          data: {
            publication: ['record_a'],
            link_to_article: 'https://example.com/article4',
            date_published: '2023-11-07T16:00:00.000Z',
            author: ['re_3'],
            headline: 'Building Relationships with Journalists',
          },
          values: {
            publication: ['Health & Wellness Weekly'],
            about_porchcam: '0',
            link_to_article: 'https://example.com/article4',
            date_published: '2023-11-07',
            author: ['Sarah Li'],
            headline: 'Building Relationships with Journalists',
          },
        },
        {
          templateId: 'record_5',
          data: {
            publication: ['record_e'],
            about_porchcam: true,
            link_to_article: 'https://example.com/article5',
            date_published: '2024-03-19T16:00:00.000Z',
            author: ['re_1'],
            headline: 'PR Trends to Watch in 2024',
          },
          values: {
            publication: ['Tech Insider'],
            about_porchcam: '1',
            link_to_article: 'https://example.com/article5',
            date_published: '2024-03-19',
            author: ['Emily Chen'],
            headline: 'PR Trends to Watch in 2024',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'publications',
      name: 'Publications',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'all_publications',
          name: 'All publications',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'name',
              hidden: false,
            },
            {
              templateId: 'type',
              hidden: false,
            },
            {
              templateId: 'description',
              hidden: false,
            },
            {
              templateId: 'relevant_articles',
              hidden: false,
            },
            {
              templateId: 'contacts',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'online_print',
          name: 'Online & print',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'type',
                fieldType: 'MULTI_SELECT',
                clause: {
                  operator: 'Contains',
                  value: ['opt_3', '2'],
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'name',
              hidden: false,
            },
            {
              templateId: 'type',
              hidden: false,
            },
            {
              templateId: 'description',
              hidden: false,
            },
            {
              templateId: 'relevant_articles',
              hidden: false,
            },
            {
              templateId: 'contacts',
              hidden: false,
            },
          ],
          groups: [
            {
              fieldTemplateId: 'type',
              asc: true,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'blogs',
          name: 'Blogs',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'type',
                fieldType: 'MULTI_SELECT',
                clause: {
                  operator: 'Is',
                  value: ['1'],
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'name',
              hidden: false,
            },
            {
              templateId: 'type',
              hidden: false,
            },
            {
              templateId: 'description',
              hidden: false,
            },
            {
              templateId: 'relevant_articles',
              hidden: false,
            },
            {
              templateId: 'contacts',
              hidden: false,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'name',
          privilege: 'TYPE_EDIT',
          name: 'Name',
          primary: true,
        },
        {
          type: 'MULTI_SELECT',
          templateId: 'type',
          privilege: 'NAME_EDIT',
          name: 'Type',
          property: {
            options: [
              {
                id: 'opt_1',
                name: 'Blog',
                color: 'deepPurple2',
              },
              {
                id: 'opt_2',
                name: 'Print publication',
                color: 'indigo3',
              },
              {
                id: 'opt_3',
                name: 'Online publication',
                color: 'teal3',
              },
            ],
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'description',
          privilege: 'NAME_EDIT',
          name: 'Description',
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'relevant_articles',
          privilege: 'NAME_EDIT',
          name: 'Relevant articles',
          property: {
            foreignDatabaseTemplateId: 'relevant_articles',
            brotherFieldTemplateId: 'relevant_articles:publication',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'contacts',
          privilege: 'NAME_EDIT',
          name: 'Contacts',
          property: {
            foreignDatabaseTemplateId: 'press_media_contacts',
            brotherFieldTemplateId: 'press_media_contacts:publications',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'record_a',
          data: {
            type: ['opt_1'],
            description: 'Weekly articles on health tips and wellness advice',
            name: 'Health & Wellness Weekly',
            relevant_articles: ['record_4'],
            contacts: ['re_5'],
          },
          values: {
            type: ['Blog'],
            description: 'Weekly articles on health tips and wellness advice',
            name: 'Health & Wellness Weekly',
            relevant_articles: ['Building Relationships with Journalists'],
            contacts: ['Lisa Zhang'],
          },
        },
        {
          templateId: 'record_b',
          data: {
            type: ['opt_3'],
            description: 'News on eco-friendly practices and sustainable living',
            name: 'Green Living News',
            relevant_articles: ['record_2'],
            contacts: ['re_3', 're_4'],
          },
          values: {
            type: ['Online publication'],
            description: 'News on eco-friendly practices and sustainable living',
            name: 'Green Living News',
            relevant_articles: ['Breaking into Top Media Outlets'],
            contacts: ['Sarah Li', 'Michael Tan'],
          },
        },
        {
          templateId: 'record_c',
          data: {
            type: ['opt_2'],
            description: 'Fashion trends, runway reports, and style advice',
            name: 'Fashion Forward',
            relevant_articles: ['record_1'],
            contacts: ['re_2'],
          },
          values: {
            type: ['Print publication'],
            description: 'Fashion trends, runway reports, and style advice',
            name: 'Fashion Forward',
            relevant_articles: ['How to Launch a Successful PR Campaign'],
            contacts: ['James Wood'],
          },
        },
        {
          templateId: 'record_d',
          data: {
            type: ['opt_2'],
            description: 'Business insights and market analysis for professionals',
            name: 'Business Daily',
            relevant_articles: ['record_3'],
          },
          values: {
            type: ['Print publication'],
            description: 'Business insights and market analysis for professionals',
            name: 'Business Daily',
            relevant_articles: ['The Power of Storytelling in PR'],
          },
        },
        {
          templateId: 'record_e',
          data: {
            type: ['opt_1'],
            description: 'Leading tech publication covering innovation and gadgets',
            name: 'Tech Insider',
            relevant_articles: ['record_5'],
            contacts: ['re_1'],
          },
          values: {
            type: ['Blog'],
            description: 'Leading tech publication covering innovation and gadgets',
            name: 'Tech Insider',
            relevant_articles: ['PR Trends to Watch in 2024'],
            contacts: ['Emily Chen'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'press_media_contacts',
      name: 'Press & media contacts',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'all_contacts',
          name: 'All Contacts',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [
            {
              fieldTemplateId: 'relationship',
              asc: true,
            },
          ],
          fields: [
            {
              templateId: 'name',
              hidden: false,
            },
            {
              templateId: 'relationship',
              hidden: false,
            },
            {
              templateId: 'publications',
              hidden: false,
            },
            {
              templateId: 'title',
              hidden: false,
            },
            {
              templateId: 'email_address',
              hidden: false,
            },
            {
              templateId: 'high_value',
              hidden: false,
            },
            {
              templateId: 'internal_contact',
              hidden: false,
            },
            {
              templateId: 'topic_interests',
              hidden: false,
            },
            {
              templateId: 'articales',
              hidden: false,
            },
            {
              templateId: 'notes',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'established_relationship',
          name: 'Established relationship',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'relationship',
                fieldType: 'SINGLE_SELECT',
                clause: {
                  operator: 'Contains',
                  value: ['opt_4', 'opt_5'],
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'name',
              hidden: false,
            },
            {
              templateId: 'relationship',
              hidden: false,
            },
            {
              templateId: 'title',
              hidden: false,
            },
            {
              templateId: 'articales',
              hidden: false,
            },
            {
              templateId: 'publications',
              hidden: false,
            },
            {
              templateId: 'email_address',
              hidden: false,
            },
            {
              templateId: 'high_value',
              hidden: false,
            },
            {
              templateId: 'internal_contact',
              hidden: false,
            },
            {
              templateId: 'topic_interests',
              hidden: false,
            },
            {
              templateId: 'notes',
              hidden: false,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'name',
          privilege: 'TYPE_EDIT',
          name: 'Name',
          primary: true,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'relationship',
          privilege: 'NAME_EDIT',
          name: 'Relationship',
          property: {
            options: [
              {
                id: 'opt_1',
                name: 'Not a priority',
              },
              {
                id: 'opt_2',
                name: 'Need to reach out',
                color: 'red3',
              },
              {
                id: 'opt_3',
                name: 'Reached out',
                color: 'orange',
              },
              {
                id: 'opt_4',
                name: 'Established relationship',
                color: 'deepPurple3',
              },
              {
                id: 'opt_5',
                name: 'Written about PorchCam before',
                color: 'indigo3',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'title',
          privilege: 'NAME_EDIT',
          name: 'Title',
          property: {
            options: [
              {
                id: 'opt_1',
                name: 'Blogger',
                color: 'deepPurple2',
              },
              {
                id: 'opt_2',
                name: 'Business correspondent',
                color: 'indigo2',
              },
              {
                id: 'opt_3',
                name: 'Business writer',
                color: 'blue3',
              },
              {
                id: 'opt_4',
                name: 'Consumer reporter',
                color: 'green2',
              },
              {
                id: 'opt_5',
                name: 'Editor',
                color: 'tangerine2',
              },
              {
                id: 'opt_6',
                name: 'Journalist',
                color: 'pink3',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'articales',
          privilege: 'NAME_EDIT',
          name: 'Articles',
          property: {
            foreignDatabaseTemplateId: 'relevant_articles',
            brotherFieldTemplateId: 'relevant_articles:author',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'publications',
          privilege: 'NAME_EDIT',
          name: 'Publications',
          property: {
            foreignDatabaseTemplateId: 'publications',
            brotherFieldTemplateId: 'publications:contacts',
          },
          primary: false,
        },
        {
          type: 'EMAIL',
          templateId: 'email_address',
          privilege: 'NAME_EDIT',
          name: 'Email address',
          primary: false,
        },
        {
          type: 'CHECKBOX',
          templateId: 'high_value',
          privilege: 'NAME_EDIT',
          name: 'High value',
          primary: false,
        },
        {
          type: 'MEMBER',
          templateId: 'internal_contact',
          privilege: 'NAME_EDIT',
          name: 'Internal contact',
          property: {},
          primary: false,
        },
        {
          type: 'MULTI_SELECT',
          templateId: 'topic_interests',
          privilege: 'NAME_EDIT',
          name: 'Topic interests',
          property: {
            options: [
              {
                id: 'opt_1',
                name: 'Startups',
                color: 'blue2',
              },
              {
                id: 'opt_2',
                name: 'Internet of things',
                color: 'deepPurple3',
              },
              {
                id: 'opt_3',
                name: 'Security',
                color: 'indigo3',
              },
              {
                id: 'opt_4',
                name: 'Home',
                color: 'teal3',
              },
              {
                id: 'opt_5',
                name: 'Technology',
                color: 'pink3',
              },
            ],
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'notes',
          privilege: 'NAME_EDIT',
          name: 'Notes',
          primary: false,
        },
      ],
      records: [
        {
          templateId: 're_1',
          data: {
            articales: ['record_5'],
            title: ['opt_1'],
            notes: 'Prefers email pitches in the morning',
            topic_interests: ['opt_2', 'opt_1'],
            name: 'Emily Chen',
            high_value: true,
            internal_contact: [],
            relationship: ['opt_1'],
            publications: ['record_e'],
            email_address: '<EMAIL>',
          },
          values: {
            articales: ['PR Trends to Watch in 2024'],
            title: ['Blogger'],
            notes: 'Prefers email pitches in the morning',
            topic_interests: ['Internet of things', 'Startups'],
            name: 'Emily Chen',
            high_value: '1',
            internal_contact: [],
            relationship: ['Not a priority'],
            publications: ['Tech Insider'],
            email_address: '<EMAIL>',
          },
        },
        {
          templateId: 're_2',
          data: {
            articales: ['record_3'],
            title: ['opt_3'],
            notes: 'Focused on tech trends in Asia',
            topic_interests: ['opt_4', 'opt_3'],
            name: 'James Wood',
            internal_contact: [],
            relationship: ['opt_3'],
            publications: ['record_c'],
            email_address: '<EMAIL>',
          },
          values: {
            articales: ['The Power of Storytelling in PR'],
            title: ['Business writer'],
            notes: 'Focused on tech trends in Asia',
            topic_interests: ['Home', 'Security'],
            name: 'James Wood',
            internal_contact: [],
            relationship: ['Reached out'],
            publications: ['Fashion Forward'],
            email_address: '<EMAIL>',
          },
        },
        {
          templateId: 're_3',
          data: {
            articales: ['record_4'],
            title: ['opt_4'],
            notes: 'Interested in sustainable fashion stories',
            topic_interests: ['opt_1', 'opt_5'],
            name: 'Sarah Li',
            high_value: true,
            internal_contact: [],
            relationship: ['opt_4'],
            publications: ['record_b'],
            email_address: '<EMAIL>',
          },
          values: {
            articales: ['Building Relationships with Journalists'],
            title: ['Consumer reporter'],
            notes: 'Interested in sustainable fashion stories',
            topic_interests: ['Startups', 'Technology'],
            name: 'Sarah Li',
            high_value: '1',
            internal_contact: [],
            relationship: ['Established relationship'],
            publications: ['Green Living News'],
            email_address: '<EMAIL>',
          },
        },
        {
          templateId: 're_4',
          data: {
            articales: ['record_1'],
            title: ['opt_6'],
            notes: 'Covers green energy initiatives',
            topic_interests: ['opt_4'],
            name: 'Michael Tan',
            internal_contact: [],
            relationship: ['opt_5'],
            publications: ['record_b'],
            email_address: '<EMAIL>',
          },
          values: {
            articales: ['How to Launch a Successful PR Campaign'],
            title: ['Journalist'],
            notes: 'Covers green energy initiatives',
            topic_interests: ['Home'],
            name: 'Michael Tan',
            high_value: '0',
            internal_contact: [],
            relationship: ['Written about PorchCam before'],
            publications: ['Green Living News'],
            email_address: '<EMAIL>',
          },
        },
        {
          templateId: 're_5',
          data: {
            articales: ['record_2'],
            title: ['opt_4'],
            notes: 'Accepts guest articles on health topics',
            topic_interests: ['opt_3'],
            name: 'Lisa Zhang',
            internal_contact: [],
            relationship: ['opt_3'],
            publications: ['record_a'],
            email_address: '<EMAIL>',
          },
          values: {
            articales: ['Breaking into Top Media Outlets'],
            title: ['Consumer reporter'],
            notes: 'Accepts guest articles on health topics',
            topic_interests: ['Security'],
            name: 'Lisa Zhang',
            high_value: '0',
            internal_contact: [],
            relationship: ['Reached out'],
            publications: ['Health & Wellness Weekly'],
            email_address: '<EMAIL>',
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
