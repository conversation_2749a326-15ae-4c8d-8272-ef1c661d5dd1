import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'pr-firm-crm',
  name: 'PR Firm CRM',
  description:
    'This PR Firm CRM template organizes contacts, articles, publications, and clients, streamlining press outreach and tracking key media coverage for PR professionals',
  cover: '/assets/template/pr-firm-crm/cover.png',
  author: ' <PERSON><PERSON> <<EMAIL>>',
  category: ['sales', 'marketing'],
  detach: false,
  visibility: 'SPACE',
  schemaVersion: 'v1',
  version: '1.0.4',
  resources: [
    {
      resourceType: 'DATABASE',
      templateId: 'blog_press_contacts',
      name: 'Bloggers & Press Contacts',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'main',
          name: 'Main View',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'name',
              hidden: true,
            },
            {
              templateId: 'notes',
              hidden: true,
            },
            {
              templateId: 'publication',
              hidden: true,
            },
            {
              templateId: 'target',
              hidden: true,
            },
            {
              templateId: 'contacted',
              hidden: true,
            },
            {
              templateId: 'title',
              hidden: true,
            },
            {
              templateId: 'relevant_articles',
              hidden: true,
            },
            {
              templateId: 'email',
              hidden: true,
            },
            {
              templateId: 'attachments',
              hidden: true,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'name',
          privilege: 'TYPE_EDIT',
          name: 'Name',
          primary: true,
        },
        {
          type: 'CHECKBOX',
          templateId: 'target',
          privilege: 'NAME_EDIT',
          name: 'Target',
          primary: false,
        },
        {
          type: 'CHECKBOX',
          templateId: 'contacted',
          privilege: 'FULL_EDIT',
          name: 'Contacted',
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'title',
          privilege: 'FULL_EDIT',
          name: 'Title',
          property: {
            options: [
              {
                id: 'opt_1',
                name: 'Writer',
                color: 'orange2',
              },
              {
                id: 'opt_2',
                name: 'Blogger',
                color: 'teal2',
              },
              {
                id: 'opt_3',
                name: 'Editor-in-Chief',
                color: 'blue2',
              },
              {
                id: 'opt_4',
                name: 'Senior Editor',
                color: 'indigo2',
              },
              {
                id: 'opt_5',
                name: 'Features Director',
                color: 'deepPurple2',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'relevant_articles',
          privilege: 'FULL_EDIT',
          name: 'Relevant Articles',
          property: {
            foreignDatabaseTemplateId: 'relevant_articles',
            brotherFieldTemplateId: 'relevant_articles:journalist',
          },
          primary: false,
        },
        {
          type: 'EMAIL',
          templateId: 'email',
          privilege: 'FULL_EDIT',
          name: 'Email',
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'notes',
          privilege: 'FULL_EDIT',
          name: 'Notes',
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'publication',
          privilege: 'FULL_EDIT',
          name: 'Publication(s)',
          property: {
            foreignDatabaseTemplateId: 'publications',
            brotherFieldTemplateId: 'publications:pub_contacts',
          },
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'attachments',
          privilege: 'FULL_EDIT',
          name: 'Attachments',
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'record1',
          data: {
            target: null,
            name: 'James Bennett',
            notes: 'Specializes in PR strategies',
            email: '<EMAIL>',
            publication: ['record_4'],
            relevant_articles: ['re1'],
            title: ['opt_5'],
          },
          values: {
            target: '0',
            name: 'James Bennett',
            notes: 'Specializes in PR strategies',
            email: '<EMAIL>',
            publication: ['PR Today'],
            relevant_articles: ['The Role of Social Media in Modern PR'],
            title: ['Features Director'],
          },
        },
        {
          templateId: 'record2',
          data: {
            target: true,
            name: 'Sophia Lopez',
            notes: 'Focuses on industry analysis',
            email: '<EMAIL>',
            publication: ['record_1'],
            relevant_articles: ['re2'],
            title: ['opt_3'],
          },
          values: {
            target: '1',
            name: 'Sophia Lopez',
            notes: 'Focuses on industry analysis',
            email: '<EMAIL>',
            publication: ['Startup World'],
            relevant_articles: ['Top 10 PR Tools for Small Businesses'],
            title: ['Editor-in-Chief'],
          },
        },
        {
          templateId: 'record3',
          data: {
            target: true,
            name: 'Ethan Garcia',
            notes: 'Writes on emerging technologies',
            email: '<EMAIL>',
            publication: ['record_2'],
            contacted: true,
            relevant_articles: ['re3'],
            title: ['opt_1'],
          },
          values: {
            target: '1',
            name: 'Ethan Garcia',
            notes: 'Writes on emerging technologies',
            email: '<EMAIL>',
            publication: ['Global Business News'],
            contacted: '1',
            relevant_articles: ['Brand Reputation Management in a Digital World'],
            title: ['Writer'],
          },
        },
        {
          templateId: 'record4',
          data: {
            name: 'Olivia Rivera',
            notes: 'Covers business and finance',
            email: '<EMAIL>',
            publication: ['record_5'],
            contacted: true,
            relevant_articles: ['re4'],
            title: ['opt_3'],
          },
          values: {
            target: '0',
            name: 'Olivia Rivera',
            notes: 'Covers business and finance',
            email: '<EMAIL>',
            publication: ['Media Insights'],
            contacted: '1',
            relevant_articles: ['The Impact of AI on Media Relations'],
            title: ['Editor-in-Chief'],
          },
        },
        {
          templateId: 'record5',
          data: {
            target: true,
            name: 'Liam Turner',
            notes: 'Lifestyle press contact',
            email: '<EMAIL>',
            publication: ['record_6'],
            relevant_articles: ['re3', 're5'],
            title: ['opt_2'],
          },
          values: {
            target: '1',
            name: 'Liam Turner',
            notes: 'Lifestyle press contact',
            email: '<EMAIL>',
            publication: ['Tech Daily'],
            contacted: '0',
            relevant_articles: ['Brand Reputation Management in a Digital World', 'How to Maximize Digital PR Efforts'],
            title: ['Blogger'],
          },
        },
        {
          templateId: 'record6',
          data: {
            single_text: 'Emma Brooks',
            notes: 'Tech and innovation blogger',
            email: '<EMAIL>',
            publication: ['record_5'],
            contacted: true,
            relevant_articles: ['re6'],
            title: ['opt_1'],
          },
          values: {
            target: '0',
            single_text: 'Emma Brooks',
            notes: 'Tech and innovation blogger',
            email: '<EMAIL>',
            publication: ['Media Insights'],
            contacted: '1',
            relevant_articles: ['Emerging Tech Trends in 2024'],
            title: ['Writer'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'clients',
      name: 'Clients',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'all_clients',
          name: 'Main View',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'client_name',
            },
            {
              templateId: 'client_type',
            },
            {
              templateId: 'relevant_articles',
              width: 150,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'client_name',
          privilege: 'TYPE_EDIT',
          name: 'Name',
          primary: true,
        },
        {
          type: 'MULTI_SELECT',
          templateId: 'client_type',
          privilege: 'NAME_EDIT',
          name: 'Type',
          property: {
            options: [
              {
                id: '1',
                name: 'Lingerie',
                color: 'teal2',
              },
              {
                id: '2',
                name: 'Clothing Basics',
                color: 'indigo2',
              },
              {
                id: '3',
                name: 'Lifestyle',
                color: 'deepPurple2',
              },
            ],
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'relevant_articles',
          privilege: 'FULL_EDIT',
          name: 'Relevant Articles',
          property: {
            foreignDatabaseTemplateId: 'relevant_articles',
            brotherFieldTemplateId: 'relevant_articles:link_company',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'record_a',
          data: {
            client_name: 'Quantum Dynamics',
            client_type: ['1', '2'],
            relevant_articles: ['re6', 're1'],
          },
          values: {
            client_name: 'Quantum Dynamics',
            client_type: ['Lingerie', 'Clothing Basics'],
            relevant_articles: ['Emerging Tech Trends in 2024', 'The Role of Social Media in Modern PR'],
          },
        },
        {
          templateId: 'record_b',
          data: {
            client_name: 'Skyline Innovations',
            client_type: ['3'],
            relevant_articles: ['re2'],
          },
          values: {
            client_name: 'Skyline Innovations',
            client_type: ['Lifestyle'],
            relevant_articles: ['Top 10 PR Tools for Small Businesses'],
          },
        },
        {
          templateId: 'record_c',
          data: {
            client_name: 'NovaCore Industries',
            client_type: ['2'],
            relevant_articles: ['re3'],
          },
          values: {
            client_name: 'NovaCore Industries',
            client_type: ['Clothing Basics'],
            relevant_articles: ['Brand Reputation Management in a Digital World'],
          },
        },
        {
          templateId: 'record_d',
          data: {
            client_name: 'Vertex Solutions',
            client_type: ['3', '2'],
            relevant_articles: ['re6', 're5', 're4'],
          },
          values: {
            client_name: 'Vertex Solutions',
            client_type: ['Lifestyle', 'Clothing Basics'],
            relevant_articles: [
              'Emerging Tech Trends in 2024',
              'How to Maximize Digital PR Efforts',
              'The Impact of AI on Media Relations',
            ],
          },
        },
        {
          templateId: 'record_e',
          data: {
            client_name: 'BlueWave Corporation',
            client_type: ['2'],
            relevant_articles: ['re3'],
          },
          values: {
            client_name: 'BlueWave Corporation',
            client_type: ['Clothing Basics'],
            relevant_articles: ['Brand Reputation Management in a Digital World'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'publications',
      name: 'Publications',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'all_publications',
          name: 'Main View',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'publication_name',
              hidden: false,
            },
            {
              templateId: 'pub_target',
              hidden: false,
            },
            {
              templateId: 'pub_type',
              hidden: false,
            },
            {
              templateId: 'pub_contacts',
              hidden: false,
            },
            {
              templateId: 'website',
              hidden: false,
            },
            {
              templateId: 'articles',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'blogs',
          name: 'Blogs',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'pub_type',
                fieldType: 'SINGLE_SELECT',
                clause: {
                  operator: 'Is',
                  value: 'opt_y',
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'publication_name',
              hidden: false,
            },
            {
              templateId: 'pub_target',
              hidden: false,
            },
            {
              templateId: 'pub_type',
              hidden: false,
            },
            {
              templateId: 'pub_contacts',
              hidden: false,
            },
            {
              templateId: 'website',
              hidden: false,
            },
            {
              templateId: 'articles',
              hidden: false,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'online',
          name: 'Online Magazines',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'pub_type',
                fieldType: 'SINGLE_SELECT',
                clause: {
                  operator: 'Is',
                  value: 'opt_z',
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'publication_name',
              hidden: false,
            },
            {
              templateId: 'pub_target',
              hidden: false,
            },
            {
              templateId: 'pub_type',
              hidden: false,
            },
            {
              templateId: 'pub_contacts',
              hidden: false,
            },
            {
              templateId: 'website',
              hidden: false,
            },
            {
              templateId: 'articles',
              hidden: false,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'magazines',
          name: 'Print Magazines',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'pub_type',
                fieldType: 'SINGLE_SELECT',
                clause: {
                  operator: 'Is',
                  value: 'opt_x',
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'publication_name',
              hidden: false,
            },
            {
              templateId: 'pub_target',
              hidden: false,
            },
            {
              templateId: 'pub_type',
              hidden: false,
            },
            {
              templateId: 'pub_contacts',
              hidden: false,
            },
            {
              templateId: 'website',
              hidden: false,
            },
            {
              templateId: 'articles',
              hidden: false,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'publication_name',
          privilege: 'TYPE_EDIT',
          name: 'Name',
          primary: true,
        },
        {
          type: 'CHECKBOX',
          templateId: 'pub_target',
          privilege: 'NAME_EDIT',
          name: 'Target',
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'pub_type',
          privilege: 'NAME_EDIT',
          name: 'Type',
          property: {
            options: [
              {
                id: 'opt_z',
                name: 'Online Magazine',
                color: 'teal2',
              },
              {
                id: 'opt_y',
                name: 'Blog',
                color: 'indigo2',
              },
              {
                id: 'opt_x',
                name: 'Print Publication',
                color: 'deepPurple3',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'pub_contacts',
          privilege: 'FULL_EDIT',
          name: 'Contacts',
          property: {
            foreignDatabaseTemplateId: 'blog_press_contacts',
            brotherFieldTemplateId: 'blog_press_contacts:publication',
          },
          primary: false,
        },
        {
          type: 'URL',
          templateId: 'website',
          privilege: 'FULL_EDIT',
          name: 'Website',
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'articles',
          privilege: 'FULL_EDIT',
          name: 'Articles',
          property: {
            foreignDatabaseTemplateId: 'relevant_articles',
            brotherFieldTemplateId: 'relevant_articles:article_pubications',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'record_1',
          data: {
            pub_type: ['opt_z'],
            publication_name: 'Startup World',
            pub_target: null,
            pub_contacts: ['record2'],
            website: 'www.startupworld.com',
            articles: ['re1'],
          },
          values: {
            pub_type: ['Online Magazine'],
            publication_name: 'Startup World',
            pub_target: '0',
            pub_contacts: ['Sophia Lopez'],
            website: 'www.startupworld.com',
            articles: ['The Role of Social Media in Modern PR'],
          },
        },
        {
          templateId: 'record_2',
          data: {
            pub_type: ['opt_y'],
            publication_name: 'Global Business News',
            pub_target: true,
            pub_contacts: ['record3'],
            website: 'www.globalbiznews.com',
            articles: ['re3', 're4'],
          },
          values: {
            pub_type: ['Blog'],
            publication_name: 'Global Business News',
            pub_target: '1',
            pub_contacts: ['Ethan Garcia'],
            website: 'www.globalbiznews.com',
            articles: ['Brand Reputation Management in a Digital World', 'The Impact of AI on Media Relations'],
          },
        },
        {
          templateId: 'record_3',
          data: {
            pub_type: ['opt_x'],
            publication_name: 'Social Buzz',
            pub_target: null,
            website: 'www.socialbuzz.com',
            articles: ['re3'],
          },
          values: {
            pub_type: ['Print Publication'],
            publication_name: 'Social Buzz',
            pub_target: '0',
            website: 'www.socialbuzz.com',
            articles: ['Brand Reputation Management in a Digital World'],
          },
        },
        {
          templateId: 'record_4',
          data: {
            pub_type: ['opt_z'],
            publication_name: 'PR Today',
            pub_contacts: ['record1'],
            website: 'www.prtoday.com',
            articles: ['re2'],
          },
          values: {
            pub_type: ['Online Magazine'],
            publication_name: 'PR Today',
            pub_target: '0',
            pub_contacts: ['James Bennett'],
            website: 'www.prtoday.com',
            articles: ['Top 10 PR Tools for Small Businesses'],
          },
        },
        {
          templateId: 'record_5',
          data: {
            pub_type: ['opt_y'],
            publication_name: 'Media Insights',
            pub_target: true,
            pub_contacts: ['record6', 'record4'],
            website: 'www.mediainsights.com',
            articles: ['re3'],
          },
          values: {
            pub_type: ['Blog'],
            publication_name: 'Media Insights',
            pub_target: '1',
            pub_contacts: ['Emma Brooks', 'Olivia Rivera'],
            website: 'www.mediainsights.com',
            articles: ['Brand Reputation Management in a Digital World'],
          },
        },
        {
          templateId: 'record_6',
          data: {
            pub_type: ['opt_z'],
            publication_name: 'Tech Daily',
            pub_target: null,
            pub_contacts: ['record5'],
            website: 'www.techdaily.com',
            articles: ['re6', 're5'],
          },
          values: {
            pub_type: ['Online Magazine'],
            publication_name: 'Tech Daily',
            pub_target: '0',
            pub_contacts: ['Liam Turner'],
            website: 'www.techdaily.com',
            articles: ['Emerging Tech Trends in 2024', 'How to Maximize Digital PR Efforts'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'relevant_articles',
      name: 'Relevant Articles',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'all_articles',
          name: 'Main View',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'article_title',
              hidden: false,
            },
            {
              templateId: 'article_emphasis',
              hidden: false,
            },
            {
              templateId: 'article_favorability',
              hidden: false,
            },
            {
              templateId: 'journalist',
              hidden: false,
            },
            {
              templateId: 'article_pubications',
              hidden: false,
            },
            {
              templateId: 'article_link',
              hidden: false,
            },
            {
              templateId: 'date',
              hidden: false,
            },
            {
              templateId: 'link_company',
              hidden: false,
            },
            {
              templateId: 'article_notes',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'article_title',
          privilege: 'TYPE_EDIT',
          name: 'Title',
          primary: true,
        },
        {
          type: 'MULTI_SELECT',
          templateId: 'article_emphasis',
          privilege: 'NAME_EDIT',
          name: 'Article Emphasis',
          property: {
            options: [
              {
                id: 'option1',
                name: 'Social Impact',
                color: 'orange2',
              },
              {
                id: 'option2',
                name: 'Clothing Quality',
                color: 'teal2',
              },
              {
                id: 'option3',
                name: 'Launch Article',
                color: 'blue3',
              },
              {
                id: 'option4',
                name: 'Discounted Clothing',
                color: 'indigo2',
              },
              {
                id: 'option5',
                name: 'Invite Only',
                color: 'deepPurple3',
              },
            ],
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'article_favorability',
          privilege: 'NAME_EDIT',
          name: 'Article Favorability',
          property: {
            options: [
              {
                id: 'option_z',
                name: 'Highly Negative',
                color: 'red3',
              },
              {
                id: 'option_y',
                name: 'Somewhat Negative',
                color: 'orange3',
              },
              {
                id: 'option_x',
                name: 'Neutral',
                color: 'indigo3',
              },
              {
                id: 'option_w',
                name: 'Somewhat Positive',
                color: 'teal2',
              },
              {
                id: 'option_v',
                name: 'Highly Positive',
                color: 'deepPurple3',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'journalist',
          privilege: 'FULL_EDIT',
          name: 'Journalist',
          property: {
            foreignDatabaseTemplateId: 'blog_press_contacts',
            brotherFieldTemplateId: 'blog_press_contacts:relevant_articles',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'article_pubications',
          privilege: 'FULL_EDIT',
          name: 'Publications',
          property: {
            foreignDatabaseTemplateId: 'publications',
            brotherFieldTemplateId: 'publications:articles',
          },
          primary: false,
        },
        {
          type: 'URL',
          templateId: 'article_link',
          privilege: 'FULL_EDIT',
          name: 'Link',
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'date',
          privilege: 'FULL_EDIT',
          name: 'Date',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'link_company',
          privilege: 'FULL_EDIT',
          name: 'Company',
          property: {
            foreignDatabaseTemplateId: 'clients',
            brotherFieldTemplateId: 'clients:relevant_articles',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'article_notes',
          privilege: 'FULL_EDIT',
          name: 'Notes',
          primary: false,
        },
      ],
      records: [
        {
          templateId: 're1',
          data: {
            article_favorability: ['option_v'],
            article_title: 'The Role of Social Media in Modern PR',
            article_emphasis: ['option1', 'option5'],
            article_link: 'www.socialmediainsight.com/role-in-pr',
            article_notes: 'Social media’s growing role in PR strategies',
            article_pubications: ['record_1'],
            date: '2024-05-11T16:00:00.000Z',
            journalist: ['record1'],
            link_company: ['record_a'],
          },
          values: {
            article_favorability: ['Highly Positive'],
            article_title: 'The Role of Social Media in Modern PR',
            article_emphasis: ['Social Impact', 'Invite Only'],
            article_link: 'www.socialmediainsight.com/role-in-pr',
            article_notes: 'Social media’s growing role in PR strategies',
            article_pubications: ['Startup World'],
            date: '2024-05-11',
            journalist: ['James Bennett'],
            link_company: ['Quantum Dynamics'],
          },
        },
        {
          templateId: 're2',
          data: {
            article_favorability: ['option_y'],
            article_title: 'Top 10 PR Tools for Small Businesses',
            article_emphasis: ['option3', 'option4'],
            article_link: 'www.smallbizprtools.com/top10',
            article_notes: 'Highlights tools for small PR firms',
            article_pubications: ['record_4'],
            date: '2024-04-07T16:00:00.000Z',
            journalist: ['record2'],
            link_company: ['record_b'],
          },
          values: {
            article_favorability: ['Somewhat Negative'],
            article_title: 'Top 10 PR Tools for Small Businesses',
            article_emphasis: ['Launch Article', 'Discounted Clothing'],
            article_link: 'www.smallbizprtools.com/top10',
            article_notes: 'Highlights tools for small PR firms',
            article_pubications: ['PR Today'],
            date: '2024-04-07',
            journalist: ['Sophia Lopez'],
            link_company: ['Skyline Innovations'],
          },
        },
        {
          templateId: 're3',
          data: {
            article_favorability: ['option_z'],
            article_title: 'Brand Reputation Management in a Digital World',
            article_emphasis: ['option1'],
            article_link: 'www.reputationworld.com/brand-management',
            article_notes: 'Explores strategies for online reputation',
            article_pubications: ['record_5', 'record_2', 'record_3'],
            date: '2024-03-19T16:00:00.000Z',
            journalist: ['record5', 'record3'],
            link_company: ['record_e', 'record_c'],
          },
          values: {
            article_favorability: ['Highly Negative'],
            article_title: 'Brand Reputation Management in a Digital World',
            article_emphasis: ['Social Impact'],
            article_link: 'www.reputationworld.com/brand-management',
            article_notes: 'Explores strategies for online reputation',
            article_pubications: ['Media Insights', 'Global Business News', 'Social Buzz'],
            date: '2024-03-19',
            journalist: ['Liam Turner', 'Ethan Garcia'],
            link_company: ['BlueWave Corporation', 'NovaCore Industries'],
          },
        },
        {
          templateId: 're4',
          data: {
            article_favorability: ['option_w'],
            article_title: 'The Impact of AI on Media Relations',
            article_emphasis: ['option5', 'option3'],
            article_link: 'www.mediainsights.com/impact-of-ai',
            article_notes: 'Insights on AI’s influence on PR tasks',
            article_pubications: ['record_2'],
            date: '2024-03-04T16:00:00.000Z',
            journalist: ['record4'],
            link_company: ['record_d'],
          },
          values: {
            article_favorability: ['Somewhat Positive'],
            article_title: 'The Impact of AI on Media Relations',
            article_emphasis: ['Invite Only', 'Launch Article'],
            article_link: 'www.mediainsights.com/impact-of-ai',
            article_notes: 'Insights on AI’s influence on PR tasks',
            article_pubications: ['Global Business News'],
            date: '2024-03-04',
            journalist: ['Olivia Rivera'],
            link_company: ['Vertex Solutions'],
          },
        },
        {
          templateId: 're5',
          data: {
            article_favorability: ['option_x'],
            article_title: 'How to Maximize Digital PR Efforts',
            article_emphasis: ['option3'],
            article_link: 'www.prdaily.com/maximizing-digital-pr',
            article_notes: 'Best practices for enhancing digital reach',
            article_pubications: ['record_6'],
            date: '2024-02-09T16:00:00.000Z',
            journalist: ['record5'],
            link_company: ['record_d'],
          },
          values: {
            article_favorability: ['Neutral'],
            article_title: 'How to Maximize Digital PR Efforts',
            article_emphasis: ['Launch Article'],
            article_link: 'www.prdaily.com/maximizing-digital-pr',
            article_notes: 'Best practices for enhancing digital reach',
            article_pubications: ['Tech Daily'],
            date: '2024-02-09',
            journalist: ['Liam Turner'],
            link_company: ['Vertex Solutions'],
          },
        },
        {
          templateId: 're6',
          data: {
            article_favorability: ['option_z'],
            article_title: 'Emerging Tech Trends in 2024',
            article_emphasis: ['option1', 'option2'],
            article_link: 'www.technews.com/article1',
            article_notes: 'Discusses the latest technology in PR',
            article_pubications: ['record_6'],
            date: '2024-01-14T16:00:00.000Z',
            journalist: ['record6'],
            link_company: ['record_d', 'record_a'],
          },
          values: {
            article_favorability: ['Highly Negative'],
            article_title: 'Emerging Tech Trends in 2024',
            article_emphasis: ['Social Impact', 'Clothing Quality'],
            article_link: 'www.technews.com/article1',
            article_notes: 'Discusses the latest technology in PR',
            article_pubications: ['Tech Daily'],
            date: '2024-01-14',
            journalist: ['Emma Brooks'],
            link_company: ['Vertex Solutions', 'Quantum Dynamics'],
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
