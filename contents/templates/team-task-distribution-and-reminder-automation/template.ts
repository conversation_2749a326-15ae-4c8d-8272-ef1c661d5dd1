import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'team-task-distribution-and-reminder-automation',
  name: {
    en: 'Team Task Distribution & Reminder Automation',
    'zh-CN': '团队任务分发和提醒自动化',
  },
  description: {
    en: 'This template utilizes automated tools to efficiently allocate tasks, provide timely reminders for progress and deadlines, thereby enhancing team efficiency and task quality.',
    'zh-CN': '此模板利用自动化工具高效分配任务，及时提醒进度和截止日期，提升团队效率和任务质量。',
  },
  cover:
    '/assets/template/team-task-distribution-and-reminder-automation/team-task-distribution-and-reminder-automation.png',
  author: 'linxiaoxin <<EMAIL>>',
  category: ['project', 'automation'],
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.2',
  resources: [
    {
      resourceType: 'AUTOMATION',
      templateId: 'atoswzh0e6kYoVNlEzHezjG9',
      name: {
        en: 'Task progress reminder',
        'zh-CN': '任务进度提醒',
      },
      triggers: [
        {
          triggerType: 'SCHEDULER',
          templateId: 'trgpYp0N0CgPPwKUUPyDwNa1',
          description: {
            en: 'Trigger at 9am every day',
            'zh-CN': '每天上午9点定时触发',
          },
          input: {
            type: 'SCHEDULER',
            scheduler: {
              repeat: {
                every: {
                  type: 'DAY',
                  interval: 1,
                },
              },
              datetime: '2025-01-06T01:00:19.730Z',
            },
          },
        },
      ],
      actions: [
        {
          templateId: 'actErLTsjjEH4vSc88kRSDgR',
          description: {
            en: 'Find task progress status',
            'zh-CN': '查找任务进展状态',
          },
          actionType: 'FIND_RECORDS',
          input: {
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fldVklHjEfqCoSVSQ1joFuSI',
                  fieldType: 'MEMBER',
                  clause: {
                    operator: 'IsNotEmpty',
                  },
                },
              ],
            },
            databaseTemplateId: 'datrTe2yFxFtXKrPsqD4KoQU',
          },
        },
        {
          templateId: 'actgS1Fk8TSdzGw27Wp1mJsY',
          description: {
            en: 'Find tasks due today',
            'zh-CN': '查找今日到期任务',
          },
          actionType: 'FIND_RECORDS',
          input: {
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fldOm1Pfyyw2d0CLOaUF2s6p',
                  fieldType: 'DATETIME',
                  clause: {
                    operator: 'Is',
                    value: ['Today'],
                  },
                },
              ],
            },
            databaseTemplateId: 'datrTe2yFxFtXKrPsqD4KoQU',
          },
        },
        {
          templateId: 'actW1jcOOJjoHzFm7KFlCujD',
          description: {
            en: 'Send task progress reports to designated members',
            'zh-CN': '将各任务进展发送报告给指定成员',
          },
          actionType: 'SEND_REPORT',
          input: {
            to: [
              {
                type: 'SPECIFY_UNITS',
                unitIds: [
                  '<%= JSON.stringify(_actions.actErLTsjjEH4vSc88kRSDgR.records[0].cells.fldVklHjEfqCoSVSQ1joFuSI.data) %>',
                ],
              },
            ],
            markdown:
              '### ‼️Tasks due today  \n' +
              "<%= _renderRecordsAsGrid(_actions.actgS1Fk8TSdzGw27Wp1mJsY.records, ['fldNBDizSEUJj2HsTKczH9Z4','fldxrqHnj5kc7lg6QSZoqnRd','fldOm1Pfyyw2d0CLOaUF2s6p','fldVklHjEfqCoSVSQ1joFuSI','fldr8SRhN44PI299ur7MPtMb']) %>  \n" +
              '  \n' +
              '### Completion progress of each task  \n' +
              "<%= _renderRecordsAsGrid(_actions.actErLTsjjEH4vSc88kRSDgR.records, ['fldNBDizSEUJj2HsTKczH9Z4','fldxrqHnj5kc7lg6QSZoqnRd','fldOm1Pfyyw2d0CLOaUF2s6p','fldVklHjEfqCoSVSQ1joFuSI','fldr8SRhN44PI299ur7MPtMb','flddY52zaUEJ9C8NH0sR42xH']) %>",
            json: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    {
                      text: '### ‼️Tasks due today',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      type: 'variable',
                      attrs: {
                        ids: "_renderRecordsAsGrid(_actions.actgS1Fk8TSdzGw27Wp1mJsY.records, ['fldNBDizSEUJj2HsTKczH9Z4','fldxrqHnj5kc7lg6QSZoqnRd','fldOm1Pfyyw2d0CLOaUF2s6p','fldVklHjEfqCoSVSQ1joFuSI','fldr8SRhN44PI299ur7MPtMb'])",
                        tips: '选中的字段: 任务名称, 重要程度, 截止日期, 任务执行人, 进展',
                        names: ['执行器', '查找记录', '网格列表'],
                      },
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: '### Completion progress of each task',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      type: 'variable',
                      attrs: {
                        ids: "_renderRecordsAsGrid(_actions.actErLTsjjEH4vSc88kRSDgR.records, ['fldNBDizSEUJj2HsTKczH9Z4','fldxrqHnj5kc7lg6QSZoqnRd','fldOm1Pfyyw2d0CLOaUF2s6p','fldVklHjEfqCoSVSQ1joFuSI','fldr8SRhN44PI299ur7MPtMb','flddY52zaUEJ9C8NH0sR42xH'])",
                        tips: '选中的字段: 任务名称, 重要程度, 截止日期, 任务执行人, 进展, 是否延期',
                        names: ['执行器', '查找记录', '网格列表'],
                      },
                    },
                  ],
                },
              ],
            },
            subject: '📝Task progress daily report',
            type: 'MARKDOWN',
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'atoaqmoOF0sme8pB5hy0ouE4',
      name: {
        en: 'Task automatic dispatch',
        'zh-CN': '任务自动派发',
      },
      triggers: [
        {
          triggerType: 'RECORD_MATCH',
          templateId: 'trg40Yf38VZ2lJUL0mkZsZyn',
          description: {
            en: 'When the task executor is not empty',
            'zh-CN': '任务执行人不为空时',
          },
          input: {
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fldVklHjEfqCoSVSQ1joFuSI',
                  fieldType: 'MEMBER',
                  clause: {
                    operator: 'IsNotEmpty',
                  },
                },
              ],
            },
            databaseTemplateId: 'datrTe2yFxFtXKrPsqD4KoQU',
          },
        },
      ],
      actions: [
        {
          templateId: 'act03daT1fpuJlUFkWYSlc7H',
          description: {
            en: 'Send task reminders to the corresponding task performers',
            'zh-CN': '给对应的任务执行人发送任务提醒',
          },
          actionType: 'CREATE_MISSION',
          input: {
            type: 'MISSION_BODY',
            mission: {
              type: 'ENTER_VIEW',
              name: {
                en: 'Attention ⚠️ you have a new task to follow~',
                'zh-CN': '注意⚠️你有新任务需要跟进～',
              },
              description:
                '## 📋 Tasks details  \n' +
                '  \n' +
                '- Task name: <%= _triggers.trg40Yf38VZ2lJUL0mkZsZyn.record.cells.fldNBDizSEUJj2HsTKczH9Z4.data %>  \n' +
                '- Task notes: <%= _triggers.trg40Yf38VZ2lJUL0mkZsZyn.record.cells.fldeafXZvTNMko9Frq5EgA6f.data %>  \n' +
                '- Mandate holders: <%= JSON.stringify(_triggers.trg40Yf38VZ2lJUL0mkZsZyn.record.cells.fldVklHjEfqCoSVSQ1joFuSI.value) %>  \n' +
                '- Importance: <%= JSON.stringify(_triggers.trg40Yf38VZ2lJUL0mkZsZyn.record.cells.fldxrqHnj5kc7lg6QSZoqnRd.value) %>  \n' +
                '- Deadline: <%= _triggers.trg40Yf38VZ2lJUL0mkZsZyn.record.cells.fldOm1Pfyyw2d0CLOaUF2s6p.value %>',
              canCompleteManually: true,
              assignType: 'DEDICATED',
              to: [
                {
                  type: 'SPECIFY_UNITS',
                  unitIds: [
                    '<%= JSON.stringify(_triggers.trg40Yf38VZ2lJUL0mkZsZyn.record.cells.fldVklHjEfqCoSVSQ1joFuSI.data) %>',
                  ],
                },
              ],
              buttonText: {
                'zh-CN': '立即更新',
              },
              viewTemplateId: 'viw7FQBOrtZSw8qiZPL2TBg1',
              databaseTemplateId: 'datrTe2yFxFtXKrPsqD4KoQU',
            },
          },
        },
      ],
    },
    {
      resourceType: 'FORM',
      templateId: 'fomUKToelWdXefVDS609PjdO',
      name: {
        en: 'Task create form',
        'zh-CN': '任务创建表单',
      },
      brandLogo: {
        type: 'EMOJI',
        backgroundColor: '',
        emoji: '📋',
      },
      formType: 'DATABASE',
      databaseTemplateId: 'datrTe2yFxFtXKrPsqD4KoQU',
      metadata: {
        type: 'VIEW',
        viewTemplateId: 'viwBfmsys3OPabUocUulHz9y',
      },
    },
    {
      resourceType: 'MIRROR',
      templateId: 'miry7pxEhdwoMVkav6BBvOEl',
      name: {
        en: 'My tasks',
        'zh-CN': '我的任务',
      },
      mirrorType: 'DATABASE_VIEW',
      databaseTemplateId: 'datrTe2yFxFtXKrPsqD4KoQU',
      viewTemplateId: 'viwtAUbvtYFuYxVed891weNX',
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datrTe2yFxFtXKrPsqD4KoQU',
      name: {
        en: 'Member tasks',
        'zh-CN': '成员任务',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viw7FQBOrtZSw8qiZPL2TBg1',
          name: {
            en: 'Task Master',
            'zh-CN': '任务总表',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldNBDizSEUJj2HsTKczH9Z4',
              hidden: false,
            },
            {
              templateId: 'fldjEcgbShY8UIgnNxHxWW9G',
              hidden: false,
            },
            {
              templateId: 'fldOm1Pfyyw2d0CLOaUF2s6p',
              hidden: false,
            },
            {
              templateId: 'fldpcKvCMM0i1PpuwTHn6PHG',
              hidden: false,
            },
            {
              templateId: 'flddY52zaUEJ9C8NH0sR42xH',
              hidden: false,
            },
            {
              templateId: 'fldVklHjEfqCoSVSQ1joFuSI',
              hidden: false,
              width: 179,
            },
            {
              templateId: 'fldxrqHnj5kc7lg6QSZoqnRd',
              hidden: false,
            },
            {
              templateId: 'fldr8SRhN44PI299ur7MPtMb',
              hidden: false,
            },
            {
              templateId: 'fldjZMCxYQlPzRZFiq49U4mm',
              hidden: false,
            },
            {
              templateId: 'fldeafXZvTNMko9Frq5EgA6f',
              hidden: false,
            },
            {
              templateId: 'fldzvyLxayWRM14sxOKUFJy6',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'viwBfmsys3OPabUocUulHz9y',
          name: {
            en: 'Form view',
            'zh-CN': '表单视图',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldNBDizSEUJj2HsTKczH9Z4',
              hidden: false,
            },
            {
              templateId: 'fldxrqHnj5kc7lg6QSZoqnRd',
              hidden: false,
            },
            {
              templateId: 'fldjEcgbShY8UIgnNxHxWW9G',
              hidden: false,
            },
            {
              templateId: 'fldOm1Pfyyw2d0CLOaUF2s6p',
              hidden: false,
            },
            {
              templateId: 'fldeafXZvTNMko9Frq5EgA6f',
              hidden: false,
            },
            {
              templateId: 'fldVklHjEfqCoSVSQ1joFuSI',
              hidden: false,
            },
            {
              templateId: 'fldpcKvCMM0i1PpuwTHn6PHG',
              hidden: true,
            },
            {
              templateId: 'fldjZMCxYQlPzRZFiq49U4mm',
              hidden: true,
            },
            {
              templateId: 'fldzvyLxayWRM14sxOKUFJy6',
              hidden: true,
            },
            {
              templateId: 'fldr8SRhN44PI299ur7MPtMb',
              hidden: true,
            },
            {
              templateId: 'flddY52zaUEJ9C8NH0sR42xH',
              hidden: true,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'viwtAUbvtYFuYxVed891weNX',
          name: {
            en: 'My tasks',
            'zh-CN': '我的任务',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fldVklHjEfqCoSVSQ1joFuSI',
                fieldType: 'MEMBER',
                clause: {
                  operator: 'Is',
                  value: ['Self'],
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldNBDizSEUJj2HsTKczH9Z4',
              hidden: false,
            },
            {
              templateId: 'fldxrqHnj5kc7lg6QSZoqnRd',
              hidden: false,
            },
            {
              templateId: 'fldjEcgbShY8UIgnNxHxWW9G',
              hidden: false,
            },
            {
              templateId: 'fldOm1Pfyyw2d0CLOaUF2s6p',
              hidden: false,
            },
            {
              templateId: 'fldeafXZvTNMko9Frq5EgA6f',
              hidden: false,
            },
            {
              templateId: 'fldVklHjEfqCoSVSQ1joFuSI',
              hidden: false,
            },
            {
              templateId: 'fldpcKvCMM0i1PpuwTHn6PHG',
              hidden: false,
            },
            {
              templateId: 'fldjZMCxYQlPzRZFiq49U4mm',
              hidden: false,
            },
            {
              templateId: 'fldzvyLxayWRM14sxOKUFJy6',
              hidden: false,
            },
            {
              templateId: 'fldr8SRhN44PI299ur7MPtMb',
              hidden: false,
            },
            {
              templateId: 'flddY52zaUEJ9C8NH0sR42xH',
              hidden: false,
            },
          ],
          groups: [],
        },
        {
          type: 'KANBAN',
          templateId: 'viw9ooN1Zo2a6SvlcVnhmwoc',
          name: {
            en: 'Person in charge Kanban',
            'zh-CN': '负责人看板',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fldVklHjEfqCoSVSQ1joFuSI',
                fieldType: 'MEMBER',
                clause: {
                  operator: 'Contains',
                  value: ['mebRWxonjD4NgNt0jcd9uY0o', 'meb8Eh1Z1WLHUoIhqGlmXFWL', 'mebKbYM1QqrLrM5y4R5HgPMJ'],
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldNBDizSEUJj2HsTKczH9Z4',
              hidden: false,
            },
            {
              templateId: 'fldxrqHnj5kc7lg6QSZoqnRd',
              hidden: false,
            },
            {
              templateId: 'fldjEcgbShY8UIgnNxHxWW9G',
              hidden: false,
            },
            {
              templateId: 'fldOm1Pfyyw2d0CLOaUF2s6p',
              hidden: false,
            },
            {
              templateId: 'fldeafXZvTNMko9Frq5EgA6f',
              hidden: false,
            },
            {
              templateId: 'fldVklHjEfqCoSVSQ1joFuSI',
              hidden: false,
            },
            {
              templateId: 'fldpcKvCMM0i1PpuwTHn6PHG',
              hidden: true,
            },
            {
              templateId: 'fldjZMCxYQlPzRZFiq49U4mm',
              hidden: true,
            },
            {
              templateId: 'fldzvyLxayWRM14sxOKUFJy6',
              hidden: true,
            },
            {
              templateId: 'fldr8SRhN44PI299ur7MPtMb',
              hidden: false,
            },
            {
              templateId: 'flddY52zaUEJ9C8NH0sR42xH',
              hidden: false,
            },
          ],
          groups: [],
          extra: {
            kanbanGroupingFieldTemplateId: 'fldVklHjEfqCoSVSQ1joFuSI',
            displayFieldName: true,
          },
        },
        {
          type: 'KANBAN',
          templateId: 'viwfDg1HSoUJPZ4z5vRA8NLc',
          name: {
            en: 'Progress board',
            'zh-CN': '进度看板',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldNBDizSEUJj2HsTKczH9Z4',
              hidden: false,
            },
            {
              templateId: 'fldxrqHnj5kc7lg6QSZoqnRd',
              hidden: false,
            },
            {
              templateId: 'fldjEcgbShY8UIgnNxHxWW9G',
              hidden: false,
            },
            {
              templateId: 'fldOm1Pfyyw2d0CLOaUF2s6p',
              hidden: false,
            },
            {
              templateId: 'fldeafXZvTNMko9Frq5EgA6f',
              hidden: false,
            },
            {
              templateId: 'fldVklHjEfqCoSVSQ1joFuSI',
              hidden: false,
            },
            {
              templateId: 'fldpcKvCMM0i1PpuwTHn6PHG',
              hidden: false,
            },
            {
              templateId: 'fldjZMCxYQlPzRZFiq49U4mm',
              hidden: false,
            },
            {
              templateId: 'fldzvyLxayWRM14sxOKUFJy6',
              hidden: false,
            },
            {
              templateId: 'fldr8SRhN44PI299ur7MPtMb',
              hidden: false,
            },
            {
              templateId: 'flddY52zaUEJ9C8NH0sR42xH',
              hidden: false,
            },
          ],
          groups: [],
          extra: {
            kanbanGroupingFieldTemplateId: 'fldr8SRhN44PI299ur7MPtMb',
            displayFieldName: true,
          },
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldNBDizSEUJj2HsTKczH9Z4',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Task name',
            'zh-CN': '任务名称',
          },
          required: true,
          primary: true,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldxrqHnj5kc7lg6QSZoqnRd',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Priority',
            'zh-CN': '重要程度',
          },
          required: true,
          property: {
            options: [
              {
                id: 'optzMVWJatXbh9fEIPwwbCZv',
                name: 'High priority',
                color: 'red5',
              },
              {
                id: 'opt2hZkA6FENr1VQ4z5hyrQa',
                name: 'Normal',
                color: 'tangerine5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldjEcgbShY8UIgnNxHxWW9G',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Start date',
            'zh-CN': '开始日期',
          },
          required: true,
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldOm1Pfyyw2d0CLOaUF2s6p',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Deadline',
            'zh-CN': '截止日期',
          },
          required: true,
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldeafXZvTNMko9Frq5EgA6f',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Remarks',
            'zh-CN': '备注',
          },
          primary: false,
        },
        {
          type: 'MEMBER',
          templateId: 'fldVklHjEfqCoSVSQ1joFuSI',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Mandate holders',
            'zh-CN': '任务执行人',
          },
          property: {},
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldpcKvCMM0i1PpuwTHn6PHG',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Actual completion date',
            'zh-CN': '实际完成日期',
          },
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldjZMCxYQlPzRZFiq49U4mm',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Latest Mission Progress Record',
            'zh-CN': '最新任务进展记录',
          },
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fldzvyLxayWRM14sxOKUFJy6',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Related information annex',
            'zh-CN': '相关资料附件',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldr8SRhN44PI299ur7MPtMb',
          privilege: 'FULL_EDIT',
          name: {
            'zh-CN': '进展',
            en: 'Progress',
          },
          property: {
            options: [
              {
                id: 'optLB5SskIDPEqhOsmtV7bIE',
                name: '🟠Not Started',
                color: 'tangerine5',
              },
              {
                id: 'optiCk9NDH6brTHWrWqi93Jn',
                name: '🟡In Progress',
                color: 'yellow5',
              },
              {
                id: 'optOTsv7PrXdyPqnbnJI8MQh',
                name: '🟢Completed',
                color: 'green5',
              },
              {
                id: 'optWHLaPeJNyI2kTE34x2ODp',
                name: '🔴Stopped',
                color: 'red5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'flddY52zaUEJ9C8NH0sR42xH',
          privilege: 'FULL_EDIT',
          name: {
            'zh-CN': '是否延期',
            en: 'Is delayed',
          },
          required: false,
          property: {
            expressionTemplate:
              'IF({fldpcKvCMM0i1PpuwTHn6PHG}>{fldOm1Pfyyw2d0CLOaUF2s6p},"🚨Delayed",\n' +
              'IF({fldr8SRhN44PI299ur7MPtMb}=“🔴Stopped"，“❌Abnormal“，”✅Normal")）',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recyP9JGn2yFemb24b9Fx9vr',
          data: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Development of new features',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-12-29T16:00:00.000Z',
            fldVklHjEfqCoSVSQ1joFuSI: ['meb8Eh1Z1WLHUoIhqGlmXFWL'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19T00:00:00.000Z',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['optLB5SskIDPEqhOsmtV7bIE'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['optzMVWJatXbh9fEIPwwbCZv'],
          },
          values: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Development of new features',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-12-29',
            fldVklHjEfqCoSVSQ1joFuSI: ['linxiaoxin'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['🟠Not Started'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['High priority'],
          },
        },
        {
          templateId: 'recViWZJusFhXcQoHh9lhD18',
          data: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Priority of known functional vulnerabilities',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-12-29T16:00:00.000Z',
            fldVklHjEfqCoSVSQ1joFuSI: ['meb8Eh1Z1WLHUoIhqGlmXFWL'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19T00:00:00.000Z',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['optLB5SskIDPEqhOsmtV7bIE'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['opt2hZkA6FENr1VQ4z5hyrQa'],
          },
          values: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Priority of known functional vulnerabilities',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-12-29',
            fldVklHjEfqCoSVSQ1joFuSI: ['linxiaoxin'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['🟠Not Started'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['Normal'],
          },
        },
        {
          templateId: 'recml8JCDAr2UrDxYwXLTefk',
          data: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Research on related functions in the industry',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-01-08T16:00:00.000Z',
            fldVklHjEfqCoSVSQ1joFuSI: ['meb8Eh1Z1WLHUoIhqGlmXFWL'],
            flddY52zaUEJ9C8NH0sR42xH: '🚨Delayed',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19T00:00:00.000Z',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldpcKvCMM0i1PpuwTHn6PHG: '2025-01-09T16:00:00.000Z',
            fldr8SRhN44PI299ur7MPtMb: ['optOTsv7PrXdyPqnbnJI8MQh'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['optzMVWJatXbh9fEIPwwbCZv'],
          },
          values: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Research on related functions in the industry',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-01-08',
            fldVklHjEfqCoSVSQ1joFuSI: ['linxiaoxin'],
            flddY52zaUEJ9C8NH0sR42xH: '🚨Delayed',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldpcKvCMM0i1PpuwTHn6PHG: '2025-01-09',
            fldr8SRhN44PI299ur7MPtMb: ['🟢Completed'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['High priority'],
          },
        },
        {
          templateId: 'rec3pOyxmQy2Y1rVNl1earHq',
          data: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Function testing and acceptance',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2024-12-29T00:00:00.000Z',
            fldVklHjEfqCoSVSQ1joFuSI: ['mebRWxonjD4NgNt0jcd9uY0o'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19T00:00:00.000Z',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldpcKvCMM0i1PpuwTHn6PHG: '2024-12-28T16:00:00.000Z',
            fldr8SRhN44PI299ur7MPtMb: ['optOTsv7PrXdyPqnbnJI8MQh'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['opt2hZkA6FENr1VQ4z5hyrQa'],
          },
          values: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Function testing and acceptance',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2024-12-29',
            fldVklHjEfqCoSVSQ1joFuSI: ['zhanpeiwei'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldpcKvCMM0i1PpuwTHn6PHG: '2024-12-28',
            fldr8SRhN44PI299ur7MPtMb: ['🟢Completed'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['Normal'],
          },
        },
        {
          templateId: 'recIc4ksfTFedxk9nAPNWOKN',
          data: {
            fldNBDizSEUJj2HsTKczH9Z4: 'New feature review',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2024-12-29T16:00:00.000Z',
            fldVklHjEfqCoSVSQ1joFuSI: ['mebRWxonjD4NgNt0jcd9uY0o'],
            flddY52zaUEJ9C8NH0sR42xH: '❌Abnormal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19T00:00:00.000Z',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['optWHLaPeJNyI2kTE34x2ODp'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['optzMVWJatXbh9fEIPwwbCZv'],
          },
          values: {
            fldNBDizSEUJj2HsTKczH9Z4: 'New feature review',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2024-12-29',
            fldVklHjEfqCoSVSQ1joFuSI: ['zhanpeiwei'],
            flddY52zaUEJ9C8NH0sR42xH: '❌Abnormal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['🔴Stopped'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['High priority'],
          },
        },
        {
          templateId: 'recWm1DyzLjkwlfw9zZLcEY8',
          data: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Function testing and acceptance',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2021-03-20T00:00:00.000Z',
            fldVklHjEfqCoSVSQ1joFuSI: ['mebRWxonjD4NgNt0jcd9uY0o'],
            flddY52zaUEJ9C8NH0sR42xH: '🚨Delayed',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2021-03-15T00:00:00.000Z',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldpcKvCMM0i1PpuwTHn6PHG: '2021-03-30T16:00:00.000Z',
            fldr8SRhN44PI299ur7MPtMb: ['optOTsv7PrXdyPqnbnJI8MQh'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['opt2hZkA6FENr1VQ4z5hyrQa'],
          },
          values: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Function testing and acceptance',
            fldVklHjEfqCoSVSQ1joFuSI: ['zhanpeiwei'],
            flddY52zaUEJ9C8NH0sR42xH: '🚨Delayed',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldpcKvCMM0i1PpuwTHn6PHG: '2021-03-30',
            fldr8SRhN44PI299ur7MPtMb: ['🟢Completed'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['Normal'],
          },
        },
        {
          templateId: 'recbk7JDsJisxWo09mGhvd2Q',
          data: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Invite users to participate in community discussions',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-01-08T16:00:00.000Z',
            fldVklHjEfqCoSVSQ1joFuSI: ['mebRWxonjD4NgNt0jcd9uY0o'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19T00:00:00.000Z',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['optiCk9NDH6brTHWrWqi93Jn'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['opt2hZkA6FENr1VQ4z5hyrQa'],
          },
          values: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Invite users to participate in community discussions',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-01-08',
            fldVklHjEfqCoSVSQ1joFuSI: ['zhanpeiwei'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['🟡In Progress'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['Normal'],
          },
        },
        {
          templateId: 'recSGxsqSJl1gko7bQa9RPzW',
          data: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Integrate feedback data and prioritize',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2024-12-29T16:00:00.000Z',
            fldVklHjEfqCoSVSQ1joFuSI: ['mebKbYM1QqrLrM5y4R5HgPMJ'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19T00:00:00.000Z',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['optiCk9NDH6brTHWrWqi93Jn'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['optzMVWJatXbh9fEIPwwbCZv'],
          },
          values: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Integrate feedback data and prioritize',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2024-12-29',
            fldVklHjEfqCoSVSQ1joFuSI: ['tianlu'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['🟡In Progress'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['High priority'],
          },
        },
        {
          templateId: 'recdEkHe5JFqz7RWWEMINERG',
          data: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Design new features',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-01-30T16:00:00.000Z',
            fldVklHjEfqCoSVSQ1joFuSI: ['mebKbYM1QqrLrM5y4R5HgPMJ'],
            flddY52zaUEJ9C8NH0sR42xH: '❌Abnormal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2025-01-06T00:00:00.000Z',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['optWHLaPeJNyI2kTE34x2ODp'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['opt2hZkA6FENr1VQ4z5hyrQa'],
          },
          values: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Design new features',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-01-30',
            fldVklHjEfqCoSVSQ1joFuSI: ['tianlu'],
            flddY52zaUEJ9C8NH0sR42xH: '❌Abnormal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2025-01-06',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['🔴Stopped'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['Normal'],
          },
        },
        {
          templateId: 'recqAeOO3WZI4chADxpCETWv',
          data: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Invite users to join the beta test',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-01-08T16:00:00.000Z',
            fldVklHjEfqCoSVSQ1joFuSI: ['mebKbYM1QqrLrM5y4R5HgPMJ'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19T00:00:00.000Z',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldpcKvCMM0i1PpuwTHn6PHG: '2024-12-19T16:00:00.000Z',
            fldr8SRhN44PI299ur7MPtMb: ['optOTsv7PrXdyPqnbnJI8MQh'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['optzMVWJatXbh9fEIPwwbCZv'],
          },
          values: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Invite users to join the beta test',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-01-08',
            fldVklHjEfqCoSVSQ1joFuSI: ['tianlu'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldpcKvCMM0i1PpuwTHn6PHG: '2024-12-19',
            fldr8SRhN44PI299ur7MPtMb: ['🟢Completed'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['High priority'],
          },
        },
        {
          templateId: 'receIq1VSM2fMAM62EO8JD9s',
          data: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Collect user feedback through multiple channels',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-12-29T16:00:00.000Z',
            fldVklHjEfqCoSVSQ1joFuSI: ['mebKbYM1QqrLrM5y4R5HgPMJ'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19T00:00:00.000Z',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['optLB5SskIDPEqhOsmtV7bIE'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['opt2hZkA6FENr1VQ4z5hyrQa'],
          },
          values: {
            fldNBDizSEUJj2HsTKczH9Z4: 'Collect user feedback through multiple channels',
            fldOm1Pfyyw2d0CLOaUF2s6p: '2025-12-29',
            fldVklHjEfqCoSVSQ1joFuSI: ['tianlu'],
            flddY52zaUEJ9C8NH0sR42xH: '✅Normal',
            fldeafXZvTNMko9Frq5EgA6f: 'Enter remarks...',
            fldjEcgbShY8UIgnNxHxWW9G: '2024-03-19',
            fldjZMCxYQlPzRZFiq49U4mm: 'Describe the latest progress of the task...',
            fldr8SRhN44PI299ur7MPtMb: ['🟠Not Started'],
            fldxrqHnj5kc7lg6QSZoqnRd: ['Normal'],
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
