import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  schemaVersion: 'v1',
  author: '<PERSON> <<EMAIL>>',
  templateId: 'send-emails-in-bulk',
  visibility: 'PUBLIC',
  name: {
    en: 'Send Emails in Bulk',
    'zh-CN': '批量发送邮件',
    'zh-TW': '批量發送郵件',
    ja: '一括メール送信',
  },
  cover: '/assets/template/template-cover-send-emails-in-bulk.png',
  description: {
    'zh-CN':
      '超级简单易用的批量发送邮件功能，一张直观的数据表格，录入邮件地址或通过表单收集，触发即可批量发送，更可看到打开率、回复率',
    en: 'Super easy-to-use bulk email sending function, an intuitive data table, enter email addresses or collect them through forms, trigger to send in bulk, and see open rates and reply rates',
    'zh-TW':
      '超級簡單易用的批量發送郵件功能，一張直觀的數據表格，錄入郵件地址或通過表單收集，觸發即可批量發送，更可看到打開率、回復率',
    ja: '超簡単に使用できる一括メール送信機能、直感的なデータテーブル、メールアドレスを入力するかフォームを通じて収集し、トリガーを送信して一括送信し、開封率と返信率を確認できます',
  },
  version: '0.1.4',
  category: ['automation', 'integration'],
  keywords: {
    'zh-CN': '邮件, 批量发送, 邮箱, Excel, 表单, 打开率, 回复率, 营销邮件',
    en: 'email, bulk send, mailbox, Excel, form, open rate, reply rate, marketing email',
    'zh-TW': '郵件, 批量發送, 郵箱, Excel, 表單, 打開率, 回復率, 行銷郵件',
    ja: 'メール, 一括送信, メールボックス, Excel, フォーム, 開封率, 返信率, マーケティングメール',
  },
  personas: {
    'zh-CN': '市场营销人员, 产品经理, 运营人员, 销售人员, 客服人员',
    en: 'Marketers, Product Managers, Operations, Sales, Customer Service',
    'zh-TW': '市場營銷人員, 產品經理, 運營人員, 銷售人員, 客服人員',
    ja: 'マーケター, プロダクトマネージャー, オペレーション, セールス, カスタマーサービス',
  },
  useCases: {
    'zh-CN':
      '批量发送邮件, 营销邮件, 客户服务, 通知邮件, 提高客户参与度, 增强客户关系, 自动化邮件发送, 提高邮件打开率, 提高邮件回复率, 通过邮件推广产品, 提高客户满意度, 提高客户保留率, 提高销售额, 提高品牌知名度, 提高客户忠诚度, 提高客户互动, 提高客户转化率, 提高客户体验, 提高客户反馈, 提高客户支持, 提高客户沟通, 提高客户管理, 提高客户服务, 提高客户满意度, 提高客户保留率, 提高客户忠诚度, 提高客户互动, 提高客户转化率, 提高客户体验, 提高客户反馈, 提高客户支持',
    'zh-TW':
      '批量發送郵件, 行銷郵件, 客戶服務, 通知郵件, 提高客戶參與度, 增強客戶關係, 自動化郵件發送, 提高郵件打開率, 提高郵件回復率, 通過郵件推廣產品, 提高客戶滿意度, 提高客戶保留率, 提高銷售額, 提高品牌知名度, 提高客戶忠誠度, 提高客戶互動, 提高客戶轉化率, 提高客戶體驗, 提高客戶反饋, 提高客戶支持, 提高客戶溝通, 提高客戶管理, 提高客戶服務, 提高客戶滿意度, 提高客戶保留率, 提高客戶忠誠度, 提高客戶互動, 提高客戶轉化率, 提高客戶體驗, 提高客戶反饋, 提高客戶支持',
    en: 'Bulk email sending, marketing email, customer service, notification email, Enhance customer engagement, Strengthen customer relationships, Automate email sending, Increase email open rates, Increase email reply rates, Promote products via email, Improve customer satisfaction, Increase customer retention, Boost sales, Enhance brand awareness, Increase customer loyalty, Improve customer interaction, Increase customer conversion rates, Enhance customer experience, Improve customer feedback, Strengthen customer support, Improve customer communication, Enhance customer management, Improve customer service, Increase customer satisfaction, Increase customer retention, Enhance customer loyalty, Improve customer interaction, Increase customer conversion rates, Enhance customer experience, Improve customer feedback, Strengthen customer support',
    ja: '一括メール送信, マーケティングメール, カスタマーサービス, 通知メール, 顧客エンゲージメントの向上, 顧客関係の強化, メール送信の自動化, メール開封率の向上, メール返信率の向上, メールによる製品のプロモーション, 顧客満足度の向上, 顧客維持率の向上, 売上の向上, ブランド認知度の向上, 顧客ロイヤルティの向上, 顧客とのインタラクションの向上, 顧客転換率の向上, 顧客体験の向上, 顧客フィードバックの向上, 顧客サポートの強化, 顧客コミュニケーションの向上, 顧客管理の強化, 顧客サービスの向上, 顧客満足度の向上, 顧客維持率の向上, 顧客ロイヤルティの向上, 顧客とのインタラクションの向上, 顧客転換率の向上, 顧客体験の向上, 顧客フィードバックの向上, 顧客サポートの強化',
  },
  initMissions: [
    {
      name: {
        'zh-CN': '批量发送邮件模板使用须知',
        'zh-TW': '批量發送郵件模板使用須知',
        ja: '一括メール送信テンプレートの使用方法',
        en: 'Instructions for bulk email sending template',
      },
      type: 'READ_TEMPLATE_README',
      templateId: 'send-emails-in-bulk',
      time: 10,
      beforeText: {
        title: {
          'zh-CN': '恭喜您，模板已经安装完毕',
          'zh-TW': '恭喜您，模板已經安裝完畢',
          ja: 'おめでとうございます、テンプレートのインストールが完了しました',
          en: 'Congratulations, the template has been installed',
        },
        description: {
          'zh-CN': '在开始使用之前，请阅读以下使用须知，以便更好地了解如何使用此模板',
          'zh-TW': '在開始使用之前，請閱讀以下使用須知，以便更好地了解如何使用此模板',
          ja: '使用を開始する前に、以下の使用方法をお読みいただき、このテンプレートの使用方法をよりよく理解してください',
          en: 'Before you start using it, please read the following instructions to better understand how to use this template',
        },
      },
      assignType: 'DEDICATED',
      forcePopup: true,
      wizardGuideId: 'COMMON_MY_TODO_TUTORIAL',
      redirect: {
        type: 'MY_MISSIONS',
      },
      to: [
        {
          type: 'CURRENT_OPERATOR',
        },
      ],
    },
    {
      name: {
        'zh-CN': '💡批量发送模板初始化任务 #1：添加测试收件人',
        'zh-TW': '💡批量發送模板初始化任務 #1：添加測試收件人',
        ja: '💡一括送信テンプレート初期化タスク #1：テスト受信者を追加',
        en: '💡Bulk send template initialization task #1: Add test recipients',
      },
      description: {
        'zh-CN': `任务描述：
请添加测试收件人，以便测试邮件发送功能，建议使用自己和团队成员的邮箱地址作为测试收件人

任务步骤：
1. 进入“收件人列表”节点，在邮件字段修改或添加需要测试的收件人邮件地址`,
        'zh-TW': `任務描述：

請添加測試收件人，以便測試郵件發送功能，建議使用自己和團隊成員的郵箱地址作為測試收件人

任務步驟：
1. 進入“收件人列表”節點，在郵件字段修改或添加需要測試的收件人郵件地址`,
        ja: `タスクの説明：

テスト受信者を追加して、メール送信機能をテストしてください。 テスト受信者として、自分とチームメンバーのメールアドレスを使用することをお勧めします。

タスクの手順：
1.「受信者リスト」ノードに移動し、メールフィールドでテスト受信者のメールアドレスを変更または追加します`,
        en: `Task description:
Please add test recipients to test the email sending function. It is recommended to use your own and team members' email addresses as test recipients.

Task steps:
1. Go to the "Emails Database" node and modify or add the email addresses of the recipients to be tested in the email field`,
      },
      type: 'REDIRECT_SPACE_NODE',
      nodeTemplateId: 'emails_list',
      buttonText: {
        'zh-CN': '添加收件人',
        'zh-TW': '添加收件人',
        ja: '受信者を追加',
        en: 'Add recipients',
      },
      assignType: 'SHARE',
      to: [
        {
          type: 'CURRENT_OPERATOR',
        },
      ],
      canCompleteManually: true,
    },
    {
      name: {
        'zh-CN': '💡批量发送模板初始化任务 #2：手工触发批量发送邮件',
        'zh-TW': '💡批量發送模板初始化任務 #2：手工觸發批量發送郵件',
        ja: '💡一括送信テンプレート初期化タスク #2：手動で一括メール送信',
        en: '💡Bulk send template initialization task #2: Manually trigger bulk email sending',
      },
      description: {
        'zh-CN': `任务描述：
如果已经添加了用于测试的收件人，现在可以尝试测试和学习如何批量发送邮件

任务步骤：
1. 进入“手工触发批量发送邮件”自动化节点
2. 点击“批量发送邮件”的步骤，检查发送邮件的相关配置，如有需要可修改邮件主题和内容
3. 点击自动化页面底部的“手工触发”按钮，触发批量发送邮件`,
        'zh-TW': `任務描述：
如果已經添加了用於測試的收件人，現在可以嘗試測試和學習如何批量發送郵件

任務步驟：
1. 進入“手工觸發批量發送郵件”自動化節點
2. 點擊“批量發送郵件”的步驟，檢查發送郵件的相關配置，如有需要可修改郵件主題和內容
3. 點擊自動化頁面底部的“手工觸發”按鈕，觸發批量發送郵件`,
        ja: `タスクの説明：
テスト用の受信者を追加した場合、今すぐメール送信をテストして学習することができます

タスクの手順：
1.「手動で一括メール送信」自動化ノードに移動します
2.「一括メール送信」のステップをクリックして、メールの送信に関連する設定を確認し、必要に応じてメールの件名と内容を変更します
3. 自動化ページの下部にある「手動トリガー」ボタンをクリックして、一括メール送信をトリガーします`,
        en: `Task description:
If you have added recipients for testing, you can now try to test and learn how to send emails in bulk

Task steps:
1. Go to the "Do bulk email sending" automation node
2. Click the "Send emails in bulk" step to check the relevant configuration for sending emails. If necessary, modify the email subject and content
3. Click the "Manually trigger" button at the bottom of the automation page to trigger bulk email sending`,
      },
      type: 'REDIRECT_SPACE_NODE',
      nodeTemplateId: 'manual_trigger_send_emails_in_bulk',
      buttonText: {
        'zh-CN': '测试发送邮件',
        'zh-TW': '測試發送郵件',
        ja: 'メール送信をテスト',
        en: 'Test send email',
      },
      assignType: 'SHARE',
      to: [
        {
          type: 'CURRENT_OPERATOR',
        },
      ],
      canCompleteManually: true,
    },
  ],
  resources: [
    {
      resourceType: 'DATABASE',
      templateId: 'emails_list',
      name: {
        en: 'Email Database',
        ja: '受信者リスト',
        'zh-CN': '收件人列表',
        'zh-TW': '收件人列表',
      },
      description: {
        en: 'A complete list of emails for bulk sending, which can be added through Excel import, manual input, form, API interface, data synchronization, etc.',
        ja: '一括送信用のメールリスト全体、Excelインポート、手動入力、フォーム、APIインターフェース、データ同期などを介して追加できます',
        'zh-CN': '用于批量发送的邮件列表大全，可通过Excel导入、手工录入、表单录入、API接口、数据同步等方式添加',
        'zh-TW': '用於批量發送的郵件列表大全，可通過Excel導入、手工錄入、表單錄入、API接口、數據同步等方式添加',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'emails_list_all',
          name: {
            en: 'All',
            ja: 'すべて',
            'zh-CN': '全部',
            'zh-TW': '全部',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'name',
              hidden: false,
            },
            {
              templateId: 'email',
              hidden: false,
              width: 380,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'name',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Recipient Name',
            ja: '受信者名',
            'zh-CN': '收件人名称',
            'zh-TW': '收件人名稱',
          },
          primary: true,
        },
        {
          type: 'EMAIL',
          templateId: 'email',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Recipient Email',
            ja: '受信者メールアドレス',
            'zh-CN': '收件人邮箱',
            'zh-TW': '收件人郵箱',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recg144qxZX6R2CRHw06iLBL',
          data: {
            name: 'Third test name',
            email: '<EMAIL>',
          },
          values: {
            name: 'Third test name',
            email: '<EMAIL>',
          },
        },
        {
          templateId: 'recCY8p2js1qh1KrEH90s8ZG',
          data: {
            name: 'Second test name',
            email: '<EMAIL>',
          },
          values: {
            name: 'Second test name',
            email: '<EMAIL>',
          },
        },
        {
          templateId: 'rec7uVRoF3WvwnNIm1zSfL8r',
          data: {
            name: 'First test name',
            email: '<EMAIL>',
          },
          values: {
            name: 'First test name',
            email: '<EMAIL>',
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'manual_trigger_send_emails_in_bulk',
      name: {
        en: 'Do bulk email sending',
        ja: '手動で一括メール送信',
        'zh-CN': '手工触发批量发送邮件',
        'zh-TW': '手工觸發批量發送郵件',
      },
      description: {
        en: 'Do bulk email sending',
        ja: '手動で一括メール送信',
        'zh-CN': '手工触发批量发送邮件',
        'zh-TW': '手工觸發批量發送郵件',
      },
      triggers: [
        {
          triggerType: 'MANUALLY',
          templateId: 'trigger_send_emails_in_bulk',
          description: {
            en: 'Manually trigger',
            ja: '手動トリガー',
            'zh-CN': '手工触发',
            'zh-TW': '手工觸發',
          },
        },
      ],
      actions: [
        {
          templateId: 'actASPU73gR9d0u0vMesFet4',
          description: 'Find all the records from a specific view',
          actionType: 'FIND_RECORDS',
          input: {
            type: 'DATABASE_VIEW',
            viewTemplateId: 'emails_list_all',
            databaseTemplateId: 'emails_list',
          },
        },
        {
          templateId: 'actpoLpiDwdQ9DEZkAH07lFf',
          description: 'Execute sequentially',
          actionType: 'LOOP',
          input: {
            type: 'PREV_ACTION',
            actionTemplateId: 'actASPU73gR9d0u0vMesFet4',
            path: 'records',
          },
          actions: [
            {
              description: 'Send emails in bulk',
              actionType: 'SEND_EMAIL',
              id: 'act1m6sjhWI7Ebff2JSCH9wq',
              input: {
                subject: 'Hi <%= _item.cells.name.data %>, Welcome to this Bika.ai email template',
                body: {
                  markdown:
                    'Hi <%= _item.cells.name.data %>,\n' +
                    '\n' +
                    'I am glad you are exploring this template. Please feel free to modify the content as needed to ensure a pleasant experience with bulk email sending.\n' +
                    '\n' +
                    'Kind regards,\n' +
                    'Bika.ai Team',
                  json: {
                    type: 'doc',
                    content: [
                      {
                        type: 'paragraph',
                        content: [
                          {
                            text: 'Hi ',
                            type: 'text',
                          },
                          {
                            type: 'variable',
                            attrs: {
                              ids: ['_item', 'cells', 'name', 'data'],
                              tips: '',
                              names: ['Item', 'Cells', 'Recipient Name', 'text'],
                            },
                          },
                          {
                            text: ',',
                            type: 'text',
                          },
                        ],
                      },
                      {
                        type: 'paragraph',
                      },
                      {
                        type: 'paragraph',
                        content: [
                          {
                            text: 'I am glad you are exploring this template. Please feel free to modify the content as needed to ensure a pleasant experience with bulk email sending.',
                            type: 'text',
                          },
                        ],
                      },
                      {
                        type: 'paragraph',
                      },
                      {
                        type: 'paragraph',
                        content: [
                          {
                            text: 'Kind regards,',
                            type: 'text',
                          },
                        ],
                      },
                      {
                        type: 'paragraph',
                        content: [
                          {
                            text: 'Bika.ai Team',
                            type: 'text',
                          },
                        ],
                      },
                    ],
                  },
                },
                to: [
                  {
                    type: 'EMAIL_STRING',
                    email: '<%= _item.cells.email.data %>',
                  },
                ],
                senderName: '',
                cc: [],
                bcc: [],
                replyTo: [],
                type: 'SERVICE',
              },
            },
          ],
        },
      ],
    },
  ],
};

export default template;
