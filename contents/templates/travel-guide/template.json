{"templateId": "travel-guide", "name": {"en": "Travel Guide", "ja": "旅行ガイド", "zh-CN": "旅行指南", "zh-TW": "旅遊指南"}, "description": {"zh-CN": "该模板是一款全面的旅行行程管理工具，包含多个功能丰富的表格和仪表板，用于记录和管理旅伴信息、行前准备、酒店安排、景点资讯以及行程规划。具备多种视图和数据关联功能，用户可高效完成出行前的准备工作与行程管理，提升整体旅行体验。", "en": "This template is a comprehensive travel itinerary management tool, containing multiple feature-rich tables and dashboards for recording and managing travel companions, trip preparations, hotel arrangements, attraction information, and itinerary planning. With various views and data association capabilities, users can efficiently complete pre-trip preparations and itinerary management, enhancing the overall travel experience.", "ja": "このテンプレートは包括的な旅行行程管理ツールで、旅行仲間の情報、出発前の準備、ホテル手配、観光スポット情報、旅程計画を記録・管理するための機能豊富な複数のテーブルとダッシュボードが含まれています。多様なビューとデータ連携機能を備えており、ユーザーは効率的に出発前の準備と旅程管理を完了でき、旅行体験全体を向上させることができます。", "zh-TW": "該模板是一款全面的旅行行程管理工具，包含多個功能豐富的表格和儀表板，用於記錄和管理旅伴資訊、行前準備、酒店安排、景點資訊以及行程規劃。具備多種視圖和數據關聯功能，用戶可高效完成出行前的準備工作與行程管理，提升整體旅行體驗。"}, "cover": "/assets/template/travel-guide/travel-guide.png", "author": "pengjin <<EMAIL>>", "category": ["project"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.11", "resources": [{"resourceType": "DATABASE", "templateId": "datPMggKP4lSbeFiQJ5nRHkJ", "name": {"en": "Participant Information", "ja": "りょこうしゃじょうほう", "zh-CN": "旅客信息", "zh-TW": "旅客資訊"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwpgU8SAP18rbb9WrFUgjO4", "name": {"en": "ALL", "ja": "すべて", "zh-CN": "全部", "zh-TW": "全部"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldOcoguAK7gLw9oi1ASLpYl", "hidden": false}, {"templateId": "fldliliFdGEhdh4NRfTKSzfU", "hidden": false}, {"templateId": "fld2JMnh1jaM1W3ZMtmRBygn", "hidden": false}, {"templateId": "fld9Qb7XnoBoat3RpOAJmdpE", "hidden": false}, {"templateId": "fld8wMeuz6nxEnsXMKey0jpd", "hidden": false}, {"templateId": "fldpKGO2HNS6O8lzCsy8QGeq", "hidden": false}, {"templateId": "fld0haKFaHrcBpeBUkVjBz5x", "hidden": true}]}, {"type": "KANBAN", "templateId": "viwgRMfFOqAGjPvwuZs3JUjn", "name": {"en": "Kanban view", "ja": "カンバンビュー", "zh-CN": "看板视图", "zh-TW": "看板視圖"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldOcoguAK7gLw9oi1ASLpYl", "hidden": false}, {"templateId": "fldliliFdGEhdh4NRfTKSzfU", "hidden": false}, {"templateId": "fld2JMnh1jaM1W3ZMtmRBygn", "hidden": false}, {"templateId": "fld9Qb7XnoBoat3RpOAJmdpE", "hidden": false}, {"templateId": "fld8wMeuz6nxEnsXMKey0jpd", "hidden": false}, {"templateId": "fldpKGO2HNS6O8lzCsy8QGeq", "hidden": true}, {"templateId": "fld0haKFaHrcBpeBUkVjBz5x", "hidden": true}], "groups": [], "extra": {"kanbanGroupingFieldTemplateId": "fld2JMnh1jaM1W3ZMtmRBygn", "displayFieldName": true}}, {"type": "GALLERY", "templateId": "viwNUO2SCUSsJkyBdmgO65YB", "name": {"en": "Gallery", "ja": "ギャラリービュー", "zh-CN": "相册视图", "zh-TW": "畫廊視圖"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldOcoguAK7gLw9oi1ASLpYl", "hidden": false}, {"templateId": "fldliliFdGEhdh4NRfTKSzfU", "hidden": false}, {"templateId": "fld2JMnh1jaM1W3ZMtmRBygn", "hidden": false}, {"templateId": "fld9Qb7XnoBoat3RpOAJmdpE", "hidden": false}, {"templateId": "fld8wMeuz6nxEnsXMKey0jpd", "hidden": false}, {"templateId": "fldpKGO2HNS6O8lzCsy8QGeq", "hidden": false}, {"templateId": "fld0haKFaHrcBpeBUkVjBz5x", "hidden": false}], "groups": [], "extra": {"coverFieldTemplateId": "fld8wMeuz6nxEnsXMKey0jpd", "coverStretch": "FILL", "displayFieldName": true}}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldOcoguAK7gLw9oi1ASLpYl", "privilege": "TYPE_EDIT", "name": {"en": "Name", "ja": "名前", "zh-CN": "姓名", "zh-TW": "姓名"}, "primary": true}, {"type": "SINGLE_SELECT", "templateId": "fldliliFdGEhdh4NRfTKSzfU", "privilege": "NAME_EDIT", "name": {"en": "Gender", "ja": "性別", "zh-CN": "性别", "zh-TW": "性別"}, "property": {"options": [{"id": "opthzUgqcP6rkIZ9zsGVBPEj", "name": "Female", "color": "deepPurple"}, {"id": "optoImZ86LsIzxgWlACGdmfg", "name": "Male", "color": "indigo"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fld2JMnh1jaM1W3ZMtmRBygn", "privilege": "NAME_EDIT", "name": {"en": "Role", "ja": "役割", "zh-CN": "角色", "zh-TW": "角色"}, "property": {"options": [{"id": "opttTPpDfrDNNn9G8V4Rjmlv", "name": "Team Member", "color": "deepPurple"}, {"id": "optRr18w9xGjkKTvIxJ1x4ak", "name": "Tour Guide", "color": "indigo"}, {"id": "optCUlFMOJgJKJ1ezXRPh8lf", "name": "Hotel Liaison", "color": "blue"}], "defaultValue": ""}, "primary": false}, {"type": "EMAIL", "templateId": "fld9Qb7XnoBoat3RpOAJmdpE", "privilege": "NAME_EDIT", "name": {"en": "Email", "ja": "メールアドレス", "zh-CN": "电子邮箱", "zh-TW": "電子郵件"}, "primary": false}, {"type": "ATTACHMENT", "templateId": "fld8wMeuz6nxEnsXMKey0jpd", "privilege": "NAME_EDIT", "name": {"en": "Photo", "ja": "写真", "zh-CN": "照片", "zh-TW": "照片"}, "primary": false}, {"type": "PHONE", "templateId": "fldpKGO2HNS6O8lzCsy8QGeq", "privilege": "NAME_EDIT", "name": {"en": "Contact Number", "ja": "連絡先番号", "zh-CN": "联系电话", "zh-TW": "聯絡電話"}, "primary": false}, {"type": "LINK", "templateId": "fld0haKFaHrcBpeBUkVjBz5x", "privilege": "NAME_EDIT", "name": "Itinerary", "property": {"foreignDatabaseTemplateId": "datZD6cVHdwjnB0pD29vujPT", "brotherFieldTemplateId": "fld3qJjukDuPhVRHf1mdQpx1"}, "primary": false}], "records": [{"templateId": "recVERA0WbrcLgubCerylBgi", "data": {"fld0haKFaHrcBpeBUkVjBz5x": ["rec0hSbD1yC4MpuhEqzSI4GW"], "fld2JMnh1jaM1W3ZMtmRBygn": ["optRr18w9xGjkKTvIxJ1x4ak"], "fld8wMeuz6nxEnsXMKey0jpd": [{"id": "tplattGdHcl2NggRvZ3Zhbf8Iv4", "name": "编组 57.png", "path": "template/tplattGdHcl2NggRvZ3Zhbf8Iv4.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 45101}], "fld9Qb7XnoBoat3RpOAJmdpE": "<EMAIL>", "fldOcoguAK7gLw9oi1ASLpYl": "<PERSON>", "fldliliFdGEhdh4NRfTKSzfU": ["opthzUgqcP6rkIZ9zsGVBPEj"], "fldpKGO2HNS6O8lzCsy8QGeq": "8820190626"}, "values": {"fld0haKFaHrcBpeBUkVjBz5x": ["College Street"], "fld2JMnh1jaM1W3ZMtmRBygn": ["Tour Guide"], "fld8wMeuz6nxEnsXMKey0jpd": ["编组 57.png"], "fldliliFdGEhdh4NRfTKSzfU": ["Female"], "fldpKGO2HNS6O8lzCsy8QGeq": "8820190626"}}, {"templateId": "recJpIKdTMlKKpVMyGhH8xV2", "data": {"fld0haKFaHrcBpeBUkVjBz5x": ["recnUx1LrT7McWF8Uiev52pz", "recJs1NeS4zMq9ccKguTtBbC"], "fld2JMnh1jaM1W3ZMtmRBygn": ["optCUlFMOJgJKJ1ezXRPh8lf"], "fld8wMeuz6nxEnsXMKey0jpd": [{"id": "tplatt81BI7IbiZWI9EeXvyWWOC", "name": "编组 56.png", "path": "template/tplatt81BI7IbiZWI9EeXvyWWOC.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 45300}], "fld9Qb7XnoBoat3RpOAJmdpE": "<EMAIL>", "fldOcoguAK7gLw9oi1ASLpYl": "<PERSON>", "fldliliFdGEhdh4NRfTKSzfU": ["optoImZ86LsIzxgWlACGdmfg"], "fldpKGO2HNS6O8lzCsy8QGeq": "8820190626"}, "values": {"fld0haKFaHrcBpeBUkVjBz5x": ["Coffee Prince No.1", " Myeongdong Cathedral"], "fld2JMnh1jaM1W3ZMtmRBygn": ["Hotel Liaison"], "fld8wMeuz6nxEnsXMKey0jpd": ["编组 56.png"], "fldliliFdGEhdh4NRfTKSzfU": ["Male"], "fldpKGO2HNS6O8lzCsy8QGeq": "8820190626"}}, {"templateId": "recf0wmmUpHbVGaTEDZcNoLp", "data": {"fld0haKFaHrcBpeBUkVjBz5x": ["recCZrM9RYafLJDfgxXYZiPE"], "fld2JMnh1jaM1W3ZMtmRBygn": ["opttTPpDfrDNNn9G8V4Rjmlv"], "fld8wMeuz6nxEnsXMKey0jpd": [{"id": "tplattVMuw82C0y4cQiGJlnaspk", "name": "编组 55.png", "path": "template/tplattVMuw82C0y4cQiGJlnaspk.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 47105}], "fld9Qb7XnoBoat3RpOAJmdpE": "<EMAIL>", "fldOcoguAK7gLw9oi1ASLpYl": "<PERSON>", "fldliliFdGEhdh4NRfTKSzfU": ["opthzUgqcP6rkIZ9zsGVBPEj"], "fldpKGO2HNS6O8lzCsy8QGeq": "12344556"}, "values": {"fld0haKFaHrcBpeBUkVjBz5x": ["Hongdae Free Market"], "fld2JMnh1jaM1W3ZMtmRBygn": ["Team Member"], "fld8wMeuz6nxEnsXMKey0jpd": ["编组 55.png"], "fldliliFdGEhdh4NRfTKSzfU": ["Female"], "fldpKGO2HNS6O8lzCsy8QGeq": "12344556"}}]}, {"resourceType": "DATABASE", "templateId": "datW0Ba1JwjjenWbXHDE3Buw", "name": {"en": "Pre-Trip Checklist", "ja": "チェックリスト", "zh-CN": "清单列表", "zh-TW": "清單列表"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwOJAIsFDUetLU6yYyZRLFG", "name": {"en": "<PERSON><PERSON><PERSON>", "ja": "すべて", "zh-CN": "全部", "zh-TW": "全部"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldurqJnc67X5vipMDiafCEg", "hidden": false, "width": 358}, {"templateId": "fldUSE1DOLbCdnbpHAN18Mbs", "hidden": true}, {"templateId": "fldExAtfyhNnG6EVupR6fC67", "hidden": false, "width": 150}], "groups": [{"fieldTemplateId": "fldExAtfyhNnG6EVupR6fC67", "asc": true}]}, {"type": "TABLE", "templateId": "viwtAtG3Roqoktf7IoW28o6X", "name": {"en": "To-Do", "ja": "やること", "zh-CN": "待办事项", "zh-TW": "待辦事項"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldUSE1DOLbCdnbpHAN18Mbs", "fieldType": "CHECKBOX", "clause": {"operator": "Is", "value": false}}]}, "sorts": [], "fields": [{"templateId": "fldurqJnc67X5vipMDiafCEg", "hidden": false}, {"templateId": "fldUSE1DOLbCdnbpHAN18Mbs", "hidden": false}, {"templateId": "fldExAtfyhNnG6EVupR6fC67", "hidden": false, "width": 150}], "groups": [{"fieldTemplateId": "fldExAtfyhNnG6EVupR6fC67", "asc": true}]}, {"type": "TABLE", "templateId": "viwHqqJ6Tjs2dICfvjsmVWl3", "name": {"en": "Overview of the List", "ja": "リストの概要", "zh-CN": "清单概览", "zh-TW": "清單總覽"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldurqJnc67X5vipMDiafCEg", "hidden": false}, {"templateId": "fldUSE1DOLbCdnbpHAN18Mbs", "hidden": false}, {"templateId": "fldExAtfyhNnG6EVupR6fC67", "hidden": false}], "groups": []}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldurqJnc67X5vipMDiafCEg", "privilege": "TYPE_EDIT", "name": {"en": "Event", "ja": "イベント", "zh-CN": "事件", "zh-TW": "事件"}, "primary": true}, {"type": "CHECKBOX", "templateId": "fldUSE1DOLbCdnbpHAN18Mbs", "privilege": "NAME_EDIT", "name": {"en": "Completed", "ja": "かんりょう", "zh-CN": "已完成", "zh-TW": "已完成"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldExAtfyhNnG6EVupR6fC67", "privilege": "NAME_EDIT", "name": {"en": "Task Category", "ja": "タスクカテゴリ", "zh-CN": "任务类别", "zh-TW": "任務類別"}, "property": {"options": [{"id": "optwD9GC6YZjVfGtZadq0STl", "name": "To-Do", "color": "deepPurple"}, {"id": "optPhvbgJ6jF4cuFhtb8Fixw", "name": "Personal Belongings", "color": "indigo"}, {"id": "opt0CC3NEcYNosykd4dYVv9F", "name": "Luggage", "color": "blue"}, {"id": "optUEp6DvgNsAXZa5X5x3HgP", "name": " Toiletries", "color": "teal"}, {"id": "optH4ow2A2xnUgo578xeXBtE", "name": " Electronic Devices", "color": "green"}], "defaultValue": ""}, "primary": false}], "records": [{"templateId": "recmAgBjY2kJUWYnRyfdzIMc", "data": {"fldExAtfyhNnG6EVupR6fC67": ["optwD9GC6YZjVfGtZadq0STl"], "fldUSE1DOLbCdnbpHAN18Mbs": true, "fldurqJnc67X5vipMDiafCEg": "Obtain travel destination control regulations"}, "values": {"fldExAtfyhNnG6EVupR6fC67": ["To-Do"], "fldUSE1DOLbCdnbpHAN18Mbs": "1"}}, {"templateId": "recleQRBf0wj4Uzq8HNFF3OP", "data": {"fldExAtfyhNnG6EVupR6fC67": ["optPhvbgJ6jF4cuFhtb8Fixw"], "fldUSE1DOLbCdnbpHAN18Mbs": true, "fldurqJnc67X5vipMDiafCEg": "Purchase masks and other pandemic prevention items"}, "values": {"fldExAtfyhNnG6EVupR6fC67": ["Personal Belongings"], "fldUSE1DOLbCdnbpHAN18Mbs": "1"}}, {"templateId": "recd8C2K3eLCxvVEfnnWRoUp", "data": {"fldExAtfyhNnG6EVupR6fC67": ["opt0CC3NEcYNosykd4dYVv9F"], "fldUSE1DOLbCdnbpHAN18Mbs": true, "fldurqJnc67X5vipMDiafCEg": "Sunglasses/Glasses"}, "values": {"fldExAtfyhNnG6EVupR6fC67": ["Luggage"], "fldUSE1DOLbCdnbpHAN18Mbs": "1", "fldurqJnc67X5vipMDiafCEg": "Sunglasses/Glasses"}}, {"templateId": "rectuvGU1CZ0KXwcGjdX7Cyw", "data": {"fldExAtfyhNnG6EVupR6fC67": ["optPhvbgJ6jF4cuFhtb8Fixw"], "fldUSE1DOLbCdnbpHAN18Mbs": true, "fldurqJnc67X5vipMDiafCEg": "Cash"}, "values": {"fldExAtfyhNnG6EVupR6fC67": ["Personal Belongings"], "fldUSE1DOLbCdnbpHAN18Mbs": "1", "fldurqJnc67X5vipMDiafCEg": "Cash"}}, {"templateId": "recOL6JYcT8vLCanI2xBJwFg", "data": {"fldExAtfyhNnG6EVupR6fC67": ["optUEp6DvgNsAXZa5X5x3HgP"], "fldurqJnc67X5vipMDiafCEg": "Facial Cleanser"}, "values": {"fldExAtfyhNnG6EVupR6fC67": [" Toiletries"], "fldUSE1DOLbCdnbpHAN18Mbs": "0", "fldurqJnc67X5vipMDiafCEg": "Facial Cleanser"}}, {"templateId": "recE24vN0FYqpTEetSr3nLtA", "data": {"fldExAtfyhNnG6EVupR6fC67": ["optH4ow2A2xnUgo578xeXBtE"], "fldurqJnc67X5vipMDiafCEg": "Tablet/Laptop"}, "values": {"fldExAtfyhNnG6EVupR6fC67": [" Electronic Devices"], "fldUSE1DOLbCdnbpHAN18Mbs": "0", "fldurqJnc67X5vipMDiafCEg": "Tablet/Laptop"}}]}, {"resourceType": "DATABASE", "templateId": "dat6yWrvm1lZiVcdxM8F77LR", "name": {"en": "Hotel Arrangements", "ja": "ホテル手配", "zh-CN": "酒店信息", "zh-TW": "飯店資訊"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwkvGZptxJ42KXNdSUIgTQJ", "name": {"en": "<PERSON><PERSON><PERSON>", "ja": "すべて", "zh-CN": "全部", "zh-TW": "全部"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fld4jz9HOyWjDvhvMRyFlSY0", "hidden": false}, {"templateId": "fldDss6SHEdklLIVWW9EPAY7", "hidden": false}, {"templateId": "fldixoPjqdwtFVAmmu98dkxE", "hidden": false}, {"templateId": "fld9t0zLuFOcsK6ZbK1CfHGR", "hidden": false}, {"templateId": "fldu6Gmc2JNw2EbAPka9AW6L", "hidden": false, "width": 150}, {"templateId": "fldTC6r0E89NBa5ulTtBw1Y7", "hidden": false}, {"templateId": "fldRTd09UpRy3pWiHUWNpoew", "hidden": false}], "groups": []}, {"type": "KANBAN", "templateId": "viw5rusQUw57TNyxbBixAJw1", "name": {"en": "kanban view", "ja": "カンバンビュー", "zh-CN": "看板视图", "zh-TW": "看板視圖"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fld4jz9HOyWjDvhvMRyFlSY0", "hidden": false}, {"templateId": "fldDss6SHEdklLIVWW9EPAY7", "hidden": false}, {"templateId": "fldixoPjqdwtFVAmmu98dkxE", "hidden": false}, {"templateId": "fld9t0zLuFOcsK6ZbK1CfHGR", "hidden": false}, {"templateId": "fldu6Gmc2JNw2EbAPka9AW6L", "hidden": false}, {"templateId": "fldTC6r0E89NBa5ulTtBw1Y7", "hidden": false}, {"templateId": "fldRTd09UpRy3pWiHUWNpoew", "hidden": false}], "groups": [], "extra": {"kanbanGroupingFieldTemplateId": "fld9t0zLuFOcsK6ZbK1CfHGR", "displayFieldName": true}}, {"type": "GALLERY", "templateId": "viwpEfg2vWBLcTdftEt6PeFZ", "name": {"en": "Gallery", "ja": "ギャラリービュー", "zh-CN": "相册视图", "zh-TW": "相册視圖"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fld4jz9HOyWjDvhvMRyFlSY0", "hidden": false}, {"templateId": "fldDss6SHEdklLIVWW9EPAY7", "hidden": false}, {"templateId": "fldixoPjqdwtFVAmmu98dkxE", "hidden": false}, {"templateId": "fld9t0zLuFOcsK6ZbK1CfHGR", "hidden": false}, {"templateId": "fldu6Gmc2JNw2EbAPka9AW6L", "hidden": false}, {"templateId": "fldTC6r0E89NBa5ulTtBw1Y7", "hidden": false}, {"templateId": "fldRTd09UpRy3pWiHUWNpoew", "hidden": false}], "groups": [], "extra": {"coverFieldTemplateId": "fldTC6r0E89NBa5ulTtBw1Y7", "coverStretch": "FILL", "displayFieldName": true}}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fld4jz9HOyWjDvhvMRyFlSY0", "privilege": "TYPE_EDIT", "name": {"en": "Hotel", "ja": "ホテル", "zh-CN": "酒店", "zh-TW": "飯店"}, "primary": true}, {"type": "RATING", "templateId": "fldDss6SHEdklLIVWW9EPAY7", "privilege": "NAME_EDIT", "name": {"en": "Hotel Rating", "ja": "ホテル評価", "zh-CN": "酒店评分", "zh-TW": "飯店評分"}, "property": {"icon": {"type": "EMOJI", "emoji": "⭐️"}, "max": 5}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldixoPjqdwtFVAmmu98dkxE", "privilege": "NAME_EDIT", "name": {"en": "Description", "ja": "説明", "zh-CN": "描述", "zh-TW": "描述"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fld9t0zLuFOcsK6ZbK1CfHGR", "privilege": "NAME_EDIT", "name": {"en": "Area", "ja": "エリア", "zh-CN": "区域", "zh-TW": "區域"}, "property": {"options": [{"id": "optHmqacqLfxLaMJ8PqW3A7Q", "name": "Seoul", "color": "deepPurple"}, {"id": "optJYiC272nxGkVVkKZq9E0E", "name": "Daegu", "color": "indigo"}, {"id": "opt2am5kBFnkSaDRkxV6qbt9", "name": "Incheon", "color": "blue"}], "defaultValue": ""}, "primary": false}, {"type": "CURRENCY", "templateId": "fldu6Gmc2JNw2EbAPka9AW6L", "privilege": "NAME_EDIT", "name": {"en": "Starting Price", "ja": "開始価格", "zh-CN": "起始价格", "zh-TW": "起始價格"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "$", "symbolAlign": "left"}, "primary": false}, {"type": "ATTACHMENT", "templateId": "fldTC6r0E89NBa5ulTtBw1Y7", "privilege": "NAME_EDIT", "name": {"en": "Attraction Photo", "ja": "観光地の写真", "zh-CN": "景点照片", "zh-TW": "景點照片"}, "primary": false}, {"type": "LINK", "templateId": "fldRTd09UpRy3pWiHUWNpoew", "privilege": "NAME_EDIT", "name": {"en": "Itinerary", "ja": "りょていひょう", "zh-CN": "行程", "zh-TW": "行程"}, "property": {"foreignDatabaseTemplateId": "datZD6cVHdwjnB0pD29vujPT", "brotherFieldTemplateId": "fldjk20WPxvvsDNmo61nQhDV"}, "primary": false}], "records": [{"templateId": "recsQnzEmMJBbCmE35jYsunC", "data": {"fld4jz9HOyWjDvhvMRyFlSY0": "Jade Mountain Resort", "fld9t0zLuFOcsK6ZbK1CfHGR": ["optHmqacqLfxLaMJ8PqW3A7Q"], "fldDss6SHEdklLIVWW9EPAY7": 3, "fldRTd09UpRy3pWiHUWNpoew": ["rec0hSbD1yC4MpuhEqzSI4GW"], "fldTC6r0E89NBa5ulTtBw1Y7": [{"id": "tplattN4NV57yqtKc3HG15nFANa", "name": "lonpr-hansom-6609-hor-feat.jpg", "path": "template/tplattN4NV57yqtKc3HG15nFANa.jpeg", "bucket": "bika-staging", "mimeType": "image/jpeg", "size": 184159}], "fldixoPjqdwtFVAmmu98dkxE": "Located on the 600-acre Anse Chastanet Beach, Jade Mountain is the only hotel in St. Lucia floating on the Caribbean Sea, offering views of both Piti and Gros Piton Mountains. The hotel's architecture fully utilizes its unique geographic location: each suite's bedroom, living space, and exclusive infinity pool are stacked, creating a platform with no walls to obstruct the mountain and sea views. This is a dream destination, the most romantic place in the world – the Jade Mountain Villas Resort in St. Lucia. Set above a private 600-acre beach, the resort provides breathtaking views of Piti and Gros Piton Mountains, and cleverly integrates nature with architecture in its design. Guests are immersed in scenery that blends with the sky, ocean, and mountains, giving the feeling of a paradise. The name \"Jade\" symbolizes the green gemstone, nature's green. This enchanting place is the work of Canadian architect <PERSON>, who used a design concept that blends with the natural environment. The resort has only 29 rooms, each connected by individual bridges to public areas like the lobby and restaurant, creating a fairy-tale palace-like atmosphere, an emerald paradise, a structure like something from another world.", "fldu6Gmc2JNw2EbAPka9AW6L": 12388}, "values": {"fld9t0zLuFOcsK6ZbK1CfHGR": ["Seoul"], "fldDss6SHEdklLIVWW9EPAY7": "3", "fldRTd09UpRy3pWiHUWNpoew": ["College Street"], "fldTC6r0E89NBa5ulTtBw1Y7": ["lonpr-hansom-6609-hor-feat.jpg"], "fldixoPjqdwtFVAmmu98dkxE": "Located on the 600-acre Anse Chastanet Beach, Jade Mountain is the only hotel in St. Lucia floating on the Caribbean Sea, offering views of both Piti and Gros Piton Mountains. The hotel's architecture fully utilizes its unique geographic location: each suite's bedroom, living space, and exclusive infinity pool are stacked, creating a platform with no walls to obstruct the mountain and sea views. This is a dream destination, the most romantic place in the world – the Jade Mountain Villas Resort in St. Lucia. Set above a private 600-acre beach, the resort provides breathtaking views of Piti and Gros Piton Mountains, and cleverly integrates nature with architecture in its design. Guests are immersed in scenery that blends with the sky, ocean, and mountains, giving the feeling of a paradise. The name \"Jade\" symbolizes the green gemstone, nature's green. This enchanting place is the work of Canadian architect <PERSON>, who used a design concept that blends with the natural environment. The resort has only 29 rooms, each connected by individual bridges to public areas like the lobby and restaurant, creating a fairy-tale palace-like atmosphere, an emerald paradise, a structure like something from another world.", "fldu6Gmc2JNw2EbAPka9AW6L": "$12388"}}, {"templateId": "recPgrsBE3jdKyEGJFa27r9D", "data": {"fld4jz9HOyWjDvhvMRyFlSY0": "Hotel Plaza Athenee", "fld9t0zLuFOcsK6ZbK1CfHGR": ["optJYiC272nxGkVVkKZq9E0E"], "fldDss6SHEdklLIVWW9EPAY7": 3, "fldRTd09UpRy3pWiHUWNpoew": ["recnUx1LrT7McWF8Uiev52pz", "recJs1NeS4zMq9ccKguTtBbC"], "fldTC6r0E89NBa5ulTtBw1Y7": [{"id": "tplattGniGCokQ6gsPV9v1zjxOJ", "name": "*********.jpg", "path": "template/tplattGniGCokQ6gsPV9v1zjxOJ.jpeg", "bucket": "bika-staging", "mimeType": "image/jpeg", "size": 133272}], "fldixoPjqdwtFVAmmu98dkxE": "With the most beautiful terrace restaurant in all of Paris, Hotel Plaza Athenee offers an otherworldly beauty, contrasting with the grandeur of the Ritz Hotel. Located on Paris's prestigious Avenue Montaigne, the hotel remains a seasonal blossom of fresh flowers, proudly growing in the soil, symbolizing the elegance and charm the hotel offers to travelers with refined taste. A place where many celebrities have stayed, including <PERSON>, <PERSON>, and the <PERSON> family. The Michelin 3-star restaurant '<PERSON> at the Plaza Athenee,' led by legendary chef <PERSON>, is faithful to the natural flavors of French cuisine and loved by gourmet enthusiasts.", "fldu6Gmc2JNw2EbAPka9AW6L": 8634}, "values": {"fld9t0zLuFOcsK6ZbK1CfHGR": ["Daegu"], "fldDss6SHEdklLIVWW9EPAY7": "3", "fldRTd09UpRy3pWiHUWNpoew": ["Coffee Prince No.1", " Myeongdong Cathedral"], "fldTC6r0E89NBa5ulTtBw1Y7": ["*********.jpg"], "fldixoPjqdwtFVAmmu98dkxE": "With the most beautiful terrace restaurant in all of Paris, Hotel Plaza Athenee offers an otherworldly beauty, contrasting with the grandeur of the Ritz Hotel. Located on Paris's prestigious Avenue Montaigne, the hotel remains a seasonal blossom of fresh flowers, proudly growing in the soil, symbolizing the elegance and charm the hotel offers to travelers with refined taste. A place where many celebrities have stayed, including <PERSON>, <PERSON>, and the <PERSON> family. The Michelin 3-star restaurant '<PERSON> at the Plaza Athenee,' led by legendary chef <PERSON>, is faithful to the natural flavors of French cuisine and loved by gourmet enthusiasts.", "fldu6Gmc2JNw2EbAPka9AW6L": "$8634"}}, {"templateId": "recHOQlD9wOPPNJoxLm9Vzhw", "data": {"fld4jz9HOyWjDvhvMRyFlSY0": "Adiwana Dara Ayu Villas", "fld9t0zLuFOcsK6ZbK1CfHGR": ["opt2am5kBFnkSaDRkxV6qbt9"], "fldDss6SHEdklLIVWW9EPAY7": 4, "fldRTd09UpRy3pWiHUWNpoew": ["recCZrM9RYafLJDfgxXYZiPE"], "fldTC6r0E89NBa5ulTtBw1Y7": [{"id": "tplatt3TncYUtg2lKn7P0tu5x00", "name": "*********.jpg", "path": "template/tplatt3TncYUtg2lKn7P0tu5x00.jpeg", "bucket": "bika-staging", "mimeType": "image/jpeg", "size": 108560}], "fldixoPjqdwtFVAmmu98dkxE": "Adiwana Dara Ayu Villas is located in Buahan Village, Payangan Gianyar, about a 45-minute drive from Ubud. The resort is surrounded by rice fields and lush forests, offering mountain views and green scenery, with wooden furniture in each villa. Each villa features an outdoor bathroom with a sunken stone bathtub and a hot spring tub. Guests can enjoy daily breakfast, afternoon tea, and small traditional cakes for two. The resort offers a public dining area by the nearby swimming pool and organizes paid activities such as day spas, hiking, and traditional Balinese cooking classes.", "fldu6Gmc2JNw2EbAPka9AW6L": 1290}, "values": {"fld9t0zLuFOcsK6ZbK1CfHGR": ["Incheon"], "fldDss6SHEdklLIVWW9EPAY7": "4", "fldRTd09UpRy3pWiHUWNpoew": ["Hongdae Free Market"], "fldTC6r0E89NBa5ulTtBw1Y7": ["*********.jpg"], "fldixoPjqdwtFVAmmu98dkxE": "Adiwana Dara Ayu Villas is located in Buahan Village, Payangan Gianyar, about a 45-minute drive from Ubud. The resort is surrounded by rice fields and lush forests, offering mountain views and green scenery, with wooden furniture in each villa. Each villa features an outdoor bathroom with a sunken stone bathtub and a hot spring tub. Guests can enjoy daily breakfast, afternoon tea, and small traditional cakes for two. The resort offers a public dining area by the nearby swimming pool and organizes paid activities such as day spas, hiking, and traditional Balinese cooking classes.", "fldu6Gmc2JNw2EbAPka9AW6L": "$1290"}}]}, {"resourceType": "DATABASE", "templateId": "datxiqnOF44xjuZSbnlH2ycx", "name": {"en": "Attractions", "ja": "観光地", "zh-CN": "景点信息", "zh-TW": "景點信息"}, "description": "Attractions", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viw2kzHFqMeFusPiCF64esyr", "name": {"en": "ALL", "ja": "すべて", "zh-CN": "全部", "zh-TW": "全部"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldA8LnZ5dIfQ2zHgXj9htN3", "hidden": false, "width": 251}, {"templateId": "fld5z7aldCglvtsIBxuEFkQA", "hidden": false}, {"templateId": "fldYsy856FzWPTkEB4WtEoJ6", "hidden": false}, {"templateId": "fldEfiEXJkhBY1AOu2VT8oxG", "hidden": false}, {"templateId": "fldIjL7xOSENIHbEMnEYoa61", "hidden": false}, {"templateId": "fldFvdg6jWsCVUH24KTBUHNz", "hidden": false}, {"templateId": "fldILCsicN9HdHeyHsCCNlsK", "hidden": false}]}, {"type": "GALLERY", "templateId": "viw3XRNOkxRM9RKWj5UBiRPF", "name": {"en": "Gallery view", "ja": "ギャラリービュー", "zh-CN": "相册视图", "zh-TW": "畫廊視圖"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldA8LnZ5dIfQ2zHgXj9htN3", "hidden": false}, {"templateId": "fld5z7aldCglvtsIBxuEFkQA", "hidden": false}, {"templateId": "fldYsy856FzWPTkEB4WtEoJ6", "hidden": false}, {"templateId": "fldEfiEXJkhBY1AOu2VT8oxG", "hidden": false}, {"templateId": "fldIjL7xOSENIHbEMnEYoa61", "hidden": false}, {"templateId": "fldFvdg6jWsCVUH24KTBUHNz", "hidden": false}, {"templateId": "fldILCsicN9HdHeyHsCCNlsK", "hidden": false}], "groups": [], "extra": {"coverFieldTemplateId": "fldFvdg6jWsCVUH24KTBUHNz", "coverStretch": "FILL", "displayFieldName": true}}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldA8LnZ5dIfQ2zHgXj9htN3", "privilege": "TYPE_EDIT", "name": {"en": "Attraction", "ja": "観光地", "zh-CN": "景点", "zh-TW": "景點"}, "primary": true}, {"type": "LONG_TEXT", "templateId": "fld5z7aldCglvtsIBxuEFkQA", "privilege": "NAME_EDIT", "name": {"en": "Latitude & Longitude", "ja": "緯度と経度", "zh-CN": "纬度&经度", "zh-TW": "緯度&經度"}, "primary": false}, {"type": "RATING", "templateId": "fldYsy856FzWPTkEB4WtEoJ6", "privilege": "NAME_EDIT", "name": {"en": "Attraction Rating", "ja": "観光地評価", "zh-CN": "景点评分", "zh-TW": "景點評分"}, "property": {"icon": {"type": "EMOJI", "emoji": "⭐️"}, "max": 10}, "primary": false}, {"type": "CURRENCY", "templateId": "fldEfiEXJkhBY1AOu2VT8oxG", "privilege": "NAME_EDIT", "name": {"en": "Ticket Price", "ja": "チケット料金", "zh-CN": "门票价格", "zh-TW": "門票價格"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "$", "symbolAlign": "left"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldIjL7xOSENIHbEMnEYoa61", "privilege": "NAME_EDIT", "name": {"en": "Notes", "ja": "メモ", "zh-CN": "备注", "zh-TW": "備註"}, "primary": false}, {"type": "ATTACHMENT", "templateId": "fldFvdg6jWsCVUH24KTBUHNz", "privilege": "NAME_EDIT", "name": {"en": "Classic Photo", "ja": "クラシック写真", "zh-CN": "经典照片", "zh-TW": "經典照片"}, "primary": false}, {"type": "LINK", "templateId": "fldILCsicN9HdHeyHsCCNlsK", "privilege": "NAME_EDIT", "name": {"en": "Itinerary", "ja": "りょてい", "zh-CN": "行程", "zh-TW": "行程"}, "property": {"foreignDatabaseTemplateId": "datZD6cVHdwjnB0pD29vujPT", "brotherFieldTemplateId": "fldIV9klmoOUnGKPHYHFcmqy"}, "primary": false}], "records": [{"templateId": "recfVmqa7CEuOps2nThCMIKH", "data": {"fld5z7aldCglvtsIBxuEFkQA": "129.0642517383149, 35.48793800907315", "fldA8LnZ5dIfQ2zHgXj9htN3": "Three Major Buddhist Temples of Korea", "fldEfiEXJkhBY1AOu2VT8oxG": 150, "fldFvdg6jWsCVUH24KTBUHNz": [{"id": "tplattumaLwb7vFGutZDTEGPDiE", "name": "image.png", "path": "template/tplattumaLwb7vFGutZDTEGPDiE.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 559215}], "fldILCsicN9HdHeyHsCCNlsK": ["rec0hSbD1yC4MpuhEqzSI4GW"], "fldIjL7xOSENIHbEMnEYoa61": "Long journey", "fldYsy856FzWPTkEB4WtEoJ6": 8}, "values": {"fldEfiEXJkhBY1AOu2VT8oxG": "$150", "fldFvdg6jWsCVUH24KTBUHNz": ["image.png"], "fldILCsicN9HdHeyHsCCNlsK": ["College Street"], "fldYsy856FzWPTkEB4WtEoJ6": "8"}}, {"templateId": "rech9fW5M2yxIO8kr3jKeDfQ", "data": {"fld5z7aldCglvtsIBxuEFkQA": "128.18528615619445, 38.22513452140671", "fldA8LnZ5dIfQ2zHgXj9htN3": "Taebaek Mountains", "fldEfiEXJkhBY1AOu2VT8oxG": 200, "fldFvdg6jWsCVUH24KTBUHNz": [{"id": "tplattumaLwb7vFGutZDTEGPDiE", "name": "image.png", "path": "template/tplattumaLwb7vFGutZDTEGPDiE.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 559215}], "fldILCsicN9HdHeyHsCCNlsK": ["recnUx1LrT7McWF8Uiev52pz"], "fldIjL7xOSENIHbEMnEYoa61": "Need to bring a passport", "fldYsy856FzWPTkEB4WtEoJ6": 7}, "values": {"fldEfiEXJkhBY1AOu2VT8oxG": "$200", "fldFvdg6jWsCVUH24KTBUHNz": ["image.png"], "fldILCsicN9HdHeyHsCCNlsK": ["Coffee Prince No.1"], "fldYsy856FzWPTkEB4WtEoJ6": "7"}}, {"templateId": "recOMlLx8qIft3m7CLkUte1k", "data": {"fld5z7aldCglvtsIBxuEFkQA": "126.5120207626567, 33.51641112697289", "fldA8LnZ5dIfQ2zHgXj9htN3": "Yongdu Rock", "fldEfiEXJkhBY1AOu2VT8oxG": 500, "fldFvdg6jWsCVUH24KTBUHNz": [{"id": "tplattumaLwb7vFGutZDTEGPDiE", "name": "image (1).png", "path": "template/tplattumaLwb7vFGutZDTEGPDiE.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 559215}], "fldILCsicN9HdHeyHsCCNlsK": ["recCZrM9RYafLJDfgxXYZiPE"], "fldIjL7xOSENIHbEMnEYoa61": "Only accessible by self-driving", "fldYsy856FzWPTkEB4WtEoJ6": 7}, "values": {"fldEfiEXJkhBY1AOu2VT8oxG": "$500", "fldFvdg6jWsCVUH24KTBUHNz": ["image (1).png"], "fldILCsicN9HdHeyHsCCNlsK": ["Hongdae Free Market"], "fldYsy856FzWPTkEB4WtEoJ6": "7"}}, {"templateId": "reclH3f6Dyalzx3pDh8WbxXk", "data": {"fld5z7aldCglvtsIBxuEFkQA": "127.09818244638801,37.51132659805846", "fldA8LnZ5dIfQ2zHgXj9htN3": " Lotte World Adventure", "fldEfiEXJkhBY1AOu2VT8oxG": 234, "fldFvdg6jWsCVUH24KTBUHNz": [{"id": "tplattvouI8ZgrQ9yKZt2l45KbF", "name": "156853096 (1).jpg", "path": "template/tplattvouI8ZgrQ9yKZt2l45KbF.jpeg", "bucket": "bika-staging", "mimeType": "image/jpeg", "size": 183640}], "fldILCsicN9HdHeyHsCCNlsK": ["recJs1NeS4zMq9ccKguTtBbC"], "fldYsy856FzWPTkEB4WtEoJ6": 5}, "values": {"fld5z7aldCglvtsIBxuEFkQA": "127.09818244638801,37.51132659805846", "fldA8LnZ5dIfQ2zHgXj9htN3": " Lotte World Adventure", "fldEfiEXJkhBY1AOu2VT8oxG": "$234", "fldFvdg6jWsCVUH24KTBUHNz": ["156853096 (1).jpg"], "fldILCsicN9HdHeyHsCCNlsK": [" Myeongdong Cathedral"], "fldYsy856FzWPTkEB4WtEoJ6": "5"}}]}, {"resourceType": "DATABASE", "templateId": "datZD6cVHdwjnB0pD29vujPT", "name": {"en": "Itinerary", "ja": "りょてい", "zh-CN": "旅程信息", "zh-TW": "旅程資訊"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwACeNI86KupXfgNgbBGLkZ", "name": {"en": "ALL", "ja": "すべて", "zh-CN": "全部", "zh-TW": "全部"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldSyFk6r6NYr2YT1LKdmKg3", "hidden": false, "width": 205}, {"templateId": "fldFAqgtU2EyIZJziwmAzh2Y", "hidden": false, "width": 172}, {"templateId": "fldVInf3YtMuEa8SQgvzEVIx", "hidden": false}, {"templateId": "fldV12AOS1e16Wx6VOrUgSYh", "hidden": false, "width": 150}, {"templateId": "fldjS6zI4StGiZ6ayJwkQqcy", "hidden": false}, {"templateId": "flddgGBvKgVSyK8PV0fqrcXR", "hidden": false}, {"templateId": "fldIV9klmoOUnGKPHYHFcmqy", "hidden": false, "width": 239}, {"templateId": "fldjk20WPxvvsDNmo61nQhDV", "hidden": false}, {"templateId": "fld3qJjukDuPhVRHf1mdQpx1", "hidden": false}], "groups": []}, {"type": "KANBAN", "templateId": "viwxKUjjMOfMyy8ih1PIIs5H", "name": {"en": "kanban view ", "ja": "カンバンビュー", "zh-CN": "看板视图", "zh-TW": "看板視圖"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldSyFk6r6NYr2YT1LKdmKg3", "hidden": false}, {"templateId": "fldFAqgtU2EyIZJziwmAzh2Y", "hidden": false}, {"templateId": "fldVInf3YtMuEa8SQgvzEVIx", "hidden": false}, {"templateId": "fldV12AOS1e16Wx6VOrUgSYh", "hidden": false}, {"templateId": "fldjS6zI4StGiZ6ayJwkQqcy", "hidden": false}, {"templateId": "flddgGBvKgVSyK8PV0fqrcXR", "hidden": false}, {"templateId": "fldIV9klmoOUnGKPHYHFcmqy", "hidden": false}, {"templateId": "fldjk20WPxvvsDNmo61nQhDV", "hidden": true}, {"templateId": "fld3qJjukDuPhVRHf1mdQpx1", "hidden": true}], "groups": [], "extra": {"kanbanGroupingFieldTemplateId": "fldVInf3YtMuEa8SQgvzEVIx", "displayFieldName": true}}, {"type": "TABLE", "templateId": "viwDf5H0zyaruRa1xTY3Y8JU", "name": {"en": " Cost Budget", "ja": "よさん", "zh-CN": "费用预算", "zh-TW": "費用預算"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldSyFk6r6NYr2YT1LKdmKg3", "hidden": false}, {"templateId": "fldFAqgtU2EyIZJziwmAzh2Y", "hidden": false}, {"templateId": "fldVInf3YtMuEa8SQgvzEVIx", "hidden": false}, {"templateId": "fldV12AOS1e16Wx6VOrUgSYh", "hidden": false}, {"templateId": "fldjS6zI4StGiZ6ayJwkQqcy", "hidden": false}, {"templateId": "flddgGBvKgVSyK8PV0fqrcXR", "hidden": false}, {"templateId": "fldIV9klmoOUnGKPHYHFcmqy", "hidden": false}, {"templateId": "fldjk20WPxvvsDNmo61nQhDV", "hidden": false}, {"templateId": "fld3qJjukDuPhVRHf1mdQpx1", "hidden": false}], "groups": [{"fieldTemplateId": "fldVInf3YtMuEa8SQgvzEVIx", "asc": false}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldSyFk6r6NYr2YT1LKdmKg3", "privilege": "TYPE_EDIT", "name": {"en": "Location", "ja": "ばしょ", "zh-CN": "地点", "zh-TW": "地點"}, "primary": true}, {"type": "DATETIME", "templateId": "fldFAqgtU2EyIZJziwmAzh2Y", "privilege": "NAME_EDIT", "name": {"en": "Departure Time", "ja": "しゅっぱつじかん", "zh-CN": "出发时间", "zh-TW": "出發時間"}, "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldVInf3YtMuEa8SQgvzEVIx", "privilege": "NAME_EDIT", "name": {"en": "Schedule", "ja": "スケジュール", "zh-CN": "日程安排", "zh-TW": "日程安排"}, "property": {"options": [{"id": "optdBZScAGe6vSPPi7zcR86z", "name": "day01", "color": "deepPurple"}, {"id": "optMTPZY67AI7HLChEEZh6jM", "name": "day02", "color": "indigo"}, {"id": "opt9C3C28JuWfqPXeylOG8is", "name": "day03", "color": "blue"}, {"id": "opt1pjGnCiDsSdhc1yPqCRhg", "name": "Day 4", "color": "teal"}], "defaultValue": ""}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldV12AOS1e16Wx6VOrUgSYh", "privilege": "NAME_EDIT", "name": {"en": "Transportation Details", "ja": "こうつうのしょうさい", "zh-CN": "交通详情", "zh-TW": "交通詳情"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldjS6zI4StGiZ6ayJwkQqcy", "privilege": "NAME_EDIT", "name": {"en": "Mode of Transportation", "ja": "こうつうしゅだん", "zh-CN": "交通方式", "zh-TW": "交通方式"}, "property": {"options": [{"id": "optPCWeaTiRezPRGzgVOPP4j", "name": "Taxi", "color": "deepPurple"}], "defaultValue": ""}, "primary": false}, {"type": "CURRENCY", "templateId": "flddgGBvKgVSyK8PV0fqrcXR", "privilege": "NAME_EDIT", "name": {"en": "Expenses", "ja": "ひよう", "zh-CN": "开销", "zh-TW": "支出"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "$", "symbolAlign": "left"}, "primary": false}, {"type": "LINK", "templateId": "fldIV9klmoOUnGKPHYHFcmqy", "privilege": "NAME_EDIT", "name": {"en": "Attractions", "ja": "かんこうち", "zh-CN": "景点信息", "zh-TW": "景點信息"}, "property": {"foreignDatabaseTemplateId": "datxiqnOF44xjuZSbnlH2ycx", "brotherFieldTemplateId": "fldILCsicN9HdHeyHsCCNlsK"}, "primary": false}, {"type": "LINK", "templateId": "fldjk20WPxvvsDNmo61nQhDV", "privilege": "NAME_EDIT", "name": {"en": "Hotel Arrangements", "ja": "ほてるてはい", "zh-CN": "酒店信息", "zh-TW": "酒店信息"}, "property": {"foreignDatabaseTemplateId": "dat6yWrvm1lZiVcdxM8F77LR", "brotherFieldTemplateId": "fldRTd09UpRy3pWiHUWNpoew"}, "primary": false}, {"type": "LINK", "templateId": "fld3qJjukDuPhVRHf1mdQpx1", "privilege": "NAME_EDIT", "name": {"en": "Participant Information", "ja": "さんかしゃじょうほう", "zh-CN": "旅客信息", "zh-TW": "參與者資訊"}, "property": {"foreignDatabaseTemplateId": "datPMggKP4lSbeFiQJ5nRHkJ", "brotherFieldTemplateId": "fld0haKFaHrcBpeBUkVjBz5x"}, "primary": false}], "records": [{"templateId": "rec0hSbD1yC4MpuhEqzSI4GW", "data": {"fld3qJjukDuPhVRHf1mdQpx1": ["recVERA0WbrcLgubCerylBgi"], "fldFAqgtU2EyIZJziwmAzh2Y": "2024-12-24T16:00:00.000Z", "fldIV9klmoOUnGKPHYHFcmqy": ["recfVmqa7CEuOps2nThCMIKH"], "fldSyFk6r6NYr2YT1LKdmKg3": "College Street", "fldV12AOS1e16Wx6VOrUgSYh": "Subway Line 2 and 5, 5-minute walk from Yeouido Station Exit 3, towards National Assembly", "fldVInf3YtMuEa8SQgvzEVIx": ["opt9C3C28JuWfqPXeylOG8is"], "flddgGBvKgVSyK8PV0fqrcXR": 53, "fldjS6zI4StGiZ6ayJwkQqcy": ["optPCWeaTiRezPRGzgVOPP4j"], "fldjk20WPxvvsDNmo61nQhDV": ["recsQnzEmMJBbCmE35jYsunC"]}, "values": {"fld3qJjukDuPhVRHf1mdQpx1": ["<PERSON>"], "fldFAqgtU2EyIZJziwmAzh2Y": "2024-12-24", "fldIV9klmoOUnGKPHYHFcmqy": ["Three Major Buddhist Temples of Korea"], "fldSyFk6r6NYr2YT1LKdmKg3": "College Street", "fldVInf3YtMuEa8SQgvzEVIx": ["day03"], "flddgGBvKgVSyK8PV0fqrcXR": "$53", "fldjS6zI4StGiZ6ayJwkQqcy": ["Taxi"], "fldjk20WPxvvsDNmo61nQhDV": ["Jade Mountain Resort"]}}, {"templateId": "recnUx1LrT7McWF8Uiev52pz", "data": {"fld3qJjukDuPhVRHf1mdQpx1": ["recJpIKdTMlKKpVMyGhH8xV2"], "fldFAqgtU2EyIZJziwmAzh2Y": "2024-12-24T16:00:00.000Z", "fldIV9klmoOUnGKPHYHFcmqy": ["rech9fW5M2yxIO8kr3jKeDfQ"], "fldSyFk6r6NYr2YT1LKdmKg3": "Coffee Prince No.1", "fldV12AOS1e16Wx6VOrUgSYh": "60 Yeouido-dong, Yeongdeungpo-gu, Seoul", "fldVInf3YtMuEa8SQgvzEVIx": ["optMTPZY67AI7HLChEEZh6jM"], "flddgGBvKgVSyK8PV0fqrcXR": 149, "fldjS6zI4StGiZ6ayJwkQqcy": ["optPCWeaTiRezPRGzgVOPP4j"], "fldjk20WPxvvsDNmo61nQhDV": ["recPgrsBE3jdKyEGJFa27r9D"]}, "values": {"fld3qJjukDuPhVRHf1mdQpx1": ["<PERSON>"], "fldFAqgtU2EyIZJziwmAzh2Y": "2024-12-24", "fldIV9klmoOUnGKPHYHFcmqy": ["Taebaek Mountains"], "fldSyFk6r6NYr2YT1LKdmKg3": "Coffee Prince No.1", "fldVInf3YtMuEa8SQgvzEVIx": ["day02"], "flddgGBvKgVSyK8PV0fqrcXR": "$149", "fldjS6zI4StGiZ6ayJwkQqcy": ["Taxi"], "fldjk20WPxvvsDNmo61nQhDV": ["Hotel Plaza Athenee"]}}, {"templateId": "recCZrM9RYafLJDfgxXYZiPE", "data": {"fld3qJjukDuPhVRHf1mdQpx1": ["recf0wmmUpHbVGaTEDZcNoLp"], "fldFAqgtU2EyIZJziwmAzh2Y": "2024-12-24T16:00:00.000Z", "fldIV9klmoOUnGKPHYHFcmqy": ["recOMlLx8qIft3m7CLkUte1k"], "fldSyFk6r6NYr2YT1LKdmKg3": "Hongdae Free Market", "fldV12AOS1e16Wx6VOrUgSYh": "11-1 Daehyeon-dong, Seodaemun-gu, Seoul", "fldVInf3YtMuEa8SQgvzEVIx": ["optdBZScAGe6vSPPi7zcR86z"], "flddgGBvKgVSyK8PV0fqrcXR": 1182, "fldjS6zI4StGiZ6ayJwkQqcy": ["optPCWeaTiRezPRGzgVOPP4j"], "fldjk20WPxvvsDNmo61nQhDV": ["recHOQlD9wOPPNJoxLm9Vzhw"]}, "values": {"fld3qJjukDuPhVRHf1mdQpx1": ["<PERSON>"], "fldFAqgtU2EyIZJziwmAzh2Y": "2024-12-24", "fldIV9klmoOUnGKPHYHFcmqy": ["Yongdu Rock"], "fldSyFk6r6NYr2YT1LKdmKg3": "Hongdae Free Market", "fldVInf3YtMuEa8SQgvzEVIx": ["day01"], "flddgGBvKgVSyK8PV0fqrcXR": "$1182", "fldjS6zI4StGiZ6ayJwkQqcy": ["Taxi"], "fldjk20WPxvvsDNmo61nQhDV": ["Adiwana Dara Ayu Villas"]}}, {"templateId": "recJs1NeS4zMq9ccKguTtBbC", "data": {"fld3qJjukDuPhVRHf1mdQpx1": ["recJpIKdTMlKKpVMyGhH8xV2"], "fldFAqgtU2EyIZJziwmAzh2Y": "2024-12-24T16:00:00.000Z", "fldIV9klmoOUnGKPHYHFcmqy": ["reclH3f6Dyalzx3pDh8WbxXk"], "fldSyFk6r6NYr2YT1LKdmKg3": " Myeongdong Cathedral", "fldV12AOS1e16Wx6VOrUgSYh": "Bukchon Hanok Village, Insadong, Gyeonghoeru Pavilion, Gyotaejeon Hall", "fldVInf3YtMuEa8SQgvzEVIx": ["optMTPZY67AI7HLChEEZh6jM"], "flddgGBvKgVSyK8PV0fqrcXR": 1353.98, "fldjS6zI4StGiZ6ayJwkQqcy": ["optPCWeaTiRezPRGzgVOPP4j"], "fldjk20WPxvvsDNmo61nQhDV": ["recPgrsBE3jdKyEGJFa27r9D"]}, "values": {"fld3qJjukDuPhVRHf1mdQpx1": ["<PERSON>"], "fldFAqgtU2EyIZJziwmAzh2Y": "2024-12-24", "fldIV9klmoOUnGKPHYHFcmqy": [" Lotte World Adventure"], "fldSyFk6r6NYr2YT1LKdmKg3": " Myeongdong Cathedral", "fldV12AOS1e16Wx6VOrUgSYh": "Bukchon Hanok Village, Insadong, Gyeonghoeru Pavilion, Gyotaejeon Hall", "fldVInf3YtMuEa8SQgvzEVIx": ["day02"], "flddgGBvKgVSyK8PV0fqrcXR": "$1353.98", "fldjS6zI4StGiZ6ayJwkQqcy": ["Taxi"], "fldjk20WPxvvsDNmo61nQhDV": ["Hotel Plaza Athenee"]}}]}, {"resourceType": "DASHBOARD", "templateId": "dsbXW659icHHIWr6mZuFBtuf", "name": {"en": "Travel Overview", "ja": "りょこうがいよう", "zh-CN": "旅行概览", "zh-TW": "旅行總覽"}, "widgets": [{"templateId": "wdtYOLEe9mgmXL4899BuRwgO", "type": "NUMBER", "name": {"en": "Number of Attractions", "ja": "かんこうちのかず", "zh-CN": "景点数量", "zh-TW": "景點數量"}, "layout": {"x": 0, "y": 0, "w": 6, "h": 3}, "summaryDescription": "Attraction", "datasource": {"databaseTemplateId": "datxiqnOF44xjuZSbnlH2ycx", "viewTemplateId": "viw2kzHFqMeFusPiCF64esyr", "type": "DATABASE", "metricsType": "COUNT_RECORDS"}}, {"templateId": "wdt6EI5CfThWYgMUDV3uTKes", "type": "CHART", "name": {"en": " Expenditure Records", "ja": "ししゅつきろく", "zh-CN": "支出记录", "zh-TW": "支出記錄"}, "layout": {"x": 6, "y": 0, "w": 6, "h": 3}, "datasource": {"databaseTemplateId": "datZD6cVHdwjnB0pD29vujPT", "viewTemplateId": "viwxKUjjMOfMyy8ih1PIIs5H", "type": "DATABASE", "chartType": "line", "metricsType": "AGGREGATION_BY_FIELD", "metrics": {"aggregationType": "SUM", "fieldTemplateId": "flddgGBvKgVSyK8PV0fqrcXR"}, "dimensionTemplateId": "flddgGBvKgVSyK8PV0fqrcXR"}, "settings": {"showEmptyValues": true, "showDataTips": true, "excludeZeroPoint": false, "theme": "theme_color_1", "sortByAxis": "X", "sortRule": "ASC"}}, {"templateId": "wdtsAzSj2RcSvMKN9trfTulX", "type": "CHART", "name": {"en": "Number of Daily Itineraries", "ja": "にちべつりょていのかず", "zh-CN": "每日行程数量", "zh-TW": "每日行程數量"}, "layout": {"x": 0, "y": 3, "w": 6, "h": 3}, "datasource": {"databaseTemplateId": "datZD6cVHdwjnB0pD29vujPT", "viewTemplateId": "viwACeNI86KupXfgNgbBGLkZ", "type": "DATABASE", "chartType": "bar", "metricsType": "COUNT_RECORDS", "dimensionTemplateId": "fldVInf3YtMuEa8SQgvzEVIx"}, "settings": {"showEmptyValues": true, "showDataTips": true, "excludeZeroPoint": false, "theme": "theme_color_1", "sortByAxis": "X", "sortRule": "ASC"}}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}