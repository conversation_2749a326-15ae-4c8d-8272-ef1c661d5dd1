import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  schemaVersion: 'v1',
  author: '<PERSON> <<EMAIL>>',
  templateId: 'regular-invoice-collection-for-paid-payments',
  name: {
    en: 'Regular Invoice Collection for Paid Payments',
    'zh-CN': '付款发票定期催收',
    'zh-TW': '付款發票定期催收',
    ja: '支払い済み請求書の定期的な回収',
  },
  cover: '/assets/template/template-cover-invoice.png',
  description: {
    'zh-CN': '定期催收已付款发票，确保财务部门及时收到发票，并管理发票数据',
    'zh-TW': '定期催收已付款發票，確保財務部門及時收到發票，並管理發票數據',
    en: 'Regularly collect paid invoices to ensure the finance department receives them on time and manage invoice data',
    ja: '支払い済みの請求書を定期的に回収して、財務部門が迅速に受信できるようにし、請求書データを管理します',
  },
  keywords: {
    'zh-CN': '发票, 催收, 财务管理, 提醒, 回款',
    'zh-TW': '發票, 催收, 財務管理, 提醒, 回款',
    en: 'Invoice, Collection, Finance Management, Reminder, Payment',
    ja: '請求書, 回収, 財務管理, リマインダー, 支払い',
  },
  personas: {
    'zh-CN': '财务经理, 行政人员, 会计, 团队领导',
    'zh-TW': '財務經理, 行政人員, 會計, 團隊領導',
    en: 'Finance Manager, Administrative Staff, Accountant, Team Leader',
    ja: '財務マネージャー, 事務スタッフ, 会計士, チームリーダー',
  },
  useCases: {
    'zh-CN':
      '定期发票提醒, 未付发票跟踪, 发票催收报告, 供应商沟通, 发票记录维护, 发票纠纷解决, 月度发票审查, 发票提醒邮件, 付款趋势分析, 法规遵从性检查, 财务摘要准备, 审计支持, 发票催收监督, 付款请求审批, 发票详情核实, 发票付款记录, 发票差异调解, 会计系统更新, 供应商查询处理, 发票报告准备, 内部审计执行, 发票流程优化, 自动化解决方案实施, 及时付款确保',
    'zh-TW':
      '定期發票提醒, 未付發票追蹤, 發票催收報告, 供應商溝通, 發票記錄維護, 發票爭議解決, 月度發票審查, 發票提醒郵件, 付款趨勢分析, 法規遵從性檢查, 財務摘要準備, 審計支持, 發票催收監督, 付款請求審批, 發票詳細驗證, 發票付款記錄, 發票差異調解, 會計系統更新, 供應商查詢處理, 發票報告準備, 內部審計執行, 發票流程優化, 自動化解決方案實施, 及時付款確保',
    ja: '定期的な請求書リマインダー, 未払い請求書の追跡, 請求書の回収報告, サプライヤーコミュニケーション, 請求書記録のメンテナンス, 請求書紛争の解決, 月次請求書の審査, 請求書リマインダーメール, 支払い傾向の分析, 法規遵守性のチェック, 財務概要の準備, 監査サポート, 請求書の回収監督, 支払い要求の承認, 請求書の詳細確認, 請求書の支払い記録, 請求書の差異調整, 会計システムの更新, サプライヤーの照会処理, 請求書報告の準備, 内部監査の実施, 請求書プロセスの最適化, 自動化ソリューションの実装, 適時支払いの確保',
    en: 'Regular invoice reminders, unpaid invoice tracking, invoice collection reports, supplier communication, invoice record maintenance, invoice dispute resolution, monthly invoice review, invoice reminder emails, payment trend analysis, compliance check, financial summary preparation, audit support, invoice collection supervision, payment request approval, invoice detail verification, invoice payment records, invoice discrepancy mediation, accounting system update, supplier inquiry handling, invoice report preparation, internal audit execution, invoice process optimization, automated solution implementation, timely payment assurance',
  },
  version: '0.1.3',
  category: 'automation',
  presetUnits: [
    {
      type: 'ROLE',
      name: {
        'zh-CN': '财务经理',
        'zh-TW': '財務經理',
        ja: '財務マネージャー',
        en: 'Finance Manager',
      },
      templateId: 'finance_manager',
    },
  ],
  initMissions: [
    {
      name: {
        'zh-CN': '💡付款发票定期催收模板使用须知',
        'zh-TW': '💡付款發票定期催收模板使用须知',
        ja: '💡支払い済みの請求書定期催收テンプレートの使用方法',
        en: '💡Payment Invoice Regular Collection Template Usage Instructions',
      },
      type: 'READ_TEMPLATE_README',
      templateId: 'regular-invoice-collection-for-paid-payments',
      time: 5,
      beforeText: {
        title: {
          'zh-CN': '恭喜您，模板已经安装完毕',
          'zh-TW': '恭喜您，模板已經安裝完畢',
          ja: 'おめでとうございます、テンプレートのインストールが完了しました',
          en: 'Congratulations, the template has been installed',
        },
        description: {
          'zh-CN': '下一步请您花几分钟阅读模板的的使用教程。',
          'zh-TW': '下一步請您花幾分鐘閱讀模板的使用教程。',
          en: 'Next, please take a few minutes to read the tutorial on how to use the template.',
          ja: '次に、テンプレートの使用方法についてのチュートリアルを数分間お読みください。',
        },
      },
      assignType: 'DEDICATED',
      forcePopup: true,
      wizardGuideId: 'COMMON_MY_TODO_TUTORIAL',
      redirect: {
        type: 'MY_MISSIONS',
      },
      to: [
        {
          type: 'CURRENT_OPERATOR',
        },
      ],
    },
    {
      name: {
        'zh-CN': '💡设置向导 #2：请在发票管理中设置好对应的发票信息以及请款人信息',
        'zh-TW': '💡設定向導 #2：請在請求書管理中設置好相對應的請求書信息以及請款人信息',
        en: '💡Setup Wizard #2: Please set up the corresponding invoice information and payer information in the invoice management',
        ja: '💡セットアップウィザード #2：請求書管理で対応の請求書情報および支払者情報を設定してください',
      },
      description: {
        'zh-CN':
          '您需要在发票管理中设置好对应的人员信息，以便在催收发票时发送信息用。如果没有设置，催收发票功能将无法正常使用。',
        'zh-TW':
          '您需要在發票管理中設置好相對應的人員信息，以便在催收發票時發送信息用。如果沒有設置，催收發票功能將無法正常使用。',
        ja: '請求書管理で対応の担当者情報を設定してください。催收請求書機能を正常に使用するには、設定が必要です。',
        en: 'You need to set up the corresponding personnel information in the invoice management. If not, the invoice collection function will not work properly.',
      },
      type: 'REDIRECT_SPACE_NODE',
      nodeTemplateId: 'invoice_collection',
      canCompleteManually: true,
      to: [
        {
          type: 'CURRENT_OPERATOR',
        },
      ],
    },
    {
      name: {
        'zh-CN': '💡设置向导 #1：请邀请财务经理加入团队',
        'zh-TW': '💡設定向導 #1：請邀請財務經理加入團隊',
        ja: '💡セットアップウィザード #1：財務マネージャーをチームに招待してください',
        en: '💡Setup Wizard #1: Please invite the finance manager to join the team',
      },
      description: {
        'zh-CN':
          '在开始之前，你需要邀请财务经理加入团队。可以设置角色为「财务经理」然后创建邀请链接。点击下方按钮开始。',
        'zh-TW':
          '在開始之前，你需要邀請財務經理加入團隊。可以設置角色為「財務經理」然後創建邀請鏈接。點擊下方按鈕開始。',
        ja: '開始する前に、財務マネージャーをチームに招待する必要があります。役職を「財務マネージャー」に設定し、招待リンクを作成します。以下のボタンをクリックして開始します。',
        en: 'Before you start, you need to invite the finance manager to join the team. You can set the role to "Finance Manager" and create an invitation link. Click the button below to start.',
      },
      type: 'INVITE_MEMBER',
      roleTemplateId: 'finance_manager',
      assignType: 'SHARE',
      canCompleteManually: true,
      to: [
        {
          type: 'ADMIN',
        },
      ],
    },
  ],
  resources: [
    {
      resourceType: 'AUTOMATION',
      name: {
        'zh-CN': '自动催收发票',
        'zh-TW': '自動催收發票',
        ja: '自動請求書催收',
        en: 'Automatic invoice collection',
      },
      templateId: 'auto_schedule_invoice',
      description: {
        'zh-CN': '每月 25 号上午 10 点自动向没有提供发票的请款人催收发票',
        'zh-TW': '每月 25 號上午 10 點自動向沒有提供發票的請款人催收發票',
        ja: '毎月25日午前10時に請求書を提供していない請求担当者に請求書催收を自動的に行います',
        en: 'Automatically remind the payer to provide the invoice on the 25th of every month at 10am',
      },
      status: 'ACTIVE',
      triggers: [
        {
          triggerType: 'SCHEDULER',
          templateId: 'trigger_scheduler_monthly',
          description: {
            'zh-CN': '每月 25 号上午 10 点自动触发',
            'zh-TW': '每月 25 號上午 10 點自動觸發',
            ja: '毎月25日午前10時に自動トリガー',
            en: 'Automatically triggered at 10am on the 25th of every month',
          },
          input: {
            type: 'SCHEDULER',
            scheduler: {
              timezone: 'AUTO',
              datetime: {
                type: 'TODAY',
                hour: 10,
                minute: 0,
              },
              repeat: {
                every: {
                  type: 'MONTH',
                  interval: 1,
                  monthDays: [25],
                },
              },
            },
          },
        },
      ],
      actions: [
        {
          actionType: 'FIND_RECORDS',
          templateId: 'action_find_have_non_invoice',
          description: {
            'zh-CN': '查找没有提供发票的记录',
            'zh-TW': '查找沒有提供發票的記錄',
            ja: '請求書を提供していないレコードを検索',
            en: 'Find records that have not provided the invoice',
          },
          input: {
            type: 'DATABASE_WITH_FILTER',
            databaseTemplateId: 'invoice_collection',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldType: 'ATTACHMENT',
                  fieldTemplateId: 'corresponding_invoice',
                  clause: {
                    operator: 'IsEmpty',
                  },
                },
              ],
            },
          },
          // 匹配的每一条记录，都单独执行action
          actions: [
            {
              actionType: 'CREATE_MISSION',
              templateId: 'action_send_text_to_payer',
              description: {
                'zh-CN': '自动向请款人催收发票',
                'zh-TW': '自動向請款人催收發票',
                ja: '自動的に請求担当者に請求書催收を依頼します',
                en: 'Automatically remind the payer to provide the invoice',
              },
              input: {
                type: 'MISSION_BODY',
                mission: {
                  name: {
                    'zh-CN': '发票催收提醒：已付款但未收到发票',
                    'zh-TW': '發票催收提醒：已付款但未收到發票',
                    ja: '請求書催收提醒：支払済みで請求書未受領',
                    en: 'Invoice collection reminder: Paid but invoice not received',
                  },
                  description: {
                    'zh-CN':
                      '您好！我们发现您提交的该款项: `<%= _item.cells.description_of_payment.value %>` 已支付，但截至目前尚未收到相关的发票。为了确保财务记录的准确性和完整性，请您尽快提供对应的发票。您可以直接在该消息的下方提交发票。若您已经发送，请忽略此提醒。如有任何问题或需要进一步信息，请随时与我们联系。感谢您的合作！',
                    'zh-TW':
                      '您好！我們發現您提交的該款項：`<%= _item.cells.description_of_payment.value %>` 已支付，但截至目前尚未收到相關的發票。為了確保財務記錄的準確性和完整性，請您盡快提供對應的發票。您可以直接在該消息的下方提交發票。若您已經發送，請忽略此提醒。如有任何問題或需要進一步信息，請隨時與我們聯繫。感謝您的合作！',
                    en: 'Hello! We have noticed that the payment you submitted: `<%= _item.cells.description_of_payment.value %>` has been made, but we have not yet received the related invoice. To ensure the accuracy and completeness of our financial records, please provide the corresponding invoice as soon as possible. You can submit the invoice directly below this message. If you have already sent it, please disregard this reminder. If you have any questions or need further information, please feel free to contact us. Thank you for your cooperation!',
                    ja: 'こんにちは！ご提出いただいた支払い：`<%= _item.cells.description_of_payment.value %>` は支払われましたが、関連する請求書がまだ届いていません。財務記録の正確性と完全性を確保するために、できるだけ早く対応する請求書を提供してください。このメッセージの下に請求書を直接提出することができます。既に送信されている場合は、このリマインダーを無視してください。ご不明な点やさらに情報が必要な場合は、お気軽にお問い合わせください。ご協力ありがとうございます！',
                  },
                  type: 'UPDATE_RECORD',
                  templateId: 'mission_invoice_collation',
                  databaseTemplateId: 'invoice_collection',
                  assignType: 'DEDICATED',
                  canCompleteManually: false,
                  recordId: '<%= _item.id %>',
                  buttonText: {
                    'zh-CN': '去提交',
                    'zh-TW': '去提交',
                    ja: '提出する',
                    en: 'Submit',
                  },
                  afterActions: [
                    // 提交发票完成后，创建通知任务，通知财务经理审发票是否正确
                    {
                      actionType: 'CREATE_MISSION',
                      templateId: 'notify_finance_manager',
                      description: {
                        'zh-CN': '通知财务经理查看提交的发票是否正确',
                        'zh-TW': '通知財務經理查看提交的發票是否正確',
                        en: 'Notify the finance manager to review the submitted invoice',
                        ja: '財務マネージャーに提出された請求書を確認するよう通知する',
                      },
                      input: {
                        type: 'MISSION_BODY',
                        mission: {
                          name: {
                            'zh-CN': '催收的发票已提交',
                            'zh-TW': '催收的發票已提交',
                            ja: '請求書の催收が提出されました',
                            en: 'Invoice collection has been submitted',
                          },
                          description: {
                            'zh-CN':
                              '`<%= record.cells.payer_name.value %>` 的 `<%= record.cells.description_of_payment.value %>` 的催收发票已提交，点击查看对应的记录。查看无误后请点击确认。',
                            'zh-TW':
                              '`<%= record.cells.payer_name.value %>` 的 `<%= record.cells.description_of_payment.value %>` 的催收發票已提交，點擊查看相對應的記錄。查看無誤後請點擊確認。',
                            ja: '`<%= record.cells.payer_name.value %>` の `<%= record.cells.description_of_payment.value %>` の請求書の催收が提出されました。対応する記録を表示するにはここをクリックしてください。確認後はここをクリックしてください。',
                            en: '`<%= record.cells.payer_name.value %>` submitted the invoice collection for `<%= record.cells.description_of_payment.value %>`. Click to view the corresponding record. Please click confirm after reviewing it correctly.',
                          },
                          type: 'REVIEW_RECORD',
                          databaseTemplateId: 'invoice_collection',
                          recordId: '<%= record.id %>',
                          buttonText: {
                            'zh-CN': '确认发票无误',
                            'zh-TW': '確認發票無誤',
                            ja: '請求書を確認する',
                            en: 'Confirm the invoice',
                          },
                          to: [
                            {
                              type: 'UNIT_ROLE',
                              roleTemplateId: 'finance_manager',
                            },
                          ],
                        },
                      },
                    },
                  ],
                  to: [
                    {
                      type: 'SPECIFY_UNITS',
                      unitIds: ['<%= _item.cells.payer_name.data[0] %>'],
                    },
                  ],
                },
              },
            },
          ],
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'invoice_collection',
      databaseType: 'DATUM',
      name: {
        'zh-CN': '发票管理',
        en: 'Invoice management',
        ja: '請求書管理',
        'zh-TW': '發票管理',
      },
      description: {
        'zh-CN': '存放提交的发票',
        en: 'Deposit of submitted invoices submitted',
        ja: '提出された請求書を保管する',
        'zh-TW': '存放提交的發票',
      },
      views: [
        {
          templateId: 'all_invoice_records',
          name: {
            'zh-CN': '所有发票记录',
            en: 'All invoice records',
            ja: 'すべての請求書記録',
            'zh-TW': '所有發票記錄',
          },
          type: 'TABLE',
          description: {
            'zh-CN': '所有发票记录都在这个视图下',
            en: 'All invoice records are under this view',
            ja: 'すべての請求書記録はこのビューの下にあります',
            'zh-TW': '所有發票記錄都在這個視圖下',
          },
        },
        {
          templateId: 'not_received_invoice_records',
          name: {
            'zh-CN': '还未没有收到发票的记录',
            en: 'Records not yet received',
            ja: 'まだ届いていない記録',
            'zh-TW': '還沒有收到發票的記錄',
          },
          type: 'TABLE',
          filters: {
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'corresponding_invoice',
                fieldType: 'ATTACHMENT',
                clause: {
                  operator: 'IsEmpty',
                },
              },
            ],
            conjunction: 'And',
          },
        },
      ],
      fields: [
        {
          type: 'MEMBER',
          templateId: 'payer_name',
          name: {
            'zh-CN': '请款人',
            en: 'Payer',
            ja: '請求担当者',
            'zh-TW': '請款人',
          },
          property: {
            many: false,
          },
        },
        {
          type: 'LONG_TEXT',
          templateId: 'description_of_payment',
          name: {
            'zh-CN': '付款事由',
            en: 'Reason for payment',
            ja: '支払いの理由',
            'zh-TW': '付款事由',
          },
        },
        {
          type: 'CURRENCY',
          templateId: 'payment_amount',
          name: {
            'zh-CN': '付款金额',
            en: 'Payment amount',
            ja: '支払い金額',
            'zh-TW': '付款金額',
          },
          property: {
            symbol: '¥',
          },
        },
        {
          type: 'SINGLE_SELECT',
          name: {
            'zh-CN': '付款方式',
            en: 'Payment method',
            ja: '支払い方法',
            'zh-TW': '付款方式',
          },
          templateId: 'type_of_payment',
          property: {
            options: [
              { name: 'Bank Card', templateId: 'type1', color: 'blue' },
              { name: 'Cash', templateId: 'type2', color: 'green' },
              { name: 'Draft', templateId: 'type3', color: 'deepPurple' },
              { name: 'Wire Transfer', templateId: 'type4', color: 'yellow' },
              { name: 'Credit', templateId: 'type5', color: 'orange' },
              { name: 'Check', templateId: 'type6', color: 'blackBlue' },
              { name: 'Other', templateId: 'type7', color: 'red' },
            ],
          },
        },
        {
          type: 'DATETIME',
          templateId: 'date_of_payment',
          name: {
            'zh-CN': '付款日期',
            en: 'Payment date',
            ja: '支払い日',
            'zh-TW': '付款日期',
          },
          property: {
            timeZone: 'Asia/Shanghai',
            dateFormat: 'YYYY/MM/DD',
            includeTime: false,
          },
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'name_of_recipient',
          name: {
            'zh-CN': '收款人全称',
            en: 'Recipient full name',
            ja: '受取人氏名',
            'zh-TW': '收款人全名',
          },
        },
        {
          type: 'NUMBER',
          templateId: 'bank_account_number',
          name: {
            'zh-CN': '银行账号',
            en: 'Bank account number',
            ja: '銀行口座番号',
            'zh-TW': '銀行帳號',
          },
          property: {
            precision: 0,
            symbol: '',
          },
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'bank_account_name',
          name: {
            'zh-CN': '开户行',
            en: 'Bank Name',
            ja: '銀行名',
            'zh-TW': '開戶行',
          },
        },
        {
          type: 'ATTACHMENT',
          templateId: 'corresponding_invoice',
          name: {
            'zh-CN': '对应的发票',
            en: 'Corresponding invoice',
            ja: '対応する請求書',
            'zh-TW': '對應的發票',
          },
        },
      ],
      records: [
        {
          data: {
            description_of_payment: 'Consulting fees for the month of July 2024',
            payment_amount: '5000',
            type_of_payment: 'Credit',
            date_of_payment: '2024/07/18',
            name_of_recipient: 'Jane Doe',
            bank_account_number: '1234567890123456789',
            bank_account_name: 'Chase Bank',
          },
        },
        {
          data: {
            description_of_payment: 'Software development services for the month of July 2024',
            payment_amount: '8000',
            type_of_payment: 'Bank Card',
            date_of_payment: '2024/07/18',
            name_of_recipient: 'Emily Davis',
            bank_account_number: '9876543210987654321',
            bank_account_name: 'Bank of America',
          },
        },
        {
          data: {
            description_of_payment: 'Equipment purchase payment for the month of July 2024',
            payment_amount: '12000',
            type_of_payment: 'Bank Card',
            date_of_payment: '2024/07/18',
            name_of_recipient: 'Sarah Taylor',
            bank_account_number: '5678901234567890123',
            bank_account_name: 'Wells Fargo',
          },
        },
      ],
    },
  ],

  // extensions: [],
};

export default template;
