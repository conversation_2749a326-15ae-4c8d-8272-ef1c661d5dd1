import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  schemaVersion: 'v1',
  templateId: 'x-ai-automated-tweets',
  author: '<PERSON><PERSON> <<EMAIL>>',
  name: {
    'zh-CN': 'AI 自动发布 X 推文',
    'zh-TW': 'AI 自動發布 X 推文',
    ja: 'AI による X ツイートの自動投稿',
    en: 'AI Automated X Tweets',
  },
  cover: '/assets/template/template-cover-x-ai-automated-tweets.png',
  description: {
    'zh-CN':
      '您可以使用该模板，实现 AI 自动发布 X(Twitter) 推文，读取数据表中准备好的推文资料，自动发布推文，帮助您提高社交媒体的曝光度，增加粉丝互动。',
    'zh-TW':
      '您可以使用該模板，實現 AI 自動發布 X(Twitter) 推文，讀取數據表中準備好的推文資料，自動發布推文，幫助您提高社交媒體的曝光度，增加粉絲互動。',
    ja: 'このテンプレートを使用すると、AI による X (Twitter) ツイートの自動投稿を実現できます。データテーブルに準備されたツイートデータを読み込み、ツイートを自動的に投稿することで、ソーシャルメディアの露出を高め、ファンとの相互作用を増やすのに役立ちます。',
    en: 'You can use this template to achieve AI automated X(Twitter) tweets, read the prepared tweet content in the database, and automatically post tweets to help you increase the exposure of social media and increase fan interaction.',
  },
  keywords: {
    en: 'AI, Automation, Tweets, Social Media, Twitter',
    'zh-CN': 'AI, 自动化, 推文, 社交媒体, Twitter',
    'zh-TW': 'AI, 自動化, 推文, 社交媒體, Twitter',
    ja: 'AI, 自動化, ツイート, ソーシャルメディア, Twitter',
  },
  personas: {
    en: 'social-media-manager, marketer, influencer, business-owner',
    'zh-CN': '社交媒体管理员, 市场营销人员, 网红, 企业主',
    'zh-TW': '社交媒體管理員, 市場營銷人員, 網紅, 企業主',
    ja: 'ソーシャルメディアマネージャー, マーケター, インフルエンサー, ビジネスオーナー',
  },
  useCases: {
    en: 'scheduling promotional tweets, monitoring tweet performance, automating customer engagement, managing multiple accounts, creating tweet campaigns, tracking social media trends, launching product announcements, running contests, engaging with followers, tracking campaign performance, sharing user-generated content, responding to customer inquiries, boosting brand awareness, promoting events, showcasing products, sharing industry news, building a personal brand, increasing social media presence, collaborating with brands, automating content calendar, driving traffic to website, sharing behind-the-scenes content, announcing partnerships, providing customer support',
    'zh-CN':
      '安排促销推文, 监控推文表现, 自动化客户互动, 管理多个账户, 创建推文活动, 跟踪社交媒体趋势, 发布产品公告, 举办竞赛, 与粉丝互动, 跟踪活动表现, 分享用户生成的内容, 回复客户查询, 提高品牌知名度, 宣传活动, 展示产品, 分享行业新闻, 塑造个人品牌, 增加社交媒体曝光, 与品牌合作, 自动化内容日历, 引流到网站, 分享幕后内容, 宣布合作伙伴关系, 提供客户支持',
    'zh-TW':
      '安排促銷推文, 監控推文表現, 自動化客戶互動, 管理多個帳戶, 創建推文活動, 跟蹤社交媒體趨勢, 發布產品公告, 舉辦競賽, 與粉絲互動, 跟蹤活動表現, 分享用戶生成的內容, 回覆客戶查詢, 提高品牌知名度, 宣傳活動, 展示產品, 分享行業新聞, 塑造個人品牌, 增加社交媒體曝光, 與品牌合作, 自動化內容日曆, 引流到網站, 分享幕後內容, 宣布合作夥伴關係, 提供客戶支持',
    ja: 'プロモーションツイートのスケジューリング, ツイートパフォーマンスの監視, 顧客エンゲージメントの自動化, 複数アカウントの管理, ツイートキャンペーンの作成, ソーシャルメディアトレンドの追跡, 製品発表の開始, コンテストの実施, フォロワーとの交流, キャンペーンパフォーマンスの追跡, ユーザー生成コンテンツの共有, 顧客の問い合わせに対応, ブランド認知度の向上, イベントのプロモーション, 製品の紹介, 業界ニュースの共有, 個人ブランドの構築, ソーシャルメディアの存在感の向上, ブランドとのコラボレーション, コンテンツカレンダーの自動化, ウェブサイトへのトラフィック誘導, 舞台裏コンテンツの共有, パートナーシップの発表, カ',
  },
  version: '1.1.3',
  category: ['automation', 'integration'],
  initMissions: [
    {
      name: {
        en: '🚀 Template Guide',
        'zh-CN': '🚀 模板指南',
        'zh-TW': '🚀 模板指南',
        ja: '🚀 テンプレートガイド',
      },
      type: 'READ_TEMPLATE_README',
      templateId: 'x-ai-automated-tweets',
      time: 10,
      beforeText: {
        title: {
          'zh-CN': '恭喜您，模板已经安装完毕',
          'zh-TW': '恭喜您，模板已經安裝完畢',
          ja: 'おめでとうございます, テンプレートのインストールが完了しました',
          en: 'Congratulations, the template has been installed',
        },
        description: {
          'zh-CN': '下一步请您花几分钟阅读模板的使用指南。',
          'zh-TW': '下一步請您花幾分鐘閱讀模板的使用指南。',
          en: 'Next, please take a few minutes to read the guide on how to use the template.',
          ja: '次に、テンプレートの使用方法についてのチュートリアルを数分間お読みください。',
        },
      },
      assignType: 'DEDICATED',
      forcePopup: true,
      wizardGuideId: 'COMMON_AUTOMATION_TUTORIAL',
      redirect: {
        type: 'SPACE_NODE',
        nodeTemplateId: 'ato_schedule_tweets',
      },
      to: [
        {
          type: 'CURRENT_OPERATOR',
        },
      ],
    },
  ],
  resources: [
    {
      resourceType: 'AUTOMATION',
      templateId: 'ato_schedule_tweets',
      name: {
        en: 'Schedule Tweets',
        'zh-CN': '定时发推文',
        'zh-TW': '定時發推文',
        ja: 'ツイートのスケジュール',
      },
      description: {
        en: 'Schedule tweets to be posted at a specific time.',
        'zh-CN': '在特定时间发布推文。',
        'zh-TW': '在特定時間發布推文。',
        ja: '特定の時間にツイートを投稿します。',
      },
      status: 'INACTIVE',
      triggers: [
        {
          triggerType: 'SCHEDULER',
          templateId: 'trg_scheduler',
          description: {
            'zh-CN': '每天上午 10:00 触发',
            'zh-TW': '每天上午 10:00 觸發',
            ja: '毎日午前 10 時にトリガー',
            en: 'Trigger every day at 10 am',
          },
          input: {
            type: 'SCHEDULER',
            scheduler: {
              timezone: 'AUTO',
              datetime: {
                type: 'TODAY',
                hour: 10,
                minute: 0,
              },
              repeat: {
                every: {
                  type: 'DAY',
                  interval: 1,
                },
              },
            },
          },
        },
      ],
      actions: [
        {
          actionType: 'FIND_RECORDS',
          templateId: 'act_get_tweets',
          description: {
            en: 'get tweets from the database',
            'zh-CN': '从数据表中获取推文',
            'zh-TW': '從數據表中獲取推文',
            ja: 'データベースからツイートを取得',
          },
          input: {
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fld_publish_date',
                  fieldType: 'DATETIME',
                  clause: {
                    operator: 'Is',
                    value: ['Today'],
                  },
                },
              ],
            },
            databaseTemplateId: 'db_tweets',
          },
        },
        {
          templateId: 'act_loop_tweets',
          description: {
            en: 'Loop through the tweets',
            'zh-CN': '循环遍历推文',
            'zh-TW': '循環遍歷推文',
            ja: 'ツイートをループ',
          },
          actionType: 'LOOP',
          input: {
            type: 'PREV_ACTION',
            actionTemplateId: 'act_get_tweets',
            path: 'records',
          },
          actions: [
            {
              description: {
                en: 'Upload media to Twitter',
                'zh-CN': '上传媒体到 Twitter',
                'zh-TW': '上傳媒體到 Twitter',
                ja: 'メディアを Twitter にアップロード',
              },
              actionType: 'TWITTER_UPLOAD_MEDIA',
              templateId: 'act_upload_media',
              input: {
                urlType: 'INTEGRATION',
                type: 'TWITTER_UPLOAD_MEDIA',
                integrationId: '',
                data: {
                  mediaUrls: '<%= JSON.stringify(_item.cells.fld_media.data) %>',
                },
              },
            },
            {
              description: {
                en: 'Create a tweet with the authorizer account',
                'zh-CN': '使用授权账号创建推文',
                'zh-TW': '使用授權帳號創建推文',
                ja: '認証済みアカウントでツイートを作成',
              },
              actionType: 'X_CREATE_TWEET',
              templateId: 'act_create_a_tweet',
              input: {
                urlType: 'INTEGRATION',
                type: 'X_CREATE_TWEET',
                authMethod: 'OAUTH1',
                integrationId: '',
                data: {
                  text: '<%= _item.cells.fld_tweet_content.value %>',
                  mediaIds: '<%= JSON.stringify(_itemActions.act_upload_media.mediaIds) %>',
                },
              },
            },
          ],
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'db_tweets',
      databaseType: 'DATUM',
      name: {
        en: 'Tweet Content',
        'zh-CN': 'X 推文内容',
        'zh-TW': 'X 推文內容',
        ja: 'X ツイートの内容',
      },
      views: [
        {
          templateId: 'viw_all',
          name: {
            en: 'All Tweets',
            'zh-CN': '所有推文',
            'zh-TW': '所有推文',
            ja: 'すべてのツイート',
          },
          type: 'TABLE',
        },
      ],
      records: [
        {
          data: {
            fld_tweet_content: 'Excited to announce our new product launch next week! 🚀 #innovation',
            fld_media: [
              {
                name: 'bika-logo-1280.png',
                id: 'tplattNTONsg4v9YRxRq05U6w3L',
                path: 'template/tplattNTONsg4v9YRxRq05U6w3L.png',
                bucket: 'bika-dev',
                mimeType: 'image/png',
                size: 222289,
              },
            ],
          },
          values: {
            fld_tweet_content: 'Excited to announce our new product launch next week! 🚀 #innovation',
            fld_media: ['bika-logo-1280.png'],
          },
        },
        {
          data: {
            fld_tweet_content: 'Had an amazing time at the tech conference today. Great insights! #TechLife',
            fld_media: [
              {
                name: 'bika-logo-1280.png',
                id: 'tplattNTONsg4v9YRxRq05U6w3L',
                path: 'template/tplattNTONsg4v9YRxRq05U6w3L.png',
                bucket: 'bika-dev',
                mimeType: 'image/png',
                size: 222289,
              },
            ],
          },
          values: {
            fld_tweet_content: 'Had an amazing time at the tech conference today. Great insights! #TechLife',
            fld_media: ['bika-logo-1280.png'],
          },
        },
        {
          data: {
            fld_tweet_content: 'Remember to stay hydrated and take breaks. #SelfCare #HealthyLiving',
            fld_media: [
              {
                name: 'bika-logo-1280.png',
                id: 'tplattNTONsg4v9YRxRq05U6w3L',
                path: 'template/tplattNTONsg4v9YRxRq05U6w3L.png',
                bucket: 'bika-dev',
                mimeType: 'image/png',
                size: 222289,
              },
            ],
          },
          values: {
            fld_tweet_content: 'Remember to stay hydrated and take breaks. #SelfCare #HealthyLiving',
            fld_media: ['bika-logo-1280.png'],
          },
        },
        {
          data: {
            fld_tweet_content: 'Working on some exciting new features. Stay tuned! #coding #developer',
            fld_media: [
              {
                name: 'bika-logo-1280.png',
                id: 'tplattNTONsg4v9YRxRq05U6w3L',
                path: 'template/tplattNTONsg4v9YRxRq05U6w3L.png',
                bucket: 'bika-dev',
                mimeType: 'image/png',
                size: 222289,
              },
            ],
          },
          values: {
            fld_tweet_content: 'Working on some exciting new features. Stay tuned! #coding #developer',
            fld_media: ['bika-logo-1280.png'],
          },
        },
        {
          data: {
            fld_tweet_content: 'Just released a new blog post on AI trends in 2024. Check it out! #AI #TechTrends',
            fld_media: [
              {
                name: 'bika-logo-1280.png',
                id: 'tplattNTONsg4v9YRxRq05U6w3L',
                path: 'template/tplattNTONsg4v9YRxRq05U6w3L.png',
                bucket: 'bika-dev',
                mimeType: 'image/png',
                size: 222289,
              },
            ],
          },
          values: {
            fld_tweet_content: 'Just released a new blog post on AI trends in 2024. Check it out! #AI #TechTrends',
            fld_media: ['bika-logo-1280.png'],
          },
        },
      ],
      fields: [
        {
          templateId: 'fld_tweet_content',
          name: {
            en: 'Content',
            'zh-CN': '内容',
            'zh-TW': '內容',
            ja: '内容',
          },
          type: 'LONG_TEXT',
          required: true,
        },
        {
          templateId: 'fld_media',
          name: {
            en: 'Media',
            'zh-CN': '媒体',
            'zh-TW': '媒體',
            ja: 'メディア',
          },
          type: 'ATTACHMENT',
        },
        {
          templateId: 'fld_publish_date',
          name: {
            en: 'Posting Date',
            'zh-CN': '发布日期',
            'zh-TW': '發布日期',
            ja: '公開日',
          },
          type: 'DATETIME',
          property: {
            includeTime: false,
            dateFormat: 'YYYY-MM-DD',
          },
        },
      ],
    },
  ],
  ignoreChanged: ['resources[0].status', 'resources[0].actions[1].input.integrationId'],
  // extensions: [],
};

export default template;
