{"schemaVersion": "v1", "author": "<PERSON> <<EMAIL>>", "visibility": "WAITING_LIST", "templateId": "youtube-to-facebook-sharing", "name": {"en": "YouTube to Facebook Sharing", "zh-CN": "YouTube到Facebook分享", "zh-TW": "YouTube到Facebook分享", "ja": "YouTubeからFacebookへの共有"}, "cover": "/assets/template/template-cover-youtube-to-facebook-sharing.png", "description": {"en": "Automatically cross-post new YouTube videos to Facebook to maximize reach and viewer engagement.", "zh-CN": "自动将新的YouTube视频交叉发布到Facebook，以最大化覆盖范围和观众参与度。", "zh-TW": "自動將新的YouTube視頻交叉發布到Facebook，以最大化覆蓋範圍和觀眾參與度。", "ja": "新しいYouTube動画をFacebookに自動クロスポストして、リーチと視聴者エンゲージメントを最大化します。"}, "keywords": "Video cross-posting, YouTube marketing, Facebook engagement, Social media strategy, Content distribution, Audience interaction", "personas": {"zh-CN": "社交媒体经理, 数字营销专家, 内容创作者, 品牌策略师", "zh-TW": "社交媒體經理, 數字營銷專家, 內容創作者, 品牌策略師", "ja": "ソーシャルメディアマネージャー, デジタルマーケター, コンテンツクリエーター, ブランドストラテジスト", "en": "Social Media Managers, Digital Marketers, Content Creators, Brand Strategists"}, "useCases": {"en": "Automate YouTube sharing, Maximize video reach, Enhance Facebook interaction, Streamline content distribution, Optimize social media campaigns, Track engagement metrics, Increase online presence, Foster community engagement, Boost brand visibility, Utilize cross-platform strategy, Enhance viewer retention, Improve content engagement, Schedule automated posts, Leverage video content, Drive audience engagement, Increase content visibility, Monitor viewer analytics, Improve campaign performance, Engage with followers, Optimize posting times, Use data-driven insights, Create cohesive content, Build online community, Promote brand awareness, Achieve marketing goals", "zh-CN": "自动分享视频, 最大化视频覆盖, 增强Facebook互动, 简化内容分发, 优化社交活动, 跟踪互动数据, 提升在线存在, 促进社区互动, 提高品牌知名, 利用跨平台策略, 提升观众留存, 提高内容吸引, 安排自动发布, 利用视频内容, 驱动观众参与, 提升内容曝光, 监控观众分析, 改善活动表现, 与粉丝互动, 优化发布时间, 使用数据洞察, 创建一致内容, 构建在线社区, 宣传品牌知名, 实现营销目标", "zh-TW": "自動分享視頻, 最大化視頻覆蓋, 增強Facebook互動, 簡化內容分發, 優化社交活動, 跟踪互動數據, 提升在線存在, 促進社區互動, 提高品牌知名, 利用跨平台策略, 提升觀眾留存, 提高內容吸引, 安排自動發布, 利用視頻內容, 駆動觀眾參與, 提升內容曝光, 監控觀眾分析, 改善活動表現, 與粉絲互動, 優化發佈時間, 使用數據洞察, 創建一致內容, 構建在線社區, 宣傳品牌知名, 實現營銷目標", "ja": "自動でYouTubeを共有, ビデオリーチを最大化, Facebookでのインタラクションを強化, コンテンツ配布を合理化, ソーシャル活動を最適化, エンゲージメントデータを追跡, オンラインプレゼンスを向上, コミュニティエンゲージメントを促進, ブランドの認知度を向上, クロスプラットフォーム戦略を活用, 視聴者リテンションを向上, コンテンツエンゲージメントを改善, 自動投稿をスケジュール, ビデオコンテンツを活用, 視聴者のエンゲージメントを促進, コンテンツの可視性を向上, 視聴者分析を監視, キャンペーンのパフォーマンスを改善, フォロワーとのエンゲージメント, 投稿時間の最適化, データドリブンの洞察を使用, 一貫したコンテンツを作成, オンラインコミュニティを構築, ブランド認知を促進, マーケティング目標を達成"}, "version": "0.1.9", "category": "marketing", "initMissions": [], "resources": [{"resourceType": "AUTOMATION", "name": {"zh-CN": "新视频自动同步Facebook", "zh-TW": "新視頻自動同步Facebook", "ja": "新しいビデオを自動的にFacebookに同期", "en": "New Video to Facebook"}, "templateId": "to_be_updated", "description": {"zh-CN": "发布新Youtube视频时，自动同步Facebook", "zh-TW": "發佈新YouTube視頻時，自動同步Facebook", "ja": "新しいYouTubeビデオを公開すると、Facebookに自動的に同期します", "en": "Automatically sync Facebook when new YouTube video is published"}, "status": "INACTIVE", "triggers": [{"triggerType": "DUMMY_TRIGGER", "description": {"zh-CN": "当特定YouTube频道发布新视频时", "zh-TW": "當特定YouTube頻道發布新視頻時", "ja": "特定のYouTubeチャンネルが新しいビデオを公開したとき", "en": "When a specific YouTube channel publishes a new video"}}], "actions": [{"actionType": "DUMMY_ACTION", "description": {"en": "Create a new page post", "zh-CN": "创建新的页面帖子", "zh-TW": "創建新的頁面貼文", "ja": "新しいページの投稿を作成する"}}, {"actionType": "DUMMY_ACTION", "description": {"en": "Create a new record with facebook post url", "zh-CN": "用Facebook帖子链接创建新记录", "zh-TW": "用Facebook貼文連結創建新記錄", "ja": "Facebookの投稿URLで新しいレコードを作成する"}}]}, {"resourceType": "DATABASE", "templateId": "to_be_updated", "databaseType": "DATUM", "name": {"en": "Facebook Post Record Database", "zh-CN": "Facebook发布记录数据库", "zh-TW": "Facebook發佈記錄數據庫", "ja": "Facebook投稿記録データベース"}, "description": {"en": "Database to store Facebook post URLs for sharing new YouTube videos", "zh-CN": "用于存储分享新YouTube视频的Facebook帖子URL的数据库", "zh-TW": "用於存儲分享新YouTube視頻的Facebook帖子URL的數據庫", "ja": "新しいYouTubeビデオを共有するためのFacebook投稿URLを保存するデータベース"}, "fields": []}], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}