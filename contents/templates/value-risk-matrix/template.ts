import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'value-risk-matrix',
  name: 'Value-Risk Matrix',
  description:
    'Manage tasks by prioritizing them based on risk and value, and trigger automated notifications for high-priority tasks',
  cover: '/assets/template/value-risk-matrix/value-risk-matrix.png',
  author: '<PERSON> <<EMAIL>>',
  category: ['project'],
  keywords: 'task management, risk prioritization, value assessment, automated notifications, project efficiency',
  useCases:
    'Identify high-risk tasks, Set task priorities based on value, Automate notifications for urgent tasks, Monitor task progress effectively, Balance team workload, Manage deadlines and risks, Track task risks and metrics, Identify process bottlenecks, Recommend task optimizations, Monitor high-priority items, Generate task reports, Evaluate value-risk balance, Assign tasks based on priority, Provide feedback on task risks, Facilitate task collaboration, Track completion of key tasks, Mitigate task risks proactively, Review task value metrics, Identify high-value opportunities, Forecast risk trends, Prioritize strategic tasks, Align tasks with business goals, Evaluate resource allocation',
  personas: 'Project Manager, Operations Analyst, Team Leader, Business Strategist',
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.3',
  resources: [
    {
      resourceType: 'DATABASE',
      templateId: 'datKSg2MN3OEdNnaiapntdis',
      name: 'Process Changes',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwq7HZbS0P9sLdS3XSuorIL',
          name: 'All Features',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldfkoZ4AFAPJeNB1lP1mLDu',
              hidden: false,
              width: 282,
            },
            {
              templateId: 'fldpqtozRw1IWYC7SE2yocRQ',
              hidden: false,
              width: 150,
            },
            {
              templateId: 'fldJaqjsMQ5yTP7r0Z918BNL',
              hidden: false,
            },
            {
              templateId: 'fldF91ij7tSCpHIuFSIZHrQv',
              hidden: false,
            },
            {
              templateId: 'fld4ukPX5SOLgx9Xg9F7097t',
              hidden: false,
              width: 150,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'viwPd6z22RbTXU5Fdwe9FExf',
          name: 'Priority Grouping',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldfkoZ4AFAPJeNB1lP1mLDu',
              hidden: false,
            },
            {
              templateId: 'fldpqtozRw1IWYC7SE2yocRQ',
              hidden: false,
            },
            {
              templateId: 'fldF91ij7tSCpHIuFSIZHrQv',
              hidden: false,
            },
            {
              templateId: 'fld4ukPX5SOLgx9Xg9F7097t',
              hidden: false,
            },
            {
              templateId: 'fldJaqjsMQ5yTP7r0Z918BNL',
              hidden: false,
            },
          ],
          groups: [
            {
              fieldTemplateId: 'fldF91ij7tSCpHIuFSIZHrQv',
              asc: true,
            },
            {
              fieldTemplateId: 'fld4ukPX5SOLgx9Xg9F7097t',
              asc: true,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldfkoZ4AFAPJeNB1lP1mLDu',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Single Line Text',
            ja: 'シングルテキスト',
            'zh-CN': 'Process Change',
            'zh-TW': '單行文本',
          },
          primary: true,
        },
        {
          type: 'MULTI_SELECT',
          templateId: 'fldpqtozRw1IWYC7SE2yocRQ',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Multi Select',
            ja: '複数選択',
            'zh-CN': 'Change Category',
            'zh-TW': '多選',
          },
          property: {
            options: [
              {
                id: '1',
                name: 'Distribution',
                color: 'deepPurple5',
              },
              {
                id: '2',
                name: 'Customer',
                color: 'indigo5',
              },
              {
                id: 'optnuKetHu8U7gQQJ1uKqPj4',
                name: 'Employees',
                color: 'indigo',
              },
              {
                id: 'optnbbEtiA3iaygCJaV1JNeR',
                name: 'Product',
                color: 'teal',
              },
              {
                id: 'optL0gbwa7tXqP03LNDMlyJc',
                name: 'Industry',
                color: 'tangerine',
              },
            ],
            defaultValue: [],
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldF91ij7tSCpHIuFSIZHrQv',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Attachment',
            ja: '添付ファイル',
            'zh-CN': 'Value Level',
            'zh-TW': '附件',
          },
          property: {
            options: [
              {
                id: 'optCzb3YfVEUSMI0NVv8DWUb',
                name: 'High Value',
                color: 'deepPurple5',
              },
              {
                id: 'optiPkAncnaN0SRi9i4jBTDH',
                name: 'Low Value',
                color: 'blue',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fld4ukPX5SOLgx9Xg9F7097t',
          privilege: 'NAME_EDIT',
          name: 'Risk Level',
          property: {
            options: [
              {
                id: 'optzIpiZCwUuxhvBu4j4pjD7',
                name: 'High Risk',
                color: 'indigo5',
              },
              {
                id: 'optOIPkh79Dk3Ms4kfjMrw6i',
                name: 'Low Risk',
                color: 'green5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'fldJaqjsMQ5yTP7r0Z918BNL',
          privilege: 'NAME_EDIT',
          name: 'Priority Level',
          property: {
            expressionTemplate:
              'IF({fldF91ij7tSCpHIuFSIZHrQv}="High Value",IF({fld4ukPX5SOLgx9Xg9F7097t}="High Risk","Medium",IF({fld4ukPX5SOLgx9Xg9F7097t}="Low Risk","High","")),IF({fld4ukPX5SOLgx9Xg9F7097t}="Low Risk","Medium",IF({fld4ukPX5SOLgx9Xg9F7097t}="High Risk","Low","")))',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recdxarDhf4pq59dCl41JY0F',
          data: {
            fld4ukPX5SOLgx9Xg9F7097t: ['optzIpiZCwUuxhvBu4j4pjD7'],
            fldF91ij7tSCpHIuFSIZHrQv: ['optiPkAncnaN0SRi9i4jBTDH'],
            fldJaqjsMQ5yTP7r0Z918BNL: null,
            fldfkoZ4AFAPJeNB1lP1mLDu: 'Competitor Onsight',
            fldpqtozRw1IWYC7SE2yocRQ: ['optL0gbwa7tXqP03LNDMlyJc'],
          },
          values: {
            fld4ukPX5SOLgx9Xg9F7097t: ['High Risk'],
            fldF91ij7tSCpHIuFSIZHrQv: ['Low Value'],
            fldJaqjsMQ5yTP7r0Z918BNL: 'Low',
            fldfkoZ4AFAPJeNB1lP1mLDu: 'Competitor Onsight',
            fldpqtozRw1IWYC7SE2yocRQ: ['Industry'],
          },
        },
        {
          templateId: 'recKwJwusMuz1HDd0GaHe9wT',
          data: {
            fld4ukPX5SOLgx9Xg9F7097t: ['optOIPkh79Dk3Ms4kfjMrw6i'],
            fldF91ij7tSCpHIuFSIZHrQv: ['optCzb3YfVEUSMI0NVv8DWUb'],
            fldJaqjsMQ5yTP7r0Z918BNL: null,
            fldfkoZ4AFAPJeNB1lP1mLDu: 'Competitor Analysis',
            fldpqtozRw1IWYC7SE2yocRQ: ['optL0gbwa7tXqP03LNDMlyJc'],
          },
          values: {
            fld4ukPX5SOLgx9Xg9F7097t: ['Low Risk'],
            fldF91ij7tSCpHIuFSIZHrQv: ['High Value'],
            fldJaqjsMQ5yTP7r0Z918BNL: 'High',
            fldfkoZ4AFAPJeNB1lP1mLDu: 'Competitor Analysis',
            fldpqtozRw1IWYC7SE2yocRQ: ['Industry'],
          },
        },
        {
          templateId: 'recNzUIGeg5rEE2A5s93vEnD',
          data: {
            fld4ukPX5SOLgx9Xg9F7097t: ['optzIpiZCwUuxhvBu4j4pjD7'],
            fldF91ij7tSCpHIuFSIZHrQv: ['optCzb3YfVEUSMI0NVv8DWUb'],
            fldJaqjsMQ5yTP7r0Z918BNL: null,
            fldfkoZ4AFAPJeNB1lP1mLDu: 'CRM System Overhaul',
            fldpqtozRw1IWYC7SE2yocRQ: ['2'],
          },
          values: {
            fld4ukPX5SOLgx9Xg9F7097t: ['High Risk'],
            fldF91ij7tSCpHIuFSIZHrQv: ['High Value'],
            fldJaqjsMQ5yTP7r0Z918BNL: 'Medium',
            fldfkoZ4AFAPJeNB1lP1mLDu: 'CRM System Overhaul',
            fldpqtozRw1IWYC7SE2yocRQ: ['Customer'],
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'atoamgAcRo0arYVabCxI7v6j',
      name: 'Priority Level',
      triggers: [
        {
          triggerType: 'RECORD_MATCH',
          templateId: 'trgZkpVtGxeNoQJkVOq7wSGs',
          description: 'When the task is assessed as high priority',
          input: {
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fldJaqjsMQ5yTP7r0Z918BNL',
                  fieldType: 'FORMULA',
                  clause: {
                    operator: 'Is',
                    value: 'High',
                  },
                },
              ],
            },
            databaseTemplateId: 'datKSg2MN3OEdNnaiapntdis',
          },
        },
      ],
      actions: [
        {
          templateId: 'actnr9ES3RiX1C4jBKWKC3SV',
          description: 'Automatically send messages to Slack',
          actionType: 'SLACK_WEBHOOK',
          input: {
            type: 'SLACK_WEBHOOK',
            data: {
              msgtype: 'text',
              text:
                'Attention: This task has been assessed as high priority!!!  \n' +
                '<%= _triggers.trgZkpVtGxeNoQJkVOq7wSGs.record.cells.fldfkoZ4AFAPJeNB1lP1mLDu.value %>    \n' +
                '<%= _triggers.trgZkpVtGxeNoQJkVOq7wSGs.record.cells.fldJaqjsMQ5yTP7r0Z918BNL.value %>    \n' +
                'View the link:<%= _triggers.trgZkpVtGxeNoQJkVOq7wSGs.record.url %>',
            },
            urlType: 'URL',
            url: '',
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
