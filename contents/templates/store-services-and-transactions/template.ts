import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'store-services-and-transactions',
  name: {
    en: 'Store Services and Transactions',
    'zh-CN': '门店服务与交易',
  },
  description: {
    en: 'This template covers basic functions such as product inventory, transaction records, and membership management, and is further equipped with AI-powered intelligent data analysis and strategy formulation. It provides comprehensive business operation support for stores, significantly enhancing management efficiency and customer service levels, and boosting store performance.',
    'zh-CN':
      '此模板涵盖产品清单、交易记录、会员管理等基础功能，更搭载AI智能数据分析与策略制定，为门店提供全面业务运营支持，显著提升管理效率与客户服务水平，助力门店业绩增长。',
  },
  cover: '/assets/template/store-services-and-transactions/store-services-and-transactions.png',
  author: 'Tianlu <<EMAIL>>',
  category: ['project', 'sales'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.2',
  resources: [
    {
      resourceType: 'DASHBOARD',
      templateId: 'dsbZys83dzWuRSYwHLJYzgYA',
      name: {
        en: 'Store Dashboard',
        'zh-CN': '门店数据看板',
      },
      widgets: [
        {
          templateId: 'wdtOb4e2FD0fwv0ZBh3MAA9V',
          type: 'NUMBER',
          name: {
            en: 'Order count',
            'zh-CN': '订单数',
          },
          layout: {
            x: 0,
            y: 0,
            w: 4,
            h: 2,
          },
          summaryDescription: {
            en: 'Order count',
            'zh-CN': '订单数',
          },
          datasource: {
            databaseTemplateId: 'datybTsphMJ9BWzFCmrtVg91',
            viewTemplateId: 'viw7YXbVLymj0qB0mzR4dBwM',
            type: 'DATABASE',
            metricsType: 'COUNT_RECORDS',
          },
        },
        {
          templateId: 'wdtHe7zW3x63zppIePteKjVO',
          type: 'CHART',
          name: {
            en: 'Product',
            'zh-CN': '产品',
          },
          layout: {
            x: 6,
            y: 2,
            w: 6,
            h: 3,
          },
          datasource: {
            databaseTemplateId: 'datybTsphMJ9BWzFCmrtVg91',
            viewTemplateId: 'viw7YXbVLymj0qB0mzR4dBwM',
            type: 'DATABASE',
            chartType: 'pie',
            metricsType: 'AGGREGATION_BY_FIELD',
            metrics: {
              aggregationType: 'SUM',
              fieldTemplateId: 'fldBmWOD2MxEae49VrNKbg01',
            },
            dimensionTemplateId: 'fldtMoZhlbOasP6IgGgzGmaQ',
          },
          settings: {
            showEmptyValues: true,
            showDataTips: true,
            excludeZeroPoint: false,
            theme: 'theme_purple',
            sortByAxis: 'Y',
            sortRule: 'DESC',
          },
        },
        {
          templateId: 'wdtxfT3J0PDtImOa6rdjusuT',
          type: 'CHART',
          name: {
            en: 'Transaction Frequency Trend Chart',
            'zh-CN': '交易量趋势图',
          },
          layout: {
            x: 4,
            y: 0,
            w: 4,
            h: 2,
          },
          datasource: {
            databaseTemplateId: 'datB2fsWXUZcPb0FAiaLvxtf',
            viewTemplateId: 'viwkZ9EFtoUiG0EvN6rIHwZO',
            type: 'DATABASE',
            chartType: 'line',
            metricsType: 'AGGREGATION_BY_FIELD',
            metrics: {
              aggregationType: 'SUM',
              fieldTemplateId: 'fldkiQgUmFFhKcuMIgiPk9bh',
            },
            dimensionTemplateId: 'attachment',
          },
          settings: {
            showEmptyValues: true,
            showDataTips: true,
            excludeZeroPoint: false,
            theme: 'theme_color_1',
            sortByAxis: 'X',
            sortRule: 'ASC',
          },
        },
        {
          templateId: 'wdtXmbQQOWC5IM9GdjJGPC5Y',
          type: 'PIVOT_TABLE',
          name: {
            en: 'Product sales pivot table',
            'zh-CN': '产品销售透视表',
          },
          layout: {
            x: 0,
            y: 2,
            w: 6,
            h: 3,
          },
          datasource: {
            type: 'DATABASE',
            databaseId: 'datxHzdhuxvqoOVIjr2t5aDM',
            viewId: 'viwZ9bLKaA3BnrmQVjYsfPIr',
            fields: {
              rows: [
                {
                  fieldId: 'fldbRSK9rJQdh4TopcqzTdQC',
                },
              ],
              columns: [
                {
                  fieldId: 'fld3SywOITJCbrw1RgLtE3Q3',
                },
              ],
              values: [
                {
                  fieldId: 'fldX7tg35AsU30EG11qO8kBf',
                  aggregation: 'sum',
                },
                {
                  fieldId: 'COUNT_RECORDS',
                  aggregation: 'count',
                },
              ],
            },
          },
          settings: {
            showTotalRow: true,
          },
        },
        {
          templateId: 'wdtzGmwnaRJJkRoUjEjHk0hY',
          type: 'CHART',
          name: {
            en: 'Transaction Volume',
            'zh-CN': '交易额',
          },
          layout: {
            x: 8,
            y: 0,
            w: 4,
            h: 2,
          },
          datasource: {
            databaseTemplateId: 'datB2fsWXUZcPb0FAiaLvxtf',
            viewTemplateId: 'viwkZ9EFtoUiG0EvN6rIHwZO',
            type: 'DATABASE',
            chartType: 'bar',
            metricsType: 'AGGREGATION_BY_FIELD',
            metrics: {
              aggregationType: 'SUM',
              fieldTemplateId: 'fldkiQgUmFFhKcuMIgiPk9bh',
            },
            dimensionTemplateId: 'attachment',
          },
          settings: {
            showEmptyValues: true,
            showDataTips: true,
            excludeZeroPoint: false,
            theme: 'theme_color_1',
            sortByAxis: 'X',
            sortRule: 'ASC',
          },
        },
        {
          templateId: 'wdtNA4Kphd6jjkyDzvhsqj9J',
          type: 'CHART',
          name: {
            en: 'Member Level',
            'zh-CN': '会员等级',
          },
          layout: {
            x: 0,
            y: 5,
            w: 4,
            h: 2,
          },
          datasource: {
            databaseTemplateId: 'datVoasHuIQGnflML4iVAM9I',
            viewTemplateId: 'viwGzLUsVvrwgqeFPN0u06wv',
            type: 'DATABASE',
            chartType: 'pie',
            metricsType: 'COUNT_RECORDS',
            dimensionTemplateId: 'fld7795hqYVS2iShQMqgVBvp',
          },
          settings: {
            showEmptyValues: true,
            showDataTips: true,
            excludeZeroPoint: false,
            theme: 'theme_color_1',
            sortByAxis: 'X',
            sortRule: 'ASC',
          },
        },
        {
          templateId: 'wdt09U7CJmY2KYIPhFWdlefK',
          type: 'PIVOT_TABLE',
          name: {
            en: 'Membership Table',
            'zh-CN': '会员',
          },
          layout: {
            x: 4,
            y: 5,
            w: 4,
            h: 2,
          },
          datasource: {
            type: 'DATABASE',
            databaseId: 'datVoasHuIQGnflML4iVAM9I',
            viewId: 'viwGzLUsVvrwgqeFPN0u06wv',
            fields: {
              rows: [
                {
                  fieldId: 'fldZ7Ut6eFmlgxFw3NPO0ZuC',
                },
              ],
              columns: [
                {
                  fieldId: 'fld7795hqYVS2iShQMqgVBvp',
                },
              ],
              values: [
                {
                  fieldId: 'COUNT_RECORDS',
                  aggregation: 'count',
                },
              ],
            },
          },
        },
        {
          templateId: 'wdt3kWz7fv7pedKfo3jyTP8D',
          type: 'NUMBER',
          name: {
            en: 'Registered members count',
            'zh-CN': '注册用户数',
          },
          layout: {
            x: 8,
            y: 5,
            w: 4,
            h: 2,
          },
          targetValue: '',
          summaryDescription: {
            en: 'Registered members',
            'zh-CN': '注册用户数',
          },
          datasource: {
            databaseTemplateId: 'datVoasHuIQGnflML4iVAM9I',
            viewTemplateId: 'viwGzLUsVvrwgqeFPN0u06wv',
            type: 'DATABASE',
            metricsType: 'COUNT_RECORDS',
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'atoR2qW7MioK2J12Nlb4V96C',
      name: {
        en: 'Store Intelligent Analysis Report',
        'zh-CN': '门店智能分析报告',
      },
      description: {
        en: 'Automatically analyze and summarize store data.',
        'zh-CN': '自动统计分析本周门店数据',
      },
      triggers: [
        {
          triggerType: 'SCHEDULER',
          templateId: 'trgLyRUfxyzg4M0BWQsr0yxB',
          description: {
            en: 'Scheduled to start every Friday evening at 18:00.',
            'zh-CN': '每周五晚18:00定时启动',
          },
          input: {
            type: 'SCHEDULER',
            scheduler: {
              repeat: {
                every: {
                  type: 'WEEK',
                  interval: 1,
                  weekdays: ['FRI'],
                },
              },
              datetime: '2024-10-16T10:00:57.193Z',
            },
          },
        },
      ],
      actions: [
        {
          templateId: 'actic745OhZBoOSTsSXQVWtJ',
          description: {
            en: "Retrieve this week's transaction records.",
            'zh-CN': '本周交易记录',
          },
          actionType: 'FIND_RECORDS',
          input: {
            type: 'DATABASE_VIEW',
            viewTemplateId: 'viwbDIQXcqU1Gt5VBnTfJkGc',
            databaseTemplateId: 'datB2fsWXUZcPb0FAiaLvxtf',
          },
        },
        {
          templateId: 'actGByWwZuYLJ4OL4yBkHJyi',
          description: {
            en: 'Find popular products.',
            'zh-CN': '产品数据',
          },
          actionType: 'FIND_RECORDS',
          input: {
            type: 'DATABASE_VIEW',
            viewTemplateId: 'viw7YXbVLymj0qB0mzR4dBwM',
            databaseTemplateId: 'datybTsphMJ9BWzFCmrtVg91',
          },
        },
        {
          templateId: 'actaix8GAkqrTPIFRGaxT8Ck',
          description: {
            en: 'Find newly registered members.',
            'zh-CN': '新注册会员',
          },
          actionType: 'FIND_RECORDS',
          input: {
            type: 'DATABASE_VIEW',
            viewTemplateId: 'viwjbOHR2iDWbYrJpwgaW4Y7',
            databaseTemplateId: 'datVoasHuIQGnflML4iVAM9I',
          },
        },
        {
          templateId: 'actonICiRguMHpLwXm1d4d7T',
          description: {
            en: 'AI data analysis.',
            'zh-CN': 'AI分析数据',
          },
          actionType: 'OPENAI_GENERATE_TEXT',
          input: {
            urlType: 'INTEGRATION',
            type: 'OPENAI_GENERATE_TEXT',
            integrationId: '',
            prompt:
              'Transaction:\n' +
              "<%= _renderRecordsAsGrid(_actions.actic745OhZBoOSTsSXQVWtJ.records, ['fldmREYmiXctHbLbiWgTWvV3','fldQzKGCA9HnXIljAtCZNiBm','fldAa87YGDfUgBRhUl4QWdVq']) %>\n" +
              '\n' +
              'Product list data: \n' +
              "<%= _renderRecordsAsGrid(_actions.actGByWwZuYLJ4OL4yBkHJyi.records, ['fldbRSK9rJQdh4TopcqzTdQC','fld3SywOITJCbrw1RgLtE3Q3','fldX7tg35AsU30EG11qO8kBf','fld1GtYUxXdCKtTJCYIPDDyc']) %> \n" +
              '\n' +
              'Newly registered members:\t\n' +
              "<%= _renderRecordsAsGrid(_actions.actaix8GAkqrTPIFRGaxT8Ck.records, ['fldQR7Kf5fKSeEiIXAhNF4Ad','fldabRFKC4UqgvQiAis08Xlf','fldlUcpTpjvP0wg9iZVLrosW']) %>\n" +
              '  \n' +
              '# Example\n' +
              '#### Time Range: xxx\n' +
              '#### Number of Transaction: xxx\n' +
              '#### Transaction amount: xxx\n' +
              '#### Number of newly registered members: xxx\n' +
              '#### Ranking of popular products: Based on the "Transaction Count" in the product list data, list the top 5 rankings\n' +
              '#### Directions for improvement and suggestions:\n',
            model: 'moonshot-v1-32k',
            timeout: 499,
          },
        },
        {
          templateId: 'actjy9HDx0nZ97ktgo3UomyT',
          description: {
            en: 'Send the report to the store owner.',
            'zh-CN': '发送报告给门店老板',
          },
          actionType: 'SEND_REPORT',
          input: {
            to: [
              {
                type: 'CURRENT_OPERATOR',
              },
            ],
            markdown: '<%= _actions.actonICiRguMHpLwXm1d4d7T.body.choices[0].message.content %>',
            json: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    {
                      type: 'hardBreak',
                    },
                    {
                      type: 'variable',
                      attrs: {
                        ids: '_actions,actonICiRguMHpLwXm1d4d7T,body,choices,[0],message,content',
                        tips: '',
                        names: '执行器,OpenAI - 生成文本,body,choices,#1,message,content',
                      },
                    },
                  ],
                },
              ],
            },
            subject: "This week's store sales data overview.",
            type: 'MARKDOWN',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datybTsphMJ9BWzFCmrtVg91',
      name: {
        en: ' Product List',
        'zh-CN': '产品清单',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viw7YXbVLymj0qB0mzR4dBwM',
          name: {
            en: 'ALL',
            'zh-CN': '所有数据',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldtMoZhlbOasP6IgGgzGmaQ',
              hidden: false,
            },
            {
              templateId: 'fldlcRKZGvpjPBiNHKfFGwD8',
              hidden: false,
              width: 186,
            },
            {
              templateId: 'fldBmWOD2MxEae49VrNKbg01',
              hidden: false,
              width: 157,
            },
            {
              templateId: 'fldtaWCIRITI8MU6P9d296du',
              hidden: false,
              width: 301,
            },
            {
              templateId: 'fldMTI8k51gKSDhXeZj4tj8l',
              hidden: false,
              width: 150,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'viwOnbB5T6VtWnq1pvPlDJfx',
          name: {
            en: 'Category',
            'zh-CN': '分类视图',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldtMoZhlbOasP6IgGgzGmaQ',
              hidden: false,
            },
            {
              templateId: 'fldlcRKZGvpjPBiNHKfFGwD8',
              hidden: false,
            },
            {
              templateId: 'fldBmWOD2MxEae49VrNKbg01',
              hidden: false,
            },
            {
              templateId: 'fldMTI8k51gKSDhXeZj4tj8l',
              hidden: false,
            },
            {
              templateId: 'fldtaWCIRITI8MU6P9d296du',
              hidden: false,
            },
          ],
          groups: [
            {
              fieldTemplateId: 'fldlcRKZGvpjPBiNHKfFGwD8',
              asc: true,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'viwgTrNeeaTFPwtcGo3XYHjW',
          name: {
            en: 'Transaction Count Sorting',
            'zh-CN': '交易排序视图',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [
            {
              fieldTemplateId: 'fldMTI8k51gKSDhXeZj4tj8l',
              asc: true,
            },
          ],
          fields: [
            {
              templateId: 'fldtMoZhlbOasP6IgGgzGmaQ',
              hidden: false,
            },
            {
              templateId: 'fldlcRKZGvpjPBiNHKfFGwD8',
              hidden: false,
            },
            {
              templateId: 'fldBmWOD2MxEae49VrNKbg01',
              hidden: false,
            },
            {
              templateId: 'fldMTI8k51gKSDhXeZj4tj8l',
              hidden: false,
            },
            {
              templateId: 'fldtaWCIRITI8MU6P9d296du',
              hidden: false,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'fldtMoZhlbOasP6IgGgzGmaQ',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Product Name',
            'zh-CN': '产品名称',
          },
          primary: true,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldlcRKZGvpjPBiNHKfFGwD8',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Category',
            'zh-CN': '类别',
          },
          property: {
            options: [
              {
                id: 'optzjChC79Mf0',
                name: 'Manicure',
                color: 'green5',
              },
              {
                id: 'optql0190tvjD',
                name: 'Eyelash Extension',
                color: 'indigo',
              },
              {
                id: 'optZldGB35XY2',
                name: 'Embroidery',
                color: 'pink5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'CURRENCY',
          templateId: 'fldBmWOD2MxEae49VrNKbg01',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Price',
            'zh-CN': '价格',
          },
          property: {
            precision: 2,
            commaStyle: 'thousand',
            symbol: '$',
            symbolAlign: 'left',
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'fldMTI8k51gKSDhXeZj4tj8l',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Transaction Count',
            'zh-CN': '交易次数',
          },
          required: false,
          property: {
            expressionTemplate: 'INT(COUNTA({fldtaWCIRITI8MU6P9d296du}))',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldtaWCIRITI8MU6P9d296du',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Transaction Record',
            'zh-CN': '交易记录',
          },
          property: {
            foreignDatabaseTemplateId: 'datB2fsWXUZcPb0FAiaLvxtf',
            brotherFieldTemplateId: 'fldwQMmEwjHGY5HU7ZI1OBom',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recnVWiUQKw6eltiP1I90RHI',
          data: {
            fldBmWOD2MxEae49VrNKbg01: 1988,
            fldMTI8k51gKSDhXeZj4tj8l: 3,
            fldlcRKZGvpjPBiNHKfFGwD8: ['optZldGB35XY2'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Imitation Eyebrows',
            fldtaWCIRITI8MU6P9d296du: [
              'recyxBHTjpupC9NAyjMaFk6Q',
              'recHa5lgDbx5mq3k79vpRZyY',
              'recayFLnJl4xAgLYWuFtjODJ',
            ],
          },
          values: {
            fldMTI8k51gKSDhXeZj4tj8l: '3',
            fldlcRKZGvpjPBiNHKfFGwD8: ['Embroidery'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Imitation Eyebrows',
            fldtaWCIRITI8MU6P9d296du: ['2025042541', '2025042538', '2025042340'],
          },
        },
        {
          templateId: 'rectzIcFIW9KEkCgexlET4PK',
          data: {
            fldBmWOD2MxEae49VrNKbg01: 30,
            fldMTI8k51gKSDhXeZj4tj8l: 3,
            fldlcRKZGvpjPBiNHKfFGwD8: ['optzjChC79Mf0'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Basic Hand Care',
            fldtaWCIRITI8MU6P9d296du: [
              'rec3UJSja9Bh7ZCmCSur15Rh',
              'recyxBHTjpupC9NAyjMaFk6Q',
              'recHa5lgDbx5mq3k79vpRZyY',
            ],
          },
          values: {
            fldMTI8k51gKSDhXeZj4tj8l: '3',
            fldlcRKZGvpjPBiNHKfFGwD8: ['Manicure'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Basic Hand Care',
            fldtaWCIRITI8MU6P9d296du: ['2025042131', '2025042541', '2025042538'],
          },
        },
        {
          templateId: 'recQZNT9kPMpCsr9cStUjGOJ',
          data: {
            fldBmWOD2MxEae49VrNKbg01: 188,
            fldMTI8k51gKSDhXeZj4tj8l: 2,
            fldlcRKZGvpjPBiNHKfFGwD8: ['optql0190tvjD'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Cloud Eyelashes',
            fldtaWCIRITI8MU6P9d296du: ['recU1mdZ5y4oAdpJ4khJeMNh', 'rec4zOgLdN1XEnFJYJyCXVkH'],
          },
          values: {
            fldMTI8k51gKSDhXeZj4tj8l: '2',
            fldlcRKZGvpjPBiNHKfFGwD8: ['Eyelash Extension'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Cloud Eyelashes',
            fldtaWCIRITI8MU6P9d296du: ['2025042335', '2025042233'],
          },
        },
        {
          templateId: 'rec8PBdWtFQMXUNxWvYKCd02',
          data: {
            fldBmWOD2MxEae49VrNKbg01: 188,
            fldMTI8k51gKSDhXeZj4tj8l: 1,
            fldlcRKZGvpjPBiNHKfFGwD8: ['optZldGB35XY2'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Powder Eyebrows',
            fldtaWCIRITI8MU6P9d296du: ['recHa5lgDbx5mq3k79vpRZyY'],
          },
          values: {
            fldMTI8k51gKSDhXeZj4tj8l: '1',
            fldlcRKZGvpjPBiNHKfFGwD8: ['Embroidery'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Powder Eyebrows',
            fldtaWCIRITI8MU6P9d296du: ['2025042538'],
          },
        },
        {
          templateId: 'recJXI607DS6HCJ12DE9912N',
          data: {
            fldBmWOD2MxEae49VrNKbg01: 168,
            fldMTI8k51gKSDhXeZj4tj8l: 1,
            fldlcRKZGvpjPBiNHKfFGwD8: ['optql0190tvjD'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Black Feather Flat Eyelashes',
            fldtaWCIRITI8MU6P9d296du: ['rec4zOgLdN1XEnFJYJyCXVkH'],
          },
          values: {
            fldMTI8k51gKSDhXeZj4tj8l: '1',
            fldlcRKZGvpjPBiNHKfFGwD8: ['Eyelash Extension'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Black Feather Flat Eyelashes',
            fldtaWCIRITI8MU6P9d296du: ['2025042233'],
          },
        },
        {
          templateId: 'rec2pSvrmAGvfko9pQyxeSG5',
          data: {
            fldBmWOD2MxEae49VrNKbg01: 168,
            fldMTI8k51gKSDhXeZj4tj8l: 1,
            fldlcRKZGvpjPBiNHKfFGwD8: ['optql0190tvjD'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Baby Straight Eyelashes',
            fldtaWCIRITI8MU6P9d296du: ['recyxBHTjpupC9NAyjMaFk6Q'],
          },
          values: {
            fldMTI8k51gKSDhXeZj4tj8l: '1',
            fldlcRKZGvpjPBiNHKfFGwD8: ['Eyelash Extension'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Baby Straight Eyelashes',
            fldtaWCIRITI8MU6P9d296du: ['2025042541'],
          },
        },
        {
          templateId: 'reczgPQGvG06NBZ9RPT1GqgD',
          data: {
            fldBmWOD2MxEae49VrNKbg01: 128,
            fldMTI8k51gKSDhXeZj4tj8l: 1,
            fldlcRKZGvpjPBiNHKfFGwD8: ['optZldGB35XY2'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Mink Eyelashes',
            fldtaWCIRITI8MU6P9d296du: ['recqCZScWVNyQEDqb81dNEws'],
          },
          values: {
            fldMTI8k51gKSDhXeZj4tj8l: '1',
            fldlcRKZGvpjPBiNHKfFGwD8: ['Embroidery'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Mink Eyelashes',
            fldtaWCIRITI8MU6P9d296du: ['2025042132'],
          },
        },
        {
          templateId: 'rec3cgRT17obP3GQhzzbnZpR',
          data: {
            fldBmWOD2MxEae49VrNKbg01: 90,
            fldMTI8k51gKSDhXeZj4tj8l: 3,
            fldlcRKZGvpjPBiNHKfFGwD8: ['optzjChC79Mf0'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Natural Nail Extension',
            fldtaWCIRITI8MU6P9d296du: [
              'rec3UJSja9Bh7ZCmCSur15Rh',
              'recEQZGDWwZjNjj9jop4OnFh',
              'recfeg7S69LzD4MbNHOcCYQK',
            ],
          },
          values: {
            fldMTI8k51gKSDhXeZj4tj8l: '3',
            fldlcRKZGvpjPBiNHKfFGwD8: ['Manicure'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Natural Nail Extension',
            fldtaWCIRITI8MU6P9d296du: ['2025042131', '2025042334', '2025042539'],
          },
        },
        {
          templateId: 'recJ5LPoxJeO8peWKlxAEz9L',
          data: {
            fldBmWOD2MxEae49VrNKbg01: 40,
            fldMTI8k51gKSDhXeZj4tj8l: 3,
            fldlcRKZGvpjPBiNHKfFGwD8: ['optql0190tvjD'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Remove Eyelashes',
            fldtaWCIRITI8MU6P9d296du: [
              'recTrfryTYWpcUXNbZdajV63',
              'recHmaDPypwrkVeA93BroqOv',
              'recEQZGDWwZjNjj9jop4OnFh',
            ],
          },
          values: {
            fldMTI8k51gKSDhXeZj4tj8l: '3',
            fldlcRKZGvpjPBiNHKfFGwD8: ['Eyelash Extension'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Remove Eyelashes',
            fldtaWCIRITI8MU6P9d296du: ['2025042028', '2025042437', '2025042334'],
          },
        },
        {
          templateId: 'recaGKby8FbfoMjhE6kWBH2S',
          data: {
            fldBmWOD2MxEae49VrNKbg01: 50,
            fldMTI8k51gKSDhXeZj4tj8l: 1,
            fldlcRKZGvpjPBiNHKfFGwD8: ['optzjChC79Mf0'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Half Paste',
            fldtaWCIRITI8MU6P9d296du: ['reczcHaUfAZn9gE4nQnUay3c'],
          },
          values: {
            fldMTI8k51gKSDhXeZj4tj8l: '1',
            fldlcRKZGvpjPBiNHKfFGwD8: ['Manicure'],
            fldtMoZhlbOasP6IgGgzGmaQ: 'Half Paste',
            fldtaWCIRITI8MU6P9d296du: ['2025042436'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datVoasHuIQGnflML4iVAM9I',
      name: {
        en: 'Membership',
        'zh-CN': '会员管理',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwGzLUsVvrwgqeFPN0u06wv',
          name: {
            en: 'All',
            'zh-CN': '所有',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldLO6fy1t783s0YDA8zz7w4',
              hidden: false,
            },
            {
              templateId: 'fldZ7Ut6eFmlgxFw3NPO0ZuC',
              hidden: false,
            },
            {
              templateId: 'fldX3ogxNzsb3I9kq0iSFoq3',
              hidden: false,
            },
            {
              templateId: 'fld7795hqYVS2iShQMqgVBvp',
              hidden: false,
              width: 173,
            },
            {
              templateId: 'fld3FKIB05DzwdlwVZjEZr9y',
              hidden: false,
              width: 163,
            },
            {
              templateId: 'fldHNi2LTS0cOZ1kQWuXGGJd',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'viwjbOHR2iDWbYrJpwgaW4Y7',
          name: {
            en: "This Week's New Members",
            'zh-CN': '本周新注册会员',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fld3FKIB05DzwdlwVZjEZr9y',
                fieldType: 'DATETIME',
                clause: {
                  operator: 'Is',
                  value: ['ThisWeek'],
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldLO6fy1t783s0YDA8zz7w4',
              hidden: false,
            },
            {
              templateId: 'fldZ7Ut6eFmlgxFw3NPO0ZuC',
              hidden: false,
            },
            {
              templateId: 'fldX3ogxNzsb3I9kq0iSFoq3',
              hidden: false,
            },
            {
              templateId: 'fld7795hqYVS2iShQMqgVBvp',
              hidden: false,
              width: 200,
            },
            {
              templateId: 'fld3FKIB05DzwdlwVZjEZr9y',
              hidden: false,
            },
            {
              templateId: 'fldHNi2LTS0cOZ1kQWuXGGJd',
              hidden: false,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'PHONE',
          templateId: 'fldLO6fy1t783s0YDA8zz7w4',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Account',
            'zh-CN': '会员账号',
          },
          primary: true,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldZ7Ut6eFmlgxFw3NPO0ZuC',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Name',
            'zh-CN': '会员姓名',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldX3ogxNzsb3I9kq0iSFoq3',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Gender',
            'zh-CN': '性别',
          },
          property: {
            options: [
              {
                id: 'opt8LL6Wgt31p',
                name: 'Male',
                color: 'deepPurple',
              },
              {
                id: 'opt8VyXJSjjnY',
                name: 'Female',
                color: 'indigo',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fld7795hqYVS2iShQMqgVBvp',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Member Level',
            'zh-CN': '会员级别',
          },
          property: {
            options: [
              {
                id: 'optc61dLwdl9RsSNAFo75tiD',
                name: 'Gold',
                color: 'indigo',
              },
              {
                id: 'optwI8mIamDQQeWCGwMoi2Cr',
                name: 'Diamond',
                color: 'blue',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fld3FKIB05DzwdlwVZjEZr9y',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Registration Date',
            'zh-CN': '注册日期',
          },
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldHNi2LTS0cOZ1kQWuXGGJd',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Transaction Record',
            'zh-CN': '交易记录',
          },
          property: {
            foreignDatabaseTemplateId: 'datB2fsWXUZcPb0FAiaLvxtf',
            brotherFieldTemplateId: 'fldWf4yy8xEDLVAaUru91rj0',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recqGv9QhCSxdeNGfG8AqnkT',
          data: {
            fld3FKIB05DzwdlwVZjEZr9y: '2024-10-17T16:00:00.000Z',
            fld7795hqYVS2iShQMqgVBvp: ['optc61dLwdl9RsSNAFo75tiD'],
            fldHNi2LTS0cOZ1kQWuXGGJd: [
              'recyxBHTjpupC9NAyjMaFk6Q',
              'recHa5lgDbx5mq3k79vpRZyY',
              'reczcHaUfAZn9gE4nQnUay3c',
              'recEQZGDWwZjNjj9jop4OnFh',
            ],
            fldLO6fy1t783s0YDA8zz7w4: '+49 17612345678',
            fldX3ogxNzsb3I9kq0iSFoq3: ['opt8VyXJSjjnY'],
            fldZ7Ut6eFmlgxFw3NPO0ZuC: 'Claire',
          },
          values: {
            fld3FKIB05DzwdlwVZjEZr9y: '2024-10-17',
            fld7795hqYVS2iShQMqgVBvp: ['Gold'],
            fldHNi2LTS0cOZ1kQWuXGGJd: ['2025042541', '2025042538', '2025042436', '2025042334'],
            fldLO6fy1t783s0YDA8zz7w4: '+49 17612345678',
            fldX3ogxNzsb3I9kq0iSFoq3: ['Female'],
            fldZ7Ut6eFmlgxFw3NPO0ZuC: 'Claire',
          },
        },
        {
          templateId: 'rec2h5dSAbEvXZvaNNSFRdPW',
          data: {
            fld3FKIB05DzwdlwVZjEZr9y: '2024-10-16T16:00:00.000Z',
            fld7795hqYVS2iShQMqgVBvp: ['optc61dLwdl9RsSNAFo75tiD'],
            fldHNi2LTS0cOZ1kQWuXGGJd: [
              'recayFLnJl4xAgLYWuFtjODJ',
              'recfeg7S69LzD4MbNHOcCYQK',
              'recU1mdZ5y4oAdpJ4khJeMNh',
              'rec4zOgLdN1XEnFJYJyCXVkH',
            ],
            fldLO6fy1t783s0YDA8zz7w4: '+61 412345678',
            fldX3ogxNzsb3I9kq0iSFoq3: ['opt8LL6Wgt31p'],
            fldZ7Ut6eFmlgxFw3NPO0ZuC: 'Bob',
          },
          values: {
            fld3FKIB05DzwdlwVZjEZr9y: '2024-10-16',
            fld7795hqYVS2iShQMqgVBvp: ['Gold'],
            fldHNi2LTS0cOZ1kQWuXGGJd: ['2025042340', '2025042539', '2025042335', '2025042233'],
            fldLO6fy1t783s0YDA8zz7w4: '+61 412345678',
            fldX3ogxNzsb3I9kq0iSFoq3: ['Male'],
            fldZ7Ut6eFmlgxFw3NPO0ZuC: 'Bob',
          },
        },
        {
          templateId: 'recCZdY0OxC0WsnKAAPc1Cln',
          data: {
            fld3FKIB05DzwdlwVZjEZr9y: '2024-10-15T16:00:00.000Z',
            fld7795hqYVS2iShQMqgVBvp: ['optwI8mIamDQQeWCGwMoi2Cr'],
            fldHNi2LTS0cOZ1kQWuXGGJd: ['recHmaDPypwrkVeA93BroqOv', 'recqCZScWVNyQEDqb81dNEws'],
            fldLO6fy1t783s0YDA8zz7w4: '+1 (555)123-4567',
            fldX3ogxNzsb3I9kq0iSFoq3: ['opt8VyXJSjjnY'],
            fldZ7Ut6eFmlgxFw3NPO0ZuC: 'Alice',
          },
          values: {
            fld3FKIB05DzwdlwVZjEZr9y: '2024-10-15',
            fld7795hqYVS2iShQMqgVBvp: ['Diamond'],
            fldHNi2LTS0cOZ1kQWuXGGJd: ['2025042437', '2025042132'],
            fldLO6fy1t783s0YDA8zz7w4: '+1 (555)123-4567',
            fldX3ogxNzsb3I9kq0iSFoq3: ['Female'],
            fldZ7Ut6eFmlgxFw3NPO0ZuC: 'Alice',
          },
        },
        {
          templateId: 'recRXzvsuaITNibR3rJNkld7',
          data: {
            fld3FKIB05DzwdlwVZjEZr9y: '2025-04-21T16:00:00.000Z',
            fld7795hqYVS2iShQMqgVBvp: ['optc61dLwdl9RsSNAFo75tiD'],
            fldHNi2LTS0cOZ1kQWuXGGJd: ['rec3UJSja9Bh7ZCmCSur15Rh'],
            fldLO6fy1t783s0YDA8zz7w4: '+49 17612345666',
            fldX3ogxNzsb3I9kq0iSFoq3: ['opt8VyXJSjjnY'],
            fldZ7Ut6eFmlgxFw3NPO0ZuC: 'Cindy',
          },
          values: {
            fld3FKIB05DzwdlwVZjEZr9y: '2025-04-21',
            fld7795hqYVS2iShQMqgVBvp: ['Gold'],
            fldHNi2LTS0cOZ1kQWuXGGJd: ['2025042131'],
            fldLO6fy1t783s0YDA8zz7w4: '+49 17612345666',
            fldX3ogxNzsb3I9kq0iSFoq3: ['Female'],
            fldZ7Ut6eFmlgxFw3NPO0ZuC: 'Cindy',
          },
        },
        {
          templateId: 'recDvHVZd6jSXxYjfea0CujE',
          data: {
            fld3FKIB05DzwdlwVZjEZr9y: '2025-04-21T16:00:00.000Z',
            fld7795hqYVS2iShQMqgVBvp: ['optc61dLwdl9RsSNAFo75tiD'],
            fldHNi2LTS0cOZ1kQWuXGGJd: ['recTrfryTYWpcUXNbZdajV63'],
            fldLO6fy1t783s0YDA8zz7w4: '+61 412345679',
            fldX3ogxNzsb3I9kq0iSFoq3: ['opt8VyXJSjjnY'],
            fldZ7Ut6eFmlgxFw3NPO0ZuC: 'Tara',
          },
          values: {
            fld3FKIB05DzwdlwVZjEZr9y: '2025-04-21',
            fld7795hqYVS2iShQMqgVBvp: ['Gold'],
            fldHNi2LTS0cOZ1kQWuXGGJd: ['2025042028'],
            fldLO6fy1t783s0YDA8zz7w4: '+61 412345679',
            fldX3ogxNzsb3I9kq0iSFoq3: ['Female'],
            fldZ7Ut6eFmlgxFw3NPO0ZuC: 'Tara',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datB2fsWXUZcPb0FAiaLvxtf',
      name: {
        en: 'Transaction',
        'zh-CN': '交易记录',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwkZ9EFtoUiG0EvN6rIHwZO',
          name: {
            en: 'All records',
            'zh-CN': '所有记录',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'single_text',
              hidden: false,
              width: 164,
            },
            {
              templateId: 'attachment',
              hidden: false,
              width: 182,
            },
            {
              templateId: 'fldWf4yy8xEDLVAaUru91rj0',
              hidden: false,
            },
            {
              templateId: 'fldCCO1UfRvEUdMjfCp5Uqvh',
              hidden: false,
              width: 141,
            },
            {
              templateId: 'fldwQMmEwjHGY5HU7ZI1OBom',
              hidden: false,
            },
            {
              templateId: 'fldkiQgUmFFhKcuMIgiPk9bh',
              hidden: false,
              width: 150,
            },
            {
              templateId: 'fldrML3Sj0Hl0n9TQpNgerUG',
              hidden: false,
              width: 153,
            },
          ],
          extra: {
            isHideAllItems: false,
          },
        },
        {
          type: 'TABLE',
          templateId: 'viwbDIQXcqU1Gt5VBnTfJkGc',
          name: {
            en: 'Weekly orders',
            'zh-CN': '本周交易订单',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'attachment',
                fieldType: 'DATETIME',
                clause: {
                  operator: 'Is',
                  value: ['ThisWeek'],
                },
              },
            ],
          },
          sorts: [
            {
              fieldTemplateId: 'attachment',
              asc: true,
            },
          ],
          fields: [
            {
              templateId: 'single_text',
              hidden: false,
            },
            {
              templateId: 'attachment',
              hidden: false,
            },
            {
              templateId: 'fldkiQgUmFFhKcuMIgiPk9bh',
              hidden: false,
            },
            {
              templateId: 'fldwQMmEwjHGY5HU7ZI1OBom',
              hidden: false,
            },
            {
              templateId: 'fldrML3Sj0Hl0n9TQpNgerUG',
              hidden: false,
            },
            {
              templateId: 'fldCCO1UfRvEUdMjfCp5Uqvh',
              hidden: false,
            },
            {
              templateId: 'fldWf4yy8xEDLVAaUru91rj0',
              hidden: true,
            },
          ],
          extra: {
            isHideAllItems: false,
          },
        },
      ],
      fields: [
        {
          type: 'FORMULA',
          templateId: 'single_text',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Transaction Number',
            ja: 'シングルテキスト',
            'zh-CN': '消费流水号',
            'zh-TW': '單行文本',
          },
          required: false,
          property: {
            expressionTemplate: 'DATETIME_FORMAT({attachment},"YYYYMMDD")&{fldrML3Sj0Hl0n9TQpNgerUG}',
          },
          primary: true,
        },
        {
          type: 'DATETIME',
          templateId: 'attachment',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Date',
            ja: '添付ファイル',
            'zh-CN': '日期',
            'zh-TW': '附件',
          },
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
            autofill: true,
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldwQMmEwjHGY5HU7ZI1OBom',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Consumption Item',
            'zh-CN': '消费项目',
          },
          property: {
            foreignDatabaseTemplateId: 'datybTsphMJ9BWzFCmrtVg91',
            brotherFieldTemplateId: 'fldtaWCIRITI8MU6P9d296du',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldkiQgUmFFhKcuMIgiPk9bh',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Amount',
            'zh-CN': '消费金额',
          },
          required: false,
          property: {
            relatedLinkFieldTemplateId: 'fldwQMmEwjHGY5HU7ZI1OBom',
            lookupTargetFieldTemplateId: 'fldBmWOD2MxEae49VrNKbg01',
            lookupTargetFieldType: 'CURRENCY',
            dataType: 'NUMBER',
            lookUpLimit: 'ALL',
            rollUpType: 'SUM',
            formatting: {
              type: 'CURRENCY',
              property: {},
            },
          },
          primary: false,
        },
        {
          type: 'AUTO_NUMBER',
          templateId: 'fldrML3Sj0Hl0n9TQpNgerUG',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Auto Number',
            'zh-CN': '自动编号',
          },
          required: false,
          property: {
            nextId: 42,
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldCCO1UfRvEUdMjfCp5Uqvh',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Member Name',
            'zh-CN': '会员名',
          },
          required: false,
          property: {
            relatedLinkFieldTemplateId: 'fldWf4yy8xEDLVAaUru91rj0',
            lookupTargetFieldTemplateId: 'fldZ7Ut6eFmlgxFw3NPO0ZuC',
            lookupTargetFieldType: 'SINGLE_TEXT',
            dataType: 'STRING',
            lookUpLimit: 'FIRST',
            rollUpType: 'VALUES',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldWf4yy8xEDLVAaUru91rj0',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Consuming Member',
            'zh-CN': '会员管理',
          },
          property: {
            foreignDatabaseTemplateId: 'datVoasHuIQGnflML4iVAM9I',
            brotherFieldTemplateId: 'fldHNi2LTS0cOZ1kQWuXGGJd',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recTrfryTYWpcUXNbZdajV63',
          data: {
            attachment: '2025-04-20T04:12:09.215Z',
            single_text: '2025042028',
            fldWf4yy8xEDLVAaUru91rj0: ['recDvHVZd6jSXxYjfea0CujE'],
            fldkiQgUmFFhKcuMIgiPk9bh: 40,
            fldrML3Sj0Hl0n9TQpNgerUG: 28,
            fldwQMmEwjHGY5HU7ZI1OBom: ['recJ5LPoxJeO8peWKlxAEz9L'],
          },
          values: {
            attachment: '2025-04-20',
            single_text: '2025042028',
            fldCCO1UfRvEUdMjfCp5Uqvh: ['Tara'],
            fldWf4yy8xEDLVAaUru91rj0: ['+61 412345679'],
            fldkiQgUmFFhKcuMIgiPk9bh: ['40'],
            fldrML3Sj0Hl0n9TQpNgerUG: '28',
            fldwQMmEwjHGY5HU7ZI1OBom: ['Remove Eyelashes'],
          },
        },
        {
          templateId: 'rec3UJSja9Bh7ZCmCSur15Rh',
          data: {
            attachment: '2025-04-21T04:07:18.297Z',
            single_text: '2025042131',
            fldWf4yy8xEDLVAaUru91rj0: ['recRXzvsuaITNibR3rJNkld7'],
            fldkiQgUmFFhKcuMIgiPk9bh: 120,
            fldrML3Sj0Hl0n9TQpNgerUG: 31,
            fldwQMmEwjHGY5HU7ZI1OBom: ['rectzIcFIW9KEkCgexlET4PK', 'rec3cgRT17obP3GQhzzbnZpR'],
          },
          values: {
            attachment: '2025-04-21',
            single_text: '2025042131',
            fldCCO1UfRvEUdMjfCp5Uqvh: ['Cindy'],
            fldWf4yy8xEDLVAaUru91rj0: ['+49 17612345666'],
            fldkiQgUmFFhKcuMIgiPk9bh: ['120'],
            fldrML3Sj0Hl0n9TQpNgerUG: '31',
            fldwQMmEwjHGY5HU7ZI1OBom: ['Basic Hand Care', 'Natural Nail Extension'],
          },
        },
        {
          templateId: 'recqCZScWVNyQEDqb81dNEws',
          data: {
            attachment: '2025-04-21T04:07:49.715Z',
            single_text: '2025042132',
            fldWf4yy8xEDLVAaUru91rj0: ['recCZdY0OxC0WsnKAAPc1Cln'],
            fldkiQgUmFFhKcuMIgiPk9bh: 128,
            fldrML3Sj0Hl0n9TQpNgerUG: 32,
            fldwQMmEwjHGY5HU7ZI1OBom: ['reczgPQGvG06NBZ9RPT1GqgD'],
          },
          values: {
            attachment: '2025-04-21',
            single_text: '2025042132',
            fldCCO1UfRvEUdMjfCp5Uqvh: ['Alice'],
            fldWf4yy8xEDLVAaUru91rj0: ['+1 (555)123-4567'],
            fldkiQgUmFFhKcuMIgiPk9bh: ['128'],
            fldrML3Sj0Hl0n9TQpNgerUG: '32',
            fldwQMmEwjHGY5HU7ZI1OBom: ['Mink Eyelashes'],
          },
        },
        {
          templateId: 'rec4zOgLdN1XEnFJYJyCXVkH',
          data: {
            attachment: '2025-04-22T04:10:15.183Z',
            single_text: '2025042233',
            fldWf4yy8xEDLVAaUru91rj0: ['rec2h5dSAbEvXZvaNNSFRdPW'],
            fldkiQgUmFFhKcuMIgiPk9bh: 356,
            fldrML3Sj0Hl0n9TQpNgerUG: 33,
            fldwQMmEwjHGY5HU7ZI1OBom: ['recQZNT9kPMpCsr9cStUjGOJ', 'recJXI607DS6HCJ12DE9912N'],
          },
          values: {
            attachment: '2025-04-22',
            single_text: '2025042233',
            fldCCO1UfRvEUdMjfCp5Uqvh: ['Bob'],
            fldWf4yy8xEDLVAaUru91rj0: ['+61 412345678'],
            fldkiQgUmFFhKcuMIgiPk9bh: ['356'],
            fldrML3Sj0Hl0n9TQpNgerUG: '33',
            fldwQMmEwjHGY5HU7ZI1OBom: ['Cloud Eyelashes', 'Black Feather Flat Eyelashes'],
          },
        },
        {
          templateId: 'recEQZGDWwZjNjj9jop4OnFh',
          data: {
            attachment: '2025-04-23T04:10:53.960Z',
            single_text: '2025042334',
            fldWf4yy8xEDLVAaUru91rj0: ['recqGv9QhCSxdeNGfG8AqnkT'],
            fldkiQgUmFFhKcuMIgiPk9bh: 130,
            fldrML3Sj0Hl0n9TQpNgerUG: 34,
            fldwQMmEwjHGY5HU7ZI1OBom: ['rec3cgRT17obP3GQhzzbnZpR', 'recJ5LPoxJeO8peWKlxAEz9L'],
          },
          values: {
            attachment: '2025-04-23',
            single_text: '2025042334',
            fldCCO1UfRvEUdMjfCp5Uqvh: ['Claire'],
            fldWf4yy8xEDLVAaUru91rj0: ['+49 17612345678'],
            fldkiQgUmFFhKcuMIgiPk9bh: ['130'],
            fldrML3Sj0Hl0n9TQpNgerUG: '34',
            fldwQMmEwjHGY5HU7ZI1OBom: ['Natural Nail Extension', 'Remove Eyelashes'],
          },
        },
        {
          templateId: 'recU1mdZ5y4oAdpJ4khJeMNh',
          data: {
            attachment: '2025-04-23T04:11:00.235Z',
            single_text: '2025042335',
            fldWf4yy8xEDLVAaUru91rj0: ['rec2h5dSAbEvXZvaNNSFRdPW'],
            fldkiQgUmFFhKcuMIgiPk9bh: 188,
            fldrML3Sj0Hl0n9TQpNgerUG: 35,
            fldwQMmEwjHGY5HU7ZI1OBom: ['recQZNT9kPMpCsr9cStUjGOJ'],
          },
          values: {
            attachment: '2025-04-23',
            single_text: '2025042335',
            fldCCO1UfRvEUdMjfCp5Uqvh: ['Bob'],
            fldWf4yy8xEDLVAaUru91rj0: ['+61 412345678'],
            fldkiQgUmFFhKcuMIgiPk9bh: ['188'],
            fldrML3Sj0Hl0n9TQpNgerUG: '35',
            fldwQMmEwjHGY5HU7ZI1OBom: ['Cloud Eyelashes'],
          },
        },
        {
          templateId: 'reczcHaUfAZn9gE4nQnUay3c',
          data: {
            attachment: '2025-04-24T04:27:38.804Z',
            single_text: '2025042436',
            fldWf4yy8xEDLVAaUru91rj0: ['recqGv9QhCSxdeNGfG8AqnkT'],
            fldkiQgUmFFhKcuMIgiPk9bh: 50,
            fldrML3Sj0Hl0n9TQpNgerUG: 36,
            fldwQMmEwjHGY5HU7ZI1OBom: ['recaGKby8FbfoMjhE6kWBH2S'],
          },
          values: {
            attachment: '2025-04-24',
            single_text: '2025042436',
            fldCCO1UfRvEUdMjfCp5Uqvh: ['Claire'],
            fldWf4yy8xEDLVAaUru91rj0: ['+49 17612345678'],
            fldkiQgUmFFhKcuMIgiPk9bh: ['50'],
            fldrML3Sj0Hl0n9TQpNgerUG: '36',
            fldwQMmEwjHGY5HU7ZI1OBom: ['Half Paste'],
          },
        },
        {
          templateId: 'recHmaDPypwrkVeA93BroqOv',
          data: {
            attachment: '2025-04-24T04:27:44.951Z',
            single_text: '2025042437',
            fldWf4yy8xEDLVAaUru91rj0: ['recCZdY0OxC0WsnKAAPc1Cln'],
            fldkiQgUmFFhKcuMIgiPk9bh: 40,
            fldrML3Sj0Hl0n9TQpNgerUG: 37,
            fldwQMmEwjHGY5HU7ZI1OBom: ['recJ5LPoxJeO8peWKlxAEz9L'],
          },
          values: {
            attachment: '2025-04-24',
            single_text: '2025042437',
            fldCCO1UfRvEUdMjfCp5Uqvh: ['Alice'],
            fldWf4yy8xEDLVAaUru91rj0: ['+1 (555)123-4567'],
            fldkiQgUmFFhKcuMIgiPk9bh: ['40'],
            fldrML3Sj0Hl0n9TQpNgerUG: '37',
            fldwQMmEwjHGY5HU7ZI1OBom: ['Remove Eyelashes'],
          },
        },
        {
          templateId: 'recHa5lgDbx5mq3k79vpRZyY',
          data: {
            attachment: '2025-04-25T04:27:48.680Z',
            single_text: '2025042538',
            fldWf4yy8xEDLVAaUru91rj0: ['recqGv9QhCSxdeNGfG8AqnkT'],
            fldkiQgUmFFhKcuMIgiPk9bh: 2206,
            fldrML3Sj0Hl0n9TQpNgerUG: 38,
            fldwQMmEwjHGY5HU7ZI1OBom: [
              'recnVWiUQKw6eltiP1I90RHI',
              'rec8PBdWtFQMXUNxWvYKCd02',
              'rectzIcFIW9KEkCgexlET4PK',
            ],
          },
          values: {
            attachment: '2025-04-25',
            single_text: '2025042538',
            fldCCO1UfRvEUdMjfCp5Uqvh: ['Claire'],
            fldWf4yy8xEDLVAaUru91rj0: ['+49 17612345678'],
            fldkiQgUmFFhKcuMIgiPk9bh: ['2206'],
            fldrML3Sj0Hl0n9TQpNgerUG: '38',
            fldwQMmEwjHGY5HU7ZI1OBom: ['Imitation Eyebrows', 'Powder Eyebrows', 'Basic Hand Care'],
          },
        },
        {
          templateId: 'recfeg7S69LzD4MbNHOcCYQK',
          data: {
            attachment: '2025-04-25T04:28:03.346Z',
            single_text: '2025042539',
            fldWf4yy8xEDLVAaUru91rj0: ['rec2h5dSAbEvXZvaNNSFRdPW'],
            fldkiQgUmFFhKcuMIgiPk9bh: 90,
            fldrML3Sj0Hl0n9TQpNgerUG: 39,
            fldwQMmEwjHGY5HU7ZI1OBom: ['rec3cgRT17obP3GQhzzbnZpR'],
          },
          values: {
            attachment: '2025-04-25',
            single_text: '2025042539',
            fldCCO1UfRvEUdMjfCp5Uqvh: ['Bob'],
            fldWf4yy8xEDLVAaUru91rj0: ['+61 412345678'],
            fldkiQgUmFFhKcuMIgiPk9bh: ['90'],
            fldrML3Sj0Hl0n9TQpNgerUG: '39',
            fldwQMmEwjHGY5HU7ZI1OBom: ['Natural Nail Extension'],
          },
        },
        {
          templateId: 'recayFLnJl4xAgLYWuFtjODJ',
          data: {
            attachment: '2025-04-23T04:28:57.513Z',
            single_text: '2025042340',
            fldWf4yy8xEDLVAaUru91rj0: ['rec2h5dSAbEvXZvaNNSFRdPW'],
            fldkiQgUmFFhKcuMIgiPk9bh: 1988,
            fldrML3Sj0Hl0n9TQpNgerUG: 40,
            fldwQMmEwjHGY5HU7ZI1OBom: ['recnVWiUQKw6eltiP1I90RHI'],
          },
          values: {
            attachment: '2025-04-23',
            single_text: '2025042340',
            fldCCO1UfRvEUdMjfCp5Uqvh: ['Bob'],
            fldWf4yy8xEDLVAaUru91rj0: ['+61 412345678'],
            fldkiQgUmFFhKcuMIgiPk9bh: ['1988'],
            fldrML3Sj0Hl0n9TQpNgerUG: '40',
            fldwQMmEwjHGY5HU7ZI1OBom: ['Imitation Eyebrows'],
          },
        },
        {
          templateId: 'recyxBHTjpupC9NAyjMaFk6Q',
          data: {
            attachment: '2025-04-25T04:29:01.609Z',
            single_text: '2025042541',
            fldWf4yy8xEDLVAaUru91rj0: ['recqGv9QhCSxdeNGfG8AqnkT'],
            fldkiQgUmFFhKcuMIgiPk9bh: 2186,
            fldrML3Sj0Hl0n9TQpNgerUG: 41,
            fldwQMmEwjHGY5HU7ZI1OBom: [
              'rec2pSvrmAGvfko9pQyxeSG5',
              'recnVWiUQKw6eltiP1I90RHI',
              'rectzIcFIW9KEkCgexlET4PK',
            ],
          },
          values: {
            attachment: '2025-04-25',
            single_text: '2025042541',
            fldCCO1UfRvEUdMjfCp5Uqvh: ['Claire'],
            fldWf4yy8xEDLVAaUru91rj0: ['+49 17612345678'],
            fldkiQgUmFFhKcuMIgiPk9bh: ['2186'],
            fldrML3Sj0Hl0n9TQpNgerUG: '41',
            fldwQMmEwjHGY5HU7ZI1OBom: ['Baby Straight Eyelashes', 'Imitation Eyebrows', 'Basic Hand Care'],
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
