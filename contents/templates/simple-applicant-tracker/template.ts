import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'simple-applicant-tracker',
  name: 'Simple Applicant Tracker',
  description:
    'An adaptable applicant tracking system suitable for small to medium HR teams. Manage recruitment processes, candidate information, set up internal hiring programs effortlessly, and easily track candidate statuses.',
  cover: '/assets/template/template-cover-simple-applicant-tracker.jpg',
  author: '<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>',
  category: ['operation'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.6',
  resources: [
    {
      resourceType: 'DATABASE',
      templateId: 'dataxrYxi2yTWq64WgKGIj0c',
      name: 'Interviewers',
      databaseType: 'DATUM',
      views: [
        {
          templateId: 'viwQy94xWeTtDN2syeoqGtL3',
          name: 'All interviewers',
          type: 'TABLE',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'single_text',
              hidden: false,
            },
            {
              templateId: 'attachment',
              hidden: false,
            },
            {
              templateId: 'fldwub3ggbioZ0wiLhbirgHJ',
              width: 206,
            },
            {
              templateId: 'fldUa3zSKZibougw5z6z0hrG',
              width: 212,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'single_text',
          privilege: 'TYPE_EDIT',
          name: 'Name',
          primary: true,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'attachment',
          privilege: 'NAME_EDIT',
          name: 'Photo',
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldwub3ggbioZ0wiLhbirgHJ',
          privilege: 'NAME_EDIT',
          name: 'Phone interviews',
          property: {
            foreignDatabaseTemplateId: 'datf3LC6wO0SpC7rT1vv1l0j',
            brotherFieldTemplateId: 'fldcxmjeLkVddB2vAzTY2I9H',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldUa3zSKZibougw5z6z0hrG',
          privilege: 'NAME_EDIT',
          name: 'Onsite interviews',
          property: {
            foreignDatabaseTemplateId: 'datf3LC6wO0SpC7rT1vv1l0j',
            brotherFieldTemplateId: 'fld94PL85tvUoxlfVcUzYV64',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'rectTZjHqrUW3iDkYkMpgztv',
          data: {
            attachment: [
              {
                id: 'tplattWtLKaYfCXTUhwjLDt4Cys',
                name: 'image.png',
                mimeType: 'image/png',
                path: 'template/tplattWtLKaYfCXTUhwjLDt4Cys.png',
                bucket: 'bika-staging',
                size: 28645,
              },
            ],
            single_text: 'Ari Bloom',
            fldUa3zSKZibougw5z6z0hrG: ['recao7MFTi6Q54Qe5qdGfPE9'],
            fldwub3ggbioZ0wiLhbirgHJ: ['rec7c7KjNw5Maq8ug109HIOP'],
          },
          values: {
            fldUa3zSKZibougw5z6z0hrG: ['Jae Monroe'],
            attachment: ['image.png'],
            single_text: 'Ari Bloom',
            fldwub3ggbioZ0wiLhbirgHJ: ['Casey Park'],
          },
        },
        {
          templateId: 'recLNP1UbComKEDicr0XbcIc',
          data: {
            attachment: [
              {
                id: 'tplatt7gGWRpT2TYXI5r0GhFLgI',
                name: 'image.png',
                mimeType: 'image/png',
                path: 'template/tplatt7gGWRpT2TYXI5r0GhFLgI.png',
                bucket: 'bika-staging',
                size: 27774,
              },
            ],
            single_text: 'Andy Park',
            fldUa3zSKZibougw5z6z0hrG: ['recpFBNWFAxG5ocUVT84ofzr', 'rec7c7KjNw5Maq8ug109HIOP'],
            fldwub3ggbioZ0wiLhbirgHJ: [],
          },
          values: {
            fldUa3zSKZibougw5z6z0hrG: ["Rin O'Shea", 'Casey Park'],
            attachment: ['image.png'],
            single_text: 'Andy Park',
          },
        },
        {
          templateId: 'recKWFhy2OOwWXErTPVYNs2F',
          data: {
            attachment: [
              {
                id: 'tplattGTceGTvVFgPa40losZr3j',
                name: 'image.png',
                mimeType: 'image/png',
                path: 'template/tplattGTceGTvVFgPa40losZr3j.png',
                bucket: 'bika-staging',
                size: 39804,
              },
            ],
            single_text: 'Kareena Mukherjee',
            fldUa3zSKZibougw5z6z0hrG: [],
            fldwub3ggbioZ0wiLhbirgHJ: ['recao7MFTi6Q54Qe5qdGfPE9', 'recpFBNWFAxG5ocUVT84ofzr'],
          },
          values: {
            attachment: ['image.png'],
            single_text: 'Kareena Mukherjee',
            fldwub3ggbioZ0wiLhbirgHJ: ['Jae Monroe', "Rin O'Shea"],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datqAVyxUg2RYPjwaOJfTbcV',
      name: 'Positions',
      databaseType: 'DATUM',
      views: [
        {
          templateId: 'viwN1sOIMJNtdpmKYZ35czIh',
          name: 'All positions',
          type: 'TABLE',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'single_text',
              hidden: false,
            },
            {
              templateId: 'fldNrRo97nmQHYPfbuIAw6jm',
              hidden: false,
            },
            {
              templateId: 'fldaftVTYtRSfLoZgRLTjkbA',
              hidden: false,
            },
            {
              templateId: 'fldJ7PtoExeBkyMwLADdvsJF',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'single_text',
          privilege: 'TYPE_EDIT',
          name: 'Name',
          primary: true,
        },
        {
          type: 'LINK',
          templateId: 'fldNrRo97nmQHYPfbuIAw6jm',
          privilege: 'NAME_EDIT',
          name: 'Applying for position',
          property: {
            foreignDatabaseTemplateId: 'datf3LC6wO0SpC7rT1vv1l0j',
            brotherFieldTemplateId: 'fldQov0gyojv7S6h2IWaJBzO',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldaftVTYtRSfLoZgRLTjkbA',
          privilege: 'NAME_EDIT',
          name: 'Description',
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldJ7PtoExeBkyMwLADdvsJF',
          privilege: 'NAME_EDIT',
          name: 'Required Experience',
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'reco5XFhElyUwRqHBv4gRCpW',
          data: {
            single_text: 'Concessions mascot',
            fldJ7PtoExeBkyMwLADdvsJF:
              'Requires a B.A. in Mascot Arts and 2 years of work experience. Experience with the food and beverage industry a plus.',
            fldNrRo97nmQHYPfbuIAw6jm: ['rec7c7KjNw5Maq8ug109HIOP'],
            fldaftVTYtRSfLoZgRLTjkbA: 'Responsible for improving fan engagement with respect to concessions sales.',
          },
          values: {
            single_text: 'Concessions mascot',
            fldJ7PtoExeBkyMwLADdvsJF:
              'Requires a B.A. in Mascot Arts and 2 years of work experience. Experience with the food and beverage industry a plus.',
            fldNrRo97nmQHYPfbuIAw6jm: ['Casey Park'],
            fldaftVTYtRSfLoZgRLTjkbA: 'Responsible for improving fan engagement with respect to concessions sales.',
          },
        },
        {
          templateId: 'recGd9iP7xUap38zEKptnjHK',
          data: {
            single_text: 'Lead mascot',
            fldJ7PtoExeBkyMwLADdvsJF:
              'At least 7 years of work as a mascot at a professional level, or 3 years of work as a mascot at a professional level and an M.A. in Mascot Arts.',
            fldNrRo97nmQHYPfbuIAw6jm: ['recao7MFTi6Q54Qe5qdGfPE9', 'recpFBNWFAxG5ocUVT84ofzr'],
            fldaftVTYtRSfLoZgRLTjkbA: 'Represents entire team. Responsible for training other mascots.',
          },
          values: {
            single_text: 'Lead mascot',
            fldJ7PtoExeBkyMwLADdvsJF:
              'At least 7 years of work as a mascot at a professional level, or 3 years of work as a mascot at a professional level and an M.A. in Mascot Arts.',
            fldNrRo97nmQHYPfbuIAw6jm: ['Jae Monroe', "Rin O'Shea"],
            fldaftVTYtRSfLoZgRLTjkbA: 'Represents entire team. Responsible for training other mascots.',
          },
        },
        {
          templateId: 'recM9UBlKxvmV7DSaeKF57x7',
          data: {
            single_text: 'Supporting mascot',
            fldJ7PtoExeBkyMwLADdvsJF:
              'A minimum of 1 year of experience working as a mascot in any capacity.\n' +
              'Strong interpersonal skills and the ability to work collaboratively with the lead mascot and other team members.\n' +
              'Creativity and adaptability to perform various routines and entertain diverse audiences.\n' +
              'Experience in event management or performing arts is a plus.',
            fldaftVTYtRSfLoZgRLTjkbA:
              'The supporting mascot plays a key role in enhancing the overall fan experience and supporting the lead mascot during events. They assist in engaging with the audience, amplifying team spirit, and contributing to the entertainment value of the performance.',
          },
          values: {
            single_text: 'Supporting mascot',
            fldJ7PtoExeBkyMwLADdvsJF:
              'A minimum of 1 year of experience working as a mascot in any capacity.\n' +
              'Strong interpersonal skills and the ability to work collaboratively with the lead mascot and other team members.\n' +
              'Creativity and adaptability to perform various routines and entertain diverse audiences.\n' +
              'Experience in event management or performing arts is a plus.',
            fldaftVTYtRSfLoZgRLTjkbA:
              'The supporting mascot plays a key role in enhancing the overall fan experience and supporting the lead mascot during events. They assist in engaging with the audience, amplifying team spirit, and contributing to the entertainment value of the performance.',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datf3LC6wO0SpC7rT1vv1l0j',
      name: 'Applicants',
      databaseType: 'DATUM',
      views: [
        {
          templateId: 'viwCccV9KV6CRQ2E7aDNZdMV',
          name: 'All applicants',
          type: 'TABLE',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'single_text',
              hidden: false,
            },
            {
              templateId: 'multi_select',
              hidden: false,
            },
            {
              templateId: 'attachment',
              hidden: false,
            },
            {
              templateId: 'fldoNg0Qa8SYQZAhk4Zv0aqU',
              hidden: false,
            },
            {
              templateId: 'fldSI26XdtPiS1lxgBZbQPol',
              hidden: false,
            },
            {
              templateId: 'fldIf6U4c9louCjmAsBWY7zM',
              hidden: false,
            },
            {
              templateId: 'fld0MyBcGxjLMvWP9eAjZB1K',
              hidden: false,
            },
            {
              templateId: 'fldsWdaMadPU6YrhuhJ3cVBy',
              hidden: false,
            },
            {
              templateId: 'flde1wdkjwt9x5aMiCoQk5st',
              hidden: false,
            },
            {
              templateId: 'fld8eByN2VYIKrNtuVeZwo4e',
              hidden: false,
            },
            {
              templateId: 'fldKoQkWEUSCZGbByqbVCdaL',
              hidden: false,
            },
            {
              templateId: 'fldQov0gyojv7S6h2IWaJBzO',
              hidden: false,
            },
            {
              templateId: 'fldcxmjeLkVddB2vAzTY2I9H',
              hidden: true,
            },
            {
              templateId: 'fld94PL85tvUoxlfVcUzYV64',
              hidden: true,
            },
          ],
        },
        {
          templateId: 'viwAapPooVP2H5Zx27RgxIgb',
          name: 'Resumes & interviews',
          type: 'TABLE',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'single_text',
              hidden: true,
            },
            {
              templateId: 'multi_select',
              hidden: false,
            },
            {
              templateId: 'attachment',
              hidden: false,
            },
            {
              templateId: 'fldoNg0Qa8SYQZAhk4Zv0aqU',
              hidden: true,
            },
            {
              templateId: 'fldSI26XdtPiS1lxgBZbQPol',
              hidden: true,
            },
            {
              templateId: 'fldIf6U4c9louCjmAsBWY7zM',
              hidden: true,
            },
            {
              templateId: 'fld0MyBcGxjLMvWP9eAjZB1K',
              hidden: false,
            },
            {
              templateId: 'fldsWdaMadPU6YrhuhJ3cVBy',
              hidden: true,
            },
            {
              templateId: 'flde1wdkjwt9x5aMiCoQk5st',
              hidden: false,
            },
            {
              templateId: 'fld8eByN2VYIKrNtuVeZwo4e',
              hidden: false,
            },
            {
              templateId: 'fldKoQkWEUSCZGbByqbVCdaL',
              hidden: false,
            },
            {
              templateId: 'fldQov0gyojv7S6h2IWaJBzO',
              hidden: false,
            },
            {
              templateId: 'fldcxmjeLkVddB2vAzTY2I9H',
              hidden: true,
            },
            {
              templateId: 'fld94PL85tvUoxlfVcUzYV64',
              hidden: true,
            },
          ],
        },
        {
          templateId: 'viwZY1trDnl8Tzjewi6GP4I1',
          name: 'Contact info',
          type: 'TABLE',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'single_text',
              hidden: true,
            },
            {
              templateId: 'multi_select',
              hidden: true,
            },
            {
              templateId: 'attachment',
              hidden: true,
            },
            {
              templateId: 'fldoNg0Qa8SYQZAhk4Zv0aqU',
              hidden: false,
            },
            {
              templateId: 'fldSI26XdtPiS1lxgBZbQPol',
              hidden: false,
            },
            {
              templateId: 'fldIf6U4c9louCjmAsBWY7zM',
              hidden: true,
            },
            {
              templateId: 'fld0MyBcGxjLMvWP9eAjZB1K',
              hidden: true,
            },
            {
              templateId: 'fldsWdaMadPU6YrhuhJ3cVBy',
              hidden: true,
            },
            {
              templateId: 'flde1wdkjwt9x5aMiCoQk5st',
              hidden: true,
            },
            {
              templateId: 'fld8eByN2VYIKrNtuVeZwo4e',
              hidden: true,
            },
            {
              templateId: 'fldKoQkWEUSCZGbByqbVCdaL',
              hidden: true,
            },
            {
              templateId: 'fldQov0gyojv7S6h2IWaJBzO',
              hidden: true,
            },
            {
              templateId: 'fldcxmjeLkVddB2vAzTY2I9H',
              hidden: true,
            },
            {
              templateId: 'fld94PL85tvUoxlfVcUzYV64',
              hidden: true,
            },
          ],
        },
        {
          templateId: 'viwfRj6u0EPuN3MIQRFPmvcv',
          name: 'Lead mascot',
          type: 'TABLE',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fldQov0gyojv7S6h2IWaJBzO',
                fieldType: 'LINK',
                clause: {
                  operator: 'Contains',
                  value: ['Lead mascot'],
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'single_text',
              hidden: false,
            },
            {
              templateId: 'multi_select',
              hidden: false,
            },
            {
              templateId: 'attachment',
              hidden: false,
            },
            {
              templateId: 'fldoNg0Qa8SYQZAhk4Zv0aqU',
              hidden: false,
            },
            {
              templateId: 'fldSI26XdtPiS1lxgBZbQPol',
              hidden: false,
            },
            {
              templateId: 'fldIf6U4c9louCjmAsBWY7zM',
              hidden: false,
            },
            {
              templateId: 'fld0MyBcGxjLMvWP9eAjZB1K',
              hidden: false,
            },
            {
              templateId: 'fldsWdaMadPU6YrhuhJ3cVBy',
              hidden: false,
            },
            {
              templateId: 'flde1wdkjwt9x5aMiCoQk5st',
              hidden: false,
            },
            {
              templateId: 'fld8eByN2VYIKrNtuVeZwo4e',
              hidden: false,
            },
            {
              templateId: 'fldKoQkWEUSCZGbByqbVCdaL',
              hidden: false,
            },
            {
              templateId: 'fldQov0gyojv7S6h2IWaJBzO',
              hidden: false,
            },
            {
              templateId: 'fldcxmjeLkVddB2vAzTY2I9H',
              hidden: false,
            },
            {
              templateId: 'fld94PL85tvUoxlfVcUzYV64',
              hidden: false,
            },
          ],
        },
        {
          templateId: 'viwVV5UyqTvsQQPdMyufunta',
          name: 'Concessions mascot',
          type: 'TABLE',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fldQov0gyojv7S6h2IWaJBzO',
                fieldType: 'LINK',
                clause: {
                  operator: 'Contains',
                  value: null,
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'single_text',
              hidden: false,
            },
            {
              templateId: 'multi_select',
              hidden: false,
            },
            {
              templateId: 'attachment',
              hidden: false,
            },
            {
              templateId: 'fldoNg0Qa8SYQZAhk4Zv0aqU',
              hidden: false,
            },
            {
              templateId: 'fldSI26XdtPiS1lxgBZbQPol',
              hidden: false,
            },
            {
              templateId: 'fldIf6U4c9louCjmAsBWY7zM',
              hidden: false,
            },
            {
              templateId: 'fld0MyBcGxjLMvWP9eAjZB1K',
              hidden: false,
            },
            {
              templateId: 'fldsWdaMadPU6YrhuhJ3cVBy',
              hidden: false,
            },
            {
              templateId: 'flde1wdkjwt9x5aMiCoQk5st',
              hidden: false,
            },
            {
              templateId: 'fld8eByN2VYIKrNtuVeZwo4e',
              hidden: false,
            },
            {
              templateId: 'fldKoQkWEUSCZGbByqbVCdaL',
              hidden: false,
            },
            {
              templateId: 'fldQov0gyojv7S6h2IWaJBzO',
              hidden: false,
            },
            {
              templateId: 'fldcxmjeLkVddB2vAzTY2I9H',
              hidden: false,
            },
            {
              templateId: 'fld94PL85tvUoxlfVcUzYV64',
              hidden: false,
            },
          ],
        },
        {
          templateId: 'viwS8Df4mn5Wd9K0dF5Rk1Yk',
          name: "Tomorrow's onsites",
          type: 'TABLE',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fldsWdaMadPU6YrhuhJ3cVBy',
                fieldType: 'DATETIME',
                clause: {
                  operator: 'Is',
                  value: ['Tomorrow'],
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'single_text',
              hidden: false,
            },
            {
              templateId: 'multi_select',
              hidden: false,
            },
            {
              templateId: 'attachment',
              hidden: false,
            },
            {
              templateId: 'fldoNg0Qa8SYQZAhk4Zv0aqU',
              hidden: false,
            },
            {
              templateId: 'fldSI26XdtPiS1lxgBZbQPol',
              hidden: false,
            },
            {
              templateId: 'fldIf6U4c9louCjmAsBWY7zM',
              hidden: false,
            },
            {
              templateId: 'fld0MyBcGxjLMvWP9eAjZB1K',
              hidden: false,
            },
            {
              templateId: 'fldsWdaMadPU6YrhuhJ3cVBy',
              hidden: false,
            },
            {
              templateId: 'flde1wdkjwt9x5aMiCoQk5st',
              hidden: false,
            },
            {
              templateId: 'fld8eByN2VYIKrNtuVeZwo4e',
              hidden: false,
            },
            {
              templateId: 'fldKoQkWEUSCZGbByqbVCdaL',
              hidden: false,
            },
            {
              templateId: 'fldQov0gyojv7S6h2IWaJBzO',
              hidden: false,
            },
            {
              templateId: 'fldcxmjeLkVddB2vAzTY2I9H',
              hidden: false,
            },
            {
              templateId: 'fld94PL85tvUoxlfVcUzYV64',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'single_text',
          privilege: 'TYPE_EDIT',
          name: 'Name',
          primary: true,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'multi_select',
          privilege: 'NAME_EDIT',
          name: 'Stage',
          property: {
            options: [
              {
                id: 'optCPSdXu2F0C3GhGg5vQBgt',
                name: 'No hire',
                color: 'red5',
              },
              {
                id: 'optETkkGq9iOt3gYE9iKnfu3',
                name: 'Interviewing',
                color: 'tangerine5',
              },
              {
                id: 'opttlMJy6c7h5jpFUTMguSqz',
                name: 'Decision needed',
                color: 'green5',
              },
              {
                id: 'optVsEp34simCASn5Ly6YH2J',
                name: 'Hire',
                color: 'deepPurple',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'attachment',
          privilege: 'NAME_EDIT',
          name: 'Attachments',
          primary: false,
        },
        {
          type: 'EMAIL',
          templateId: 'fldoNg0Qa8SYQZAhk4Zv0aqU',
          privilege: 'NAME_EDIT',
          name: 'Email address',
          primary: false,
        },
        {
          type: 'PHONE',
          templateId: 'fldSI26XdtPiS1lxgBZbQPol',
          privilege: 'NAME_EDIT',
          name: 'Phone',
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldIf6U4c9louCjmAsBWY7zM',
          privilege: 'NAME_EDIT',
          name: 'Phone interview',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fld0MyBcGxjLMvWP9eAjZB1K',
          privilege: 'NAME_EDIT',
          name: 'Phone interview score',
          property: {
            options: [
              {
                id: 'opt9VJCq78CuAxIjbCcVG8UM',
                name: '0 – No hire',
                color: 'deepPurple',
              },
              {
                id: 'optcZX2QdyGTlTrRnpS7KRII',
                name: '1 – Probably no hire',
                color: 'indigo5',
              },
              {
                id: 'optkFTVGvyuAhX9pQNkZgDLV',
                name: '2 – Worth consideration',
                color: 'teal5',
              },
              {
                id: 'optMC6QTTfpn5kexB2XNitzO',
                name: '3 – Good candidate',
                color: 'tangerine5',
              },
              {
                id: 'optgwYEpU4RCP4ELVKrCg0WY',
                name: '4 – Please hire this person',
                color: 'red5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldsWdaMadPU6YrhuhJ3cVBy',
          privilege: 'NAME_EDIT',
          name: 'Onsite interview',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'flde1wdkjwt9x5aMiCoQk5st',
          privilege: 'NAME_EDIT',
          name: 'Onsite interview score',
          property: {
            options: [
              {
                id: 'optr1RsTRgPyTgy5xHrHzUTH',
                name: '0 – No hire',
                color: 'deepPurple',
              },
              {
                id: 'opthHHYKBxvw2avhQzCZGIX5',
                name: '1 – Probably no hire',
                color: 'indigo5',
              },
              {
                id: 'optzFRrR4NWrN1K7yQf1d9T5',
                name: '2 – Worth consideration',
                color: 'teal5',
              },
              {
                id: 'opt5QkwHg4T7PRmWK0qt1GtF',
                name: '3 – Good candidate',
                color: 'orange5',
              },
              {
                id: 'optCYd13r1JURHjQm5Ixazcf',
                name: '4 – Please hire this person',
                color: 'red5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fld8eByN2VYIKrNtuVeZwo4e',
          privilege: 'NAME_EDIT',
          name: 'Phone interview notes',
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldKoQkWEUSCZGbByqbVCdaL',
          privilege: 'NAME_EDIT',
          name: 'Onsite interview notes',
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldQov0gyojv7S6h2IWaJBzO',
          privilege: 'NAME_EDIT',
          name: 'Applying for',
          property: {
            foreignDatabaseTemplateId: 'datqAVyxUg2RYPjwaOJfTbcV',
            brotherFieldTemplateId: 'fldNrRo97nmQHYPfbuIAw6jm',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldcxmjeLkVddB2vAzTY2I9H',
          privilege: 'NAME_EDIT',
          name: 'Phone interviewer',
          property: {
            foreignDatabaseTemplateId: 'dataxrYxi2yTWq64WgKGIj0c',
            brotherFieldTemplateId: 'fldwub3ggbioZ0wiLhbirgHJ',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fld94PL85tvUoxlfVcUzYV64',
          privilege: 'NAME_EDIT',
          name: 'Onsite interviewer',
          property: {
            foreignDatabaseTemplateId: 'dataxrYxi2yTWq64WgKGIj0c',
            brotherFieldTemplateId: 'fldUa3zSKZibougw5z6z0hrG',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recao7MFTi6Q54Qe5qdGfPE9',
          data: {
            attachment: [
              {
                id: 'tplatteSNsZYSsZada5Qpy7qKIj',
                name: 'chippypotatoresume.docx',
                mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                path: 'template/tplatteSNsZYSsZada5Qpy7qKIj.docx',
                bucket: 'bika-staging',
                size: 127681,
              },
            ],
            single_text: 'Jae Monroe',
            multi_select: ['opttlMJy6c7h5jpFUTMguSqz'],
            fld0MyBcGxjLMvWP9eAjZB1K: ['optMC6QTTfpn5kexB2XNitzO'],
            fld8eByN2VYIKrNtuVeZwo4e: 'Move to in-person.',
            fld94PL85tvUoxlfVcUzYV64: ['rectTZjHqrUW3iDkYkMpgztv'],
            fldIf6U4c9louCjmAsBWY7zM: '2024-10-28T16:00:00.000Z',
            fldKoQkWEUSCZGbByqbVCdaL:
              "Jae was highly qualified, but seemed shifty. Could be someone that is very smart, but difficult to deal with. Not sure that he's a team player, which is a problem if you're going to be representing a team.",
            fldQov0gyojv7S6h2IWaJBzO: ['recGd9iP7xUap38zEKptnjHK'],
            fldSI26XdtPiS1lxgBZbQPol: '(333) 333-3333',
            fldcxmjeLkVddB2vAzTY2I9H: ['recKWFhy2OOwWXErTPVYNs2F'],
            flde1wdkjwt9x5aMiCoQk5st: ['optzFRrR4NWrN1K7yQf1d9T5'],
            fldoNg0Qa8SYQZAhk4Zv0aqU: '<EMAIL>',
            fldsWdaMadPU6YrhuhJ3cVBy: '2024-11-02T16:00:00.000Z',
          },
          values: {
            fld0MyBcGxjLMvWP9eAjZB1K: ['3 – Good candidate'],
            multi_select: ['Decision needed'],
            fld8eByN2VYIKrNtuVeZwo4e: 'Move to in-person.',
            fld94PL85tvUoxlfVcUzYV64: ['Ari Bloom'],
            attachment: ['chippypotatoresume.docx'],
            fldIf6U4c9louCjmAsBWY7zM: '2024-10-28',
            fldKoQkWEUSCZGbByqbVCdaL:
              "Jae was highly qualified, but seemed shifty. Could be someone that is very smart, but difficult to deal with. Not sure that he's a team player, which is a problem if you're going to be representing a team.",
            fldQov0gyojv7S6h2IWaJBzO: ['Lead mascot'],
            fldSI26XdtPiS1lxgBZbQPol: '(333) 333-3333',
            single_text: 'Jae Monroe',
            fldcxmjeLkVddB2vAzTY2I9H: ['Kareena Mukherjee'],
            flde1wdkjwt9x5aMiCoQk5st: ['2 – Worth consideration'],
            fldoNg0Qa8SYQZAhk4Zv0aqU: '<EMAIL>',
            fldsWdaMadPU6YrhuhJ3cVBy: '2024-11-02',
          },
        },
        {
          templateId: 'rec7c7KjNw5Maq8ug109HIOP',
          data: {
            attachment: [
              {
                id: 'tplatteSNsZYSsZada5Qpy7qKIj',
                name: 'chippypotatoresume.docx',
                mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                path: 'template/tplatteSNsZYSsZada5Qpy7qKIj.docx',
                bucket: 'bika-staging',
                size: 127681,
              },
            ],
            single_text: 'Casey Park',
            multi_select: ['opttlMJy6c7h5jpFUTMguSqz'],
            fld0MyBcGxjLMvWP9eAjZB1K: ['optkFTVGvyuAhX9pQNkZgDLV'],
            fld8eByN2VYIKrNtuVeZwo4e: 'Questionable, but tentatively move to on-site interview.',
            fld94PL85tvUoxlfVcUzYV64: ['recLNP1UbComKEDicr0XbcIc'],
            fldIf6U4c9louCjmAsBWY7zM: '2024-10-24T16:00:00.000Z',
            fldKoQkWEUSCZGbByqbVCdaL:
              "Seems like a really hard worker, and has a great attitude. Very observant: He's got eyes everywhere. But I am concerned that he won't be able to think outside the box.",
            fldQov0gyojv7S6h2IWaJBzO: ['reco5XFhElyUwRqHBv4gRCpW'],
            fldSI26XdtPiS1lxgBZbQPol: '(222) 222-2222',
            fldcxmjeLkVddB2vAzTY2I9H: ['rectTZjHqrUW3iDkYkMpgztv'],
            flde1wdkjwt9x5aMiCoQk5st: ['optzFRrR4NWrN1K7yQf1d9T5'],
            fldoNg0Qa8SYQZAhk4Zv0aqU: '<EMAIL>',
            fldsWdaMadPU6YrhuhJ3cVBy: '2024-10-31T16:00:00.000Z',
          },
          values: {
            fld0MyBcGxjLMvWP9eAjZB1K: ['2 – Worth consideration'],
            multi_select: ['Decision needed'],
            fld8eByN2VYIKrNtuVeZwo4e: 'Questionable, but tentatively move to on-site interview.',
            fld94PL85tvUoxlfVcUzYV64: ['Andy Park'],
            attachment: ['chippypotatoresume.docx'],
            fldIf6U4c9louCjmAsBWY7zM: '2024-10-24',
            fldKoQkWEUSCZGbByqbVCdaL:
              "Seems like a really hard worker, and has a great attitude. Very observant: He's got eyes everywhere. But I am concerned that he won't be able to think outside the box.",
            fldQov0gyojv7S6h2IWaJBzO: ['Concessions mascot'],
            fldSI26XdtPiS1lxgBZbQPol: '(222) 222-2222',
            single_text: 'Casey Park',
            fldcxmjeLkVddB2vAzTY2I9H: ['Ari Bloom'],
            flde1wdkjwt9x5aMiCoQk5st: ['2 – Worth consideration'],
            fldoNg0Qa8SYQZAhk4Zv0aqU: '<EMAIL>',
            fldsWdaMadPU6YrhuhJ3cVBy: '2024-10-31',
          },
        },
        {
          templateId: 'recpFBNWFAxG5ocUVT84ofzr',
          data: {
            attachment: [
              {
                id: 'tplattaC3mKUioUV4vZWnqWaMxk',
                name: 'Resume (O. Huxley).docx',
                mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                path: 'template/tplattaC3mKUioUV4vZWnqWaMxk.docx',
                bucket: 'bika-staging',
                size: 16443,
              },
            ],
            single_text: "Rin O'Shea",
            multi_select: ['optETkkGq9iOt3gYE9iKnfu3'],
            fld0MyBcGxjLMvWP9eAjZB1K: ['optMC6QTTfpn5kexB2XNitzO'],
            fld8eByN2VYIKrNtuVeZwo4e: 'Good analytical skills, very articulate. Proceed to in-person interview.',
            fld94PL85tvUoxlfVcUzYV64: ['recLNP1UbComKEDicr0XbcIc'],
            fldIf6U4c9louCjmAsBWY7zM: '2024-10-22T16:00:00.000Z',
            fldKoQkWEUSCZGbByqbVCdaL:
              "Rin was impressive overall, and he has a lot of experience in the corporate world. But if he were to join with a sports team, he'd still have a lot to learn about the industry.",
            fldQov0gyojv7S6h2IWaJBzO: ['recGd9iP7xUap38zEKptnjHK'],
            fldSI26XdtPiS1lxgBZbQPol: '(111) 111-1111',
            fldcxmjeLkVddB2vAzTY2I9H: ['recKWFhy2OOwWXErTPVYNs2F'],
            flde1wdkjwt9x5aMiCoQk5st: ['opt5QkwHg4T7PRmWK0qt1GtF'],
            fldoNg0Qa8SYQZAhk4Zv0aqU: '<EMAIL>',
            fldsWdaMadPU6YrhuhJ3cVBy: '2024-10-26T16:00:00.000Z',
          },
          values: {
            fld0MyBcGxjLMvWP9eAjZB1K: ['3 – Good candidate'],
            multi_select: ['Interviewing'],
            fld8eByN2VYIKrNtuVeZwo4e: 'Good analytical skills, very articulate. Proceed to in-person interview.',
            fld94PL85tvUoxlfVcUzYV64: ['Andy Park'],
            attachment: ['Resume (O. Huxley).docx'],
            fldIf6U4c9louCjmAsBWY7zM: '2024-10-22',
            fldKoQkWEUSCZGbByqbVCdaL:
              "Rin was impressive overall, and he has a lot of experience in the corporate world. But if he were to join with a sports team, he'd still have a lot to learn about the industry.",
            fldQov0gyojv7S6h2IWaJBzO: ['Lead mascot'],
            fldSI26XdtPiS1lxgBZbQPol: '(111) 111-1111',
            single_text: "Rin O'Shea",
            fldcxmjeLkVddB2vAzTY2I9H: ['Kareena Mukherjee'],
            flde1wdkjwt9x5aMiCoQk5st: ['3 – Good candidate'],
            fldoNg0Qa8SYQZAhk4Zv0aqU: '<EMAIL>',
            fldsWdaMadPU6YrhuhJ3cVBy: '2024-10-26',
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
