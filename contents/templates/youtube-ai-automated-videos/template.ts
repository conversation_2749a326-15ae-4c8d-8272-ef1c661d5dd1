import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  schemaVersion: 'v1',
  author: '<PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>',

  // 如果你正在制作“Comming Soon”类型的模板，则需要下方属性配置。如果是正式的模板，请移除下方这一行
  visibility: 'WAITING_LIST',

  // 根据你的模板作用，起一个系统唯一的templateId，模板文件夹的名称与templateId 必须相同！
  templateId: 'youtube-ai-automated-videos',

  // 模板的名称，会出现在模板中心，安装后也会显示该名称
  name: {
    en: 'YouTube Publishing Process Automation',
    'zh-CN': 'YouTube发布流程自动化',
    'zh-TW': 'YouTube發佈流程自動化',
    ja: 'YouTube公開プロセスの自動化',
  },

  // 模板的封面图
  cover: '/assets/template/template-cover-youtube-ai-automated-videos.png',

  // 模板的简短描述，会出现在模板中心和模板文件夹详情页
  description: {
    en: 'Streamline your content publishing process by auto-publishing videos to YouTube and storing their URLs for easy access and sharing.',
    'zh-CN': '通过自动发布视频到YouTube并存储其URL来简化您的内容分发流程，以便轻松访问和分享。',
    'zh-TW': '通過自動發佈視頻到YouTube並存儲其URL來簡化您的內容分發流程，以便輕鬆訪問和分享。',
    ja: '動画を YouTube に自動公開し、その URL を保存することでコンテンツ配信プロセスを簡素化し、簡単にアクセスおよび共有できるようにします。',
  },

  // 模板的SEO关键词
  keywords:
    'YouTube publishing, Video automation, Content management, Workflow optimization, Social media distribution, Video marketing',

  // 用户画像关键词
  personas: {
    'zh-CN': 'YouTube创作者, 社交媒体经理, 内容营销人员, 数字策略师',
    'zh-TW': 'YouTube創作者, 社交媒體經理, 內容營銷人員, 數字策略師',
    ja: 'YouTubeクリエーター, ソーシャルメディアマネージャー, コンテンツマーケター, デジタルストラテジスト',
    en: 'YouTube Creators, Social Media Managers, Content Marketers, Digital Strategists',
  },

  // Use Cases关键词
  useCases: {
    en: 'Automate video publishing, Schedule YouTube posts, Manage video content, Simplify content sharing, Optimize publishing workflow, Track video URLs, Enhance content visibility, Improve audience reach, Streamline social media strategy, Reduce manual tasks, Increase content efficiency, Integrate with content calendar, Boost video engagement, Schedule regular uploads, Maintain consistent posting, Monitor publishing metrics, Improve content organization, Enhance video strategy, Automate content distribution, Simplify video sharing, Track content performance, Utilize publishing tools, Foster audience engagement, Optimize social media posts',
    'zh-CN':
      '自动发布视频, 安排YouTube发布, 管理视频内容, 简化内容分享, 优化发布流程, 跟踪视频URL, 提高内容能见, 提升观众覆盖, 简化社交策略, 减少手工操作, 增加内容效率, 整合内容日历, 提高视频互动, 安排定期上传, 保持一致发布, 监控发布指标, 改善内容组织, 提升视频策略, 自动内容分发, 简化视频分享, 跟踪内容表现, 利用发布工具, 促进观众互动, 优化社交发布',
    'zh-TW':
      '自動發佈視頻, 安排YouTube發佈, 管理視頻內容, 簡化內容分享, 優化發佈流程, 跟踪視頻URL, 提高內容能見, 提升觀眾覆蓋, 簡化社交策略, 減少手工操作, 增加內容效率, 整合內容日曆, 提高視頻互動, 安排定期上傳, 保持一致發佈, 監控發佈指標, 改善內容組織, 提升視頻策略, 自動內容分發, 簡化視頻分享, 跟踪內容表現, 利用發佈工具, 促進觀眾互動, 優化社交發佈',
    ja: 'ビデオ公開の自動化, YouTube投稿のスケジュール, ビデオコンテンツの管理, コンテンツ共有の簡素化, 公開ワークフローの最適化, ビデオURLの追跡, コンテンツの可視性向上, 視聴者リーチの改善, ソーシャルメディア戦略の合理化, 手動作業の削減, コンテンツ効率の向上, コンテンツカレンダーとの統合, ビデオエンゲージメントの向上, 定期アップロードのスケジュール, 一貫した投稿の維持, 公開メトリクスの監視, コンテンツの整理の改善, ビデオ戦略の強化, コンテンツ配信の自動化, ビデオ共有の簡素化, コンテンツパフォーマンスの追跡, 公開ツールの利用, 視聴者エンゲージメントの促進, ソーシャルメディア投稿の最適化',
  },

  // 模板的当前迭代版本
  version: '0.1.9',

  // 模板的分类，不能随意填写，请从 packages/types/src/template/template.ts 声明的schema 中选择合适的分类。模板支持同时存在于多个分类。
  category: ['marketing'],

  // 模板内置的新手任务。"Comming Soon" 类型的模板，可忽略不填
  initMissions: [],

  // 模板包含的资源节点，例如 automation、database。"Comming Soon" 类型的模板，可忽略不填，但建议填写，以令模板详情页的内容更加丰富
  resources: [
    {
      resourceType: 'AUTOMATION',
      name: {
        'zh-CN': '视频自动发布',
        'zh-TW': '自動發布影片',
        ja: '動画の自動投稿',
        en: 'Automatic video publishing',
      },
      templateId: 'to_be_updated',
      description: {
        'zh-CN': '在特定时间发布YouTube视频',
        'zh-TW': '在特定時間發佈YouTube影片',
        ja: '特定の時間にYouTubeの動画を投稿する',
        en: 'Post a YouTube video at a specific time',
      },
      status: 'INACTIVE',
      triggers: [
        {
          triggerType: 'DUMMY_TRIGGER',
          description: {
            'zh-CN': '每天上午 10:00 触发',
            'zh-TW': '每天上午10:00觸發',
            ja: '毎日午前10時にトリガー',
            en: 'Trigger at 10:00 AM every day',
          },
        },
      ],
      actions: [
        {
          actionType: 'DUMMY_ACTION',
          description: {
            en: 'Retrieve video content from the data table',
            'zh-CN': '从数据表中获取视频内容',
            'zh-TW': '從資料表中獲取影片內容',
            ja: 'データテーブルから動画コンテンツを取得',
          },
        },
        {
          actionType: 'DUMMY_ACTION',
          description: {
            en: 'Publish the YouTube video using authorized account',
            'zh-CN': '使用授权账号发布YouTube视频',
            'zh-TW': '使用授權帳號發佈YouTube影片',
            ja: '認証されたアカウントを使用してYouTubeの動画を投稿',
          },
        },
        {
          actionType: 'DUMMY_ACTION',
          description: {
            en: 'Store the URL of the newly published video in the data table',
            'zh-CN': '存储新发布视频URL到数据表中',
            'zh-TW': '將新發佈影片的URL存儲到資料表中',
            ja: '新しく投稿された動画のURLをデータテーブルに保存',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'to_be_updated',
      databaseType: 'DATUM',
      name: {
        en: 'YouTube video content',
        'zh-CN': 'YouTube视频内容',
        'zh-TW': 'YouTube影片內容',
        ja: 'YouTubeの動画コンテンツ',
      },
      views: [
        {
          templateId: 'all',
          name: {
            en: '',
            'zh-CN': 'Coming Soon',
            'zh-TW': '',
            ja: '',
          },
          type: 'TABLE',
        },
      ],
      fields: [],
    },
  ],
};

export default template;
