import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'simple-powerful-crm',
  name: 'A Simple & Powerful CRM',
  description:
    "A Simple & Powerful CRM offers essential resources for managing customer relationships effectively. Whether you're starting out or optimizing existing processes, this CRM toolkit provides valuable insights and support to enhance your business operations.",
  cover: '/assets/template/simple-powerful-crm/simple-powerful-crm.png',
  author: '<PERSON> <<EMAIL>>',
  category: ['sales'],
  keywords: 'CRM, Sales, Customer Relationship Management',
  useCases: 'CRM, Sales, Customer Relationship Management, Customer Service, Customer Support, Sales Management',
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.3',
  resources: [
    {
      resourceType: 'DASHBOARD',
      templateId: 'dsbepoMXhXMBB3ygGjhbRlWE',
      name: 'Auto Sales Data Dashboard',
      widgets: [
        {
          templateId: 'wdtLU1a8Ffc96XFKV00R2tmh',
          type: 'CHART',
          name: 'Sales Stage Overview',
          datasource: {
            databaseTemplateId: 'datp2gT0iVDTuTiChytVJ2yp',
            viewTemplateId: 'viwWKdyQ8s583p6MNqDohab6',
            type: 'DATABASE',
            chartType: 'bar',
            metricsType: 'COUNT_RECORDS',
            dimensionTemplateId: 'fldUUY51jfea6FgxS2PiQmr3',
          },
        },
      ],
    },
    {
      resourceType: 'FORM',
      templateId: 'fomMKGWS7Mgc5x1ZL8BhPWQm',
      name: 'Help Us Understand Your Needs',
      description:
        'Welcome to Bika.ai! To serve you better, please share your details with us. Our experts are ready to give you a comprehensive 15-30 minutes walkthrough to get you started quickly.',
      formType: 'DATABASE',
      databaseTemplateId: 'datp2gT0iVDTuTiChytVJ2yp',
      metadata: {
        type: 'VIEW',
        viewTemplateId: 'viwdVQDLZuWZYVic1yK0i3Zf',
      },
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datp2gT0iVDTuTiChytVJ2yp',
      name: 'CRM Database',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwWKdyQ8s583p6MNqDohab6',
          name: 'Database',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldOMeMAV1BI8pgaLSLjW1p2',
              hidden: false,
            },
            {
              templateId: 'fldZz7bX0bvVp3W1QWMRnirY',
              hidden: false,
            },
            {
              templateId: 'fldcTfXqX4XPqVxghGzIo2iO',
              hidden: false,
            },
            {
              templateId: 'fldDRBDOV33arln6h0LuCoh1',
              hidden: false,
            },
            {
              templateId: 'fldfJdoxXOZDrg3m1FlBwbnt',
              hidden: false,
            },
            {
              templateId: 'flddcYetlnOkHJHJbjbzQGtM',
              hidden: false,
            },
            {
              templateId: 'fld4qRWgKrdZmFC5o5sFIWqJ',
              hidden: false,
            },
            {
              templateId: 'fldE9yVL1W6YXvoWs85zkTFF',
              hidden: false,
            },
            {
              templateId: 'fldpagn3SLRDIGPBSBPRraaK',
              hidden: false,
            },
            {
              templateId: 'fldUUY51jfea6FgxS2PiQmr3',
              hidden: false,
            },
            {
              templateId: 'fldz8KepWk3uH2WVRuLQftwh',
              hidden: false,
            },
            {
              templateId: 'fldezsGzfJk9cw7ozO1cNTut',
              hidden: false,
            },
            {
              templateId: 'fldkiliO0fI1QQPn9nIv2wRg',
              hidden: false,
            },
            {
              templateId: 'fld8xNQLfTXY9UGcMbRQR46B',
              hidden: false,
            },
          ],
          groups: [
            {
              fieldTemplateId: 'fldUUY51jfea6FgxS2PiQmr3',
              asc: true,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'viwdVQDLZuWZYVic1yK0i3Zf',
          name: 'Form',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldOMeMAV1BI8pgaLSLjW1p2',
              hidden: false,
            },
            {
              templateId: 'fldZz7bX0bvVp3W1QWMRnirY',
              hidden: false,
            },
            {
              templateId: 'fldcTfXqX4XPqVxghGzIo2iO',
              hidden: false,
            },
            {
              templateId: 'fldDRBDOV33arln6h0LuCoh1',
              hidden: false,
            },
            {
              templateId: 'fldfJdoxXOZDrg3m1FlBwbnt',
              hidden: false,
            },
            {
              templateId: 'flddcYetlnOkHJHJbjbzQGtM',
              hidden: false,
            },
            {
              templateId: 'fld4qRWgKrdZmFC5o5sFIWqJ',
              hidden: false,
            },
            {
              templateId: 'fldE9yVL1W6YXvoWs85zkTFF',
              hidden: true,
            },
            {
              templateId: 'fldpagn3SLRDIGPBSBPRraaK',
              hidden: true,
            },
            {
              templateId: 'fldUUY51jfea6FgxS2PiQmr3',
              hidden: true,
            },
            {
              templateId: 'fldz8KepWk3uH2WVRuLQftwh',
              hidden: true,
            },
            {
              templateId: 'fldezsGzfJk9cw7ozO1cNTut',
              hidden: true,
            },
            {
              templateId: 'fldkiliO0fI1QQPn9nIv2wRg',
              hidden: true,
            },
            {
              templateId: 'fld8xNQLfTXY9UGcMbRQR46B',
              hidden: true,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'fldOMeMAV1BI8pgaLSLjW1p2',
          privilege: 'TYPE_EDIT',
          name: 'Full name',
          primary: true,
        },
        {
          type: 'EMAIL',
          templateId: 'fldZz7bX0bvVp3W1QWMRnirY',
          privilege: 'FULL_EDIT',
          name: 'Work email',
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldcTfXqX4XPqVxghGzIo2iO',
          privilege: 'FULL_EDIT',
          name: 'Job title or department',
          primary: false,
        },
        {
          type: 'PHONE',
          templateId: 'fldDRBDOV33arln6h0LuCoh1',
          privilege: 'FULL_EDIT',
          name: 'Phone number',
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldfJdoxXOZDrg3m1FlBwbnt',
          privilege: 'FULL_EDIT',
          name: 'Company name',
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'flddcYetlnOkHJHJbjbzQGtM',
          privilege: 'FULL_EDIT',
          name: 'Company size',
          property: {
            options: [
              {
                id: 'opteHrUw2LaJPCmnHiRhgb3B',
                name: '1-19',
                color: 'deepPurple5',
              },
              {
                id: 'opt358q4IKMTuvtozLrEK15h',
                name: '20-49',
                color: 'indigo5',
              },
              {
                id: 'opt4w7C9xWKrZDmTiXBa1f9a',
                name: '100-250',
                color: 'blue5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fld4qRWgKrdZmFC5o5sFIWqJ',
          privilege: 'FULL_EDIT',
          name: 'How can our team help you?',
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldE9yVL1W6YXvoWs85zkTFF',
          privilege: 'FULL_EDIT',
          name: 'Prefill',
          primary: false,
        },
        {
          type: 'MEMBER',
          templateId: 'fldpagn3SLRDIGPBSBPRraaK',
          privilege: 'FULL_EDIT',
          name: 'Sales',
          property: {},
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldUUY51jfea6FgxS2PiQmr3',
          privilege: 'FULL_EDIT',
          name: 'Stage',
          property: {
            options: [
              {
                id: 'optTdMHl3aOOlL5ZkLQlgAH0',
                name: 'Initiate Contact',
                color: 'deepPurple5',
              },
              {
                id: 'optDzbQEP2NHdnYJBq7lzWLs',
                name: 'Identify Needs',
                color: 'indigo5',
              },
              {
                id: 'optq3tXSOYZWBgV9HycP4Fnl',
                name: 'Present Offer',
                color: 'blue5',
              },
              {
                id: 'optgJNhNM1wE5CHpoUW4TdzA',
                name: 'Close Deals',
                color: 'teal5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'CREATED_TIME',
          templateId: 'fldz8KepWk3uH2WVRuLQftwh',
          privilege: 'FULL_EDIT',
          name: 'Created time',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'MODIFIED_TIME',
          templateId: 'fldezsGzfJk9cw7ozO1cNTut',
          privilege: 'FULL_EDIT',
          name: 'Last edited time',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'fldkiliO0fI1QQPn9nIv2wRg',
          privilege: 'FULL_EDIT',
          name: 'Time since last stage update?',
          property: {
            expressionTemplate: "DATETIME_DIFF(NOW(),{fldezsGzfJk9cw7ozO1cNTut},'days')",
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fld8xNQLfTXY9UGcMbRQR46B',
          privilege: 'FULL_EDIT',
          name: 'Visit Records',
          property: {
            foreignDatabaseTemplateId: 'datk0owZwLVCDYWFYJxBiJ3g',
            brotherFieldTemplateId: 'fld6PbaihaAv43kQh4dkejpg',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'reck9eRcU6GsN2qOIZ6LyTTn',
          data: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fld8xNQLfTXY9UGcMbRQR46B: ['recFCm2Tc2RTwb7HqiRbuXSq', 'rec9mmA3OhhbpOBnOlrAMwWk'],
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'homepage',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Abby Simmons',
            fldUUY51jfea6FgxS2PiQmr3: ['optTdMHl3aOOlL5ZkLQlgAH0'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'CTO',
            flddcYetlnOkHJHJbjbzQGtM: ['opteHrUw2LaJPCmnHiRhgb3B'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
          values: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fld8xNQLfTXY9UGcMbRQR46B: ['2023/09/07-Abby Simmons', '2023/11/08-Abby Simmons'],
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'homepage',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Abby Simmons',
            fldUUY51jfea6FgxS2PiQmr3: ['Initiate Contact'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'CTO',
            flddcYetlnOkHJHJbjbzQGtM: ['1-19'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
        },
        {
          templateId: 'reco8Q5ryDYhJ4rimC8TapIl',
          data: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fld8xNQLfTXY9UGcMbRQR46B: ['recm7cfTwJ693NeWP8Uj5j16', 'recLj3B1Uv38vrnbb2arNxrN'],
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'pricing_page',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Benjamin Peterson',
            fldUUY51jfea6FgxS2PiQmr3: ['optDzbQEP2NHdnYJBq7lzWLs'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'CEO',
            flddcYetlnOkHJHJbjbzQGtM: ['opt358q4IKMTuvtozLrEK15h'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
          values: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fld8xNQLfTXY9UGcMbRQR46B: ['2023/09/07-Benjamin Peterson', '2023/11/10-Benjamin Peterson'],
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'pricing_page',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Benjamin Peterson',
            fldUUY51jfea6FgxS2PiQmr3: ['Identify Needs'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'CEO',
            flddcYetlnOkHJHJbjbzQGtM: ['20-49'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
        },
        {
          templateId: 'recq5FaMwtgLbrZ5MoYfzUaI',
          data: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fld8xNQLfTXY9UGcMbRQR46B: ['recviwcfQFlnDZ5wGvqHJvfr'],
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'inside_product',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Christine Davis',
            fldUUY51jfea6FgxS2PiQmr3: ['optq3tXSOYZWBgV9HycP4Fnl'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'PM',
            flddcYetlnOkHJHJbjbzQGtM: ['opteHrUw2LaJPCmnHiRhgb3B'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
          values: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fld8xNQLfTXY9UGcMbRQR46B: ['2023/11/15-Christine Davis'],
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'inside_product',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Christine Davis',
            fldUUY51jfea6FgxS2PiQmr3: ['Present Offer'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'PM',
            flddcYetlnOkHJHJbjbzQGtM: ['1-19'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
        },
        {
          templateId: 'recMSPa508XCeiqYtGB7NELH',
          data: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'homepage',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Daniel Murphy',
            fldUUY51jfea6FgxS2PiQmr3: ['optgJNhNM1wE5CHpoUW4TdzA'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'Head of product development department',
            flddcYetlnOkHJHJbjbzQGtM: ['opt4w7C9xWKrZDmTiXBa1f9a'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
          values: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'homepage',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Daniel Murphy',
            fldUUY51jfea6FgxS2PiQmr3: ['Close Deals'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'Head of product development department',
            flddcYetlnOkHJHJbjbzQGtM: ['100-250'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
        },
        {
          templateId: 'recq3jGh7jM4KJ4FQBN9pJiP',
          data: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fld8xNQLfTXY9UGcMbRQR46B: ['rec0xGlbUcTH0XP5SKsNadei'],
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'pricing_page',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Edward Thompson',
            fldUUY51jfea6FgxS2PiQmr3: ['optTdMHl3aOOlL5ZkLQlgAH0'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'Administration Specialist',
            flddcYetlnOkHJHJbjbzQGtM: ['opt358q4IKMTuvtozLrEK15h'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
          values: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fld8xNQLfTXY9UGcMbRQR46B: ['2023/10/16-Edward Thompson'],
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'pricing_page',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Edward Thompson',
            fldUUY51jfea6FgxS2PiQmr3: ['Initiate Contact'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'Administration Specialist',
            flddcYetlnOkHJHJbjbzQGtM: ['20-49'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
        },
        {
          templateId: 'recToIn6vq6oNO2z713FWFiB',
          data: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'inside_product',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Fiona White',
            fldUUY51jfea6FgxS2PiQmr3: ['optDzbQEP2NHdnYJBq7lzWLs'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'Administrator',
            flddcYetlnOkHJHJbjbzQGtM: ['opt4w7C9xWKrZDmTiXBa1f9a'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
          values: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'inside_product',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Fiona White',
            fldUUY51jfea6FgxS2PiQmr3: ['Identify Needs'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'Administrator',
            flddcYetlnOkHJHJbjbzQGtM: ['100-250'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
        },
        {
          templateId: 'recm8kelPnMMhZ59pHc6wsn0',
          data: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fld8xNQLfTXY9UGcMbRQR46B: ['recQp465sGy7CNbLEH4bFpsa'],
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'homepage',
            fldOMeMAV1BI8pgaLSLjW1p2: 'George Harris',
            fldUUY51jfea6FgxS2PiQmr3: ['optq3tXSOYZWBgV9HycP4Fnl'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'CEO',
            flddcYetlnOkHJHJbjbzQGtM: ['opteHrUw2LaJPCmnHiRhgb3B'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
          values: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fld8xNQLfTXY9UGcMbRQR46B: ['2023/09/07-George Harris'],
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'homepage',
            fldOMeMAV1BI8pgaLSLjW1p2: 'George Harris',
            fldUUY51jfea6FgxS2PiQmr3: ['Present Offer'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'CEO',
            flddcYetlnOkHJHJbjbzQGtM: ['1-19'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
        },
        {
          templateId: 'rec3xnF9CoXpCa0DBCwLf245',
          data: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fld8xNQLfTXY9UGcMbRQR46B: ['reccbQW5R40NdUM0S5iGz4kV'],
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'pricing_page',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Helen Jackson',
            fldUUY51jfea6FgxS2PiQmr3: ['optgJNhNM1wE5CHpoUW4TdzA'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'Founder',
            flddcYetlnOkHJHJbjbzQGtM: ['opt358q4IKMTuvtozLrEK15h'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
          values: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fld8xNQLfTXY9UGcMbRQR46B: ['2023/09/07-Helen Jackson'],
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'pricing_page',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Helen Jackson',
            fldUUY51jfea6FgxS2PiQmr3: ['Close Deals'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'Founder',
            flddcYetlnOkHJHJbjbzQGtM: ['20-49'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
        },
        {
          templateId: 'rec8TVoDoVwxo26Z6tuSU1nA',
          data: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'inside_product',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Ian Miller',
            fldUUY51jfea6FgxS2PiQmr3: ['optgJNhNM1wE5CHpoUW4TdzA'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'Chief Financial Engineer',
            flddcYetlnOkHJHJbjbzQGtM: ['opteHrUw2LaJPCmnHiRhgb3B'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
          values: {
            fld4qRWgKrdZmFC5o5sFIWqJ:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldDRBDOV33arln6h0LuCoh1: '1 555-123-4567',
            fldE9yVL1W6YXvoWs85zkTFF: 'inside_product',
            fldOMeMAV1BI8pgaLSLjW1p2: 'Ian Miller',
            fldUUY51jfea6FgxS2PiQmr3: ['Close Deals'],
            fldZz7bX0bvVp3W1QWMRnirY: '<EMAIL>',
            fldcTfXqX4XPqVxghGzIo2iO: 'Chief Financial Engineer',
            flddcYetlnOkHJHJbjbzQGtM: ['1-19'],
            fldfJdoxXOZDrg3m1FlBwbnt: 'Example Ltd.',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datk0owZwLVCDYWFYJxBiJ3g',
      name: 'Visit Records',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwWesDpBBdu64F1Xu3RCD29',
          name: 'Grid view',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [
            {
              fieldTemplateId: 'fldrwDsbvFZJ8yMPpD1qSXkq',
              asc: true,
            },
          ],
          fields: [
            {
              templateId: 'fldOA7p8ymYDpvhVYlFSIeBn',
              hidden: false,
              width: 284,
            },
            {
              templateId: 'fld6PbaihaAv43kQh4dkejpg',
              hidden: false,
            },
            {
              templateId: 'fldrwDsbvFZJ8yMPpD1qSXkq',
              hidden: false,
            },
            {
              templateId: 'fldbr1WwDVWBwhDJ8YXDQEBW',
              hidden: false,
            },
            {
              templateId: 'fldnF258uuSDH7EW5D5LFZng',
              hidden: false,
            },
            {
              templateId: 'fldycoJ9pZN8B9OJjb7QbHVB',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'fldOA7p8ymYDpvhVYlFSIeBn',
          privilege: 'TYPE_EDIT',
          name: 'Title',
          primary: true,
        },
        {
          type: 'DATETIME',
          templateId: 'fldrwDsbvFZJ8yMPpD1qSXkq',
          privilege: 'FULL_EDIT',
          name: 'Date',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fldnF258uuSDH7EW5D5LFZng',
          privilege: 'FULL_EDIT',
          name: 'Meeting record',
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldycoJ9pZN8B9OJjb7QbHVB',
          privilege: 'FULL_EDIT',
          name: 'Meeting Notes',
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fld6PbaihaAv43kQh4dkejpg',
          privilege: 'FULL_EDIT',
          name: 'Leads',
          property: {
            foreignDatabaseTemplateId: 'datp2gT0iVDTuTiChytVJ2yp',
            brotherFieldTemplateId: 'fld8xNQLfTXY9UGcMbRQR46B',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldbr1WwDVWBwhDJ8YXDQEBW',
          privilege: 'FULL_EDIT',
          name: 'Visitor',
          property: {
            databaseTemplateId: 'datp2gT0iVDTuTiChytVJ2yp',
            relatedLinkFieldTemplateId: 'fld6PbaihaAv43kQh4dkejpg',
            lookupTargetFieldTemplateId: 'fldpagn3SLRDIGPBSBPRraaK',
            lookUpLimit: 'FIRST',
            rollUpType: 'VALUES',
            dataType: 'STRING',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'rec9mmA3OhhbpOBnOlrAMwWk',
          data: {
            fld6PbaihaAv43kQh4dkejpg: ['reck9eRcU6GsN2qOIZ6LyTTn'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/11/08-Abby Simmons',
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-11-08T00:00:00.000Z',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
          values: {
            fld6PbaihaAv43kQh4dkejpg: ['Abby Simmons'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/11/08-Abby Simmons',
            fldbr1WwDVWBwhDJ8YXDQEBW: [null],
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-11-08',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
        },
        {
          templateId: 'recLj3B1Uv38vrnbb2arNxrN',
          data: {
            fld6PbaihaAv43kQh4dkejpg: ['reco8Q5ryDYhJ4rimC8TapIl'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/11/10-Benjamin Peterson',
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-11-10T00:00:00.000Z',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
          values: {
            fld6PbaihaAv43kQh4dkejpg: ['Benjamin Peterson'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/11/10-Benjamin Peterson',
            fldbr1WwDVWBwhDJ8YXDQEBW: [null],
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-11-10',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
        },
        {
          templateId: 'recviwcfQFlnDZ5wGvqHJvfr',
          data: {
            fld6PbaihaAv43kQh4dkejpg: ['recq5FaMwtgLbrZ5MoYfzUaI'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/11/15-Christine Davis',
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-11-15T00:00:00.000Z',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
          values: {
            fld6PbaihaAv43kQh4dkejpg: ['Christine Davis'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/11/15-Christine Davis',
            fldbr1WwDVWBwhDJ8YXDQEBW: [null],
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-11-15',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
        },
        {
          templateId: 'recm7cfTwJ693NeWP8Uj5j16',
          data: {
            fld6PbaihaAv43kQh4dkejpg: ['reco8Q5ryDYhJ4rimC8TapIl'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/09/07-Benjamin Peterson',
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-09-07T00:00:00.000Z',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
          values: {
            fld6PbaihaAv43kQh4dkejpg: ['Benjamin Peterson'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/09/07-Benjamin Peterson',
            fldbr1WwDVWBwhDJ8YXDQEBW: [null],
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-09-07',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
        },
        {
          templateId: 'rec0xGlbUcTH0XP5SKsNadei',
          data: {
            fld6PbaihaAv43kQh4dkejpg: ['recq3jGh7jM4KJ4FQBN9pJiP'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/10/16-Edward Thompson',
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-10-16T00:00:00.000Z',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
          values: {
            fld6PbaihaAv43kQh4dkejpg: ['Edward Thompson'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/10/16-Edward Thompson',
            fldbr1WwDVWBwhDJ8YXDQEBW: [null],
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-10-16',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
        },
        {
          templateId: 'recFCm2Tc2RTwb7HqiRbuXSq',
          data: {
            fld6PbaihaAv43kQh4dkejpg: ['reck9eRcU6GsN2qOIZ6LyTTn'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/09/07-Abby Simmons',
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-09-07T00:00:00.000Z',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
          values: {
            fld6PbaihaAv43kQh4dkejpg: ['Abby Simmons'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/09/07-Abby Simmons',
            fldbr1WwDVWBwhDJ8YXDQEBW: [null],
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-09-07',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
        },
        {
          templateId: 'reccbQW5R40NdUM0S5iGz4kV',
          data: {
            fld6PbaihaAv43kQh4dkejpg: ['rec3xnF9CoXpCa0DBCwLf245'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/09/07-Helen Jackson',
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-09-07T00:00:00.000Z',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
          values: {
            fld6PbaihaAv43kQh4dkejpg: ['Helen Jackson'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/09/07-Helen Jackson',
            fldbr1WwDVWBwhDJ8YXDQEBW: [null],
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-09-07',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
        },
        {
          templateId: 'recQp465sGy7CNbLEH4bFpsa',
          data: {
            fld6PbaihaAv43kQh4dkejpg: ['recm8kelPnMMhZ59pHc6wsn0'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/09/07-George Harris',
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-09-07T00:00:00.000Z',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
          values: {
            fld6PbaihaAv43kQh4dkejpg: ['George Harris'],
            fldOA7p8ymYDpvhVYlFSIeBn: '2023/09/07-George Harris',
            fldbr1WwDVWBwhDJ8YXDQEBW: [null],
            fldrwDsbvFZJ8yMPpD1qSXkq: '2023-09-07',
            fldycoJ9pZN8B9OJjb7QbHVB:
              '1. Profile: Medium-sized enterprise in the SaaS industry.\n' +
              '2. KPI: Reduce system downtime by 20% over the next year.\n' +
              '3. Budget: $50,000 for IT improvements.\n' +
              '4. Authority: Decision-maker for IT investments.\n' +
              '5. Need: A robust IT infrastructure with improved security features.\n' +
              '6. Timeline: Implementation by Q3 2023.',
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
