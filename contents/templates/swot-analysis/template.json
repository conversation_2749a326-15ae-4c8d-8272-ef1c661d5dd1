{"templateId": "swot-analysis", "name": "SWOT Analysis", "description": "The SWOT analysis, alternatively known as a SWOT matrix, aids in pinpointing the Strengths, Weaknesses, Opportunities, and Threats associated with any prospective decision-making process.", "cover": "/assets/template/swot-analysis/swot-analysis.png", "author": "<PERSON> <<EMAIL>>", "category": ["project"], "keywords": "SWOT, Opportunities, Threats", "useCases": "swot analysis, business strategy, decision-making", "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.3", "resources": [{"resourceType": "DATABASE", "templateId": "datfjs1yacdwxwVoBDxiLQt4", "name": "Factors", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwEXS1mtTOHYauu6r0dSMF5", "name": "Factors view", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "single_text", "hidden": false}, {"templateId": "detail", "hidden": false}, {"templateId": "origin", "hidden": false}, {"templateId": "effect", "hidden": false}, {"templateId": "swot", "hidden": false}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "single_text", "name": "Factors", "primary": true}, {"type": "SINGLE_TEXT", "templateId": "detail", "name": "Detail", "primary": false}, {"type": "SINGLE_SELECT", "templateId": "origin", "name": "Origin", "property": {"options": [{"id": "optnDZX6XPEGtcCLjIua0NCx", "name": "Internal", "color": "indigo5"}, {"id": "optlVOEYVkqyr3HhoAYFuBQ2", "name": "External", "color": "deepPurple"}]}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "effect", "name": "Effect", "property": {"options": [{"id": "optrRpv175ehkl9561MNIcqw", "name": "Positive", "color": "teal5"}, {"id": "optQFCnhvvP0PHklOE8OdyPN", "name": "Negative", "color": "pink5"}]}, "primary": false}, {"type": "FORMULA", "templateId": "swot", "name": "SWOT", "property": {"expressionTemplate": "IF(AND({origin}=\"Internal\", {effect}=\"Positive\"), \"Strength\",     IF(AND({origin}=\"Internal\", {effect}=\"Negative\"), \"Weakness\",         IF(AND({origin}=\"External\", {effect}=\"Positive\"), \"Opportunity\",             IF(AND({origin}=\"External\", {effect}=\"Negative\"), \"Threat\", \"\")         )     ) )"}, "primary": false}], "records": [{"templateId": "recKG4QsIJGsJ46QG5jnVbmm", "data": {"single_text": "Competitive Advantage", "effect": ["optrRpv175ehkl9561MNIcqw"], "origin": ["optnDZX6XPEGtcCLjIua0NCx"], "swot": "Strength", "detail": "Strong partnerships with ingredient sources."}, "values": {"single_text": "Competitive Advantage", "effect": ["Positive"], "origin": ["Internal"], "swot": "Strength", "detail": "Strong partnerships with ingredient sources."}}, {"templateId": "recxuoVGV0so64KA0kRTTkxx", "data": {"single_text": "Adverse Political Changes", "effect": ["optQFCnhvvP0PHklOE8OdyPN"], "origin": ["optlVOEYVkqyr3HhoAYFuBQ2"], "swot": "Threat", "detail": "New zoning policy, which may require a location change."}, "values": {"single_text": "Adverse Political Changes", "effect": ["Negative"], "origin": ["External"], "swot": "Threat", "detail": "New zoning policy, which may require a location change."}}, {"templateId": "reczgYeuOJvLqcgDaP8IWyjc", "data": {"single_text": "Adverse Demographic Changes", "effect": ["optQFCnhvvP0PHklOE8OdyPN"], "origin": ["optlVOEYVkqyr3HhoAYFuBQ2"], "swot": "Threat", "detail": "City demographic is trending to a population less likely to visit a bakery."}, "values": {"single_text": "Adverse Demographic Changes", "effect": ["Negative"], "origin": ["External"], "swot": "Threat", "detail": "City demographic is trending to a population less likely to visit a bakery."}}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}