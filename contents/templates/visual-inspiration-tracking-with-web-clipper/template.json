{"templateId": "visual-inspiration-tracking-with-web-clipper", "name": "Visual inspiration tracking with web clipper", "description": "This template is designed to help users easily collect and manage visual inspiration from the web. Whether you're a designer, artist, or anyone seeking creative ideas, this template allows you to quickly record and categorize various visual assets. Users can customize fields and views according to their needs, ensuring that their materials are organized effectively.", "cover": "/assets/template/visual-inspiration-tracking-with-web-clipper/visual-inspiration-tracking-with-web-clipper.png", "author": "<PERSON> <<EMAIL>>", "category": ["project"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "0.1.2", "resources": [{"resourceType": "DATABASE", "templateId": "datR6QuB5rIG7HX5xnTPnUD0", "name": "Visual inspiration", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwWlUyu8r2jXJxwNlxi9lTn", "name": "All images", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "single_text"}, {"templateId": "multi_select"}, {"templateId": "attachment"}, {"templateId": "fldr1TlaRCxxBxP8YIvJI40h"}, {"templateId": "fldwwx6ToDRK9bOlNmqdsiNw", "width": 527}, {"templateId": "flddvWGI2ciRBygH4XbIqTrL"}, {"templateId": "fldmd4qeXjSJj2NiK5yEkIz2"}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "single_text", "privilege": "TYPE_EDIT", "name": "Page title", "primary": true}, {"type": "MULTI_SELECT", "templateId": "multi_select", "privilege": "NAME_EDIT", "name": "Tags", "property": {"options": [{"id": "1", "name": "Logo", "color": "blue2"}, {"id": "2", "name": "Photo", "color": "pink4"}, {"id": "optYPZm8wKmqdnPMEP4qHvWG", "name": "Web design", "color": "deepPurple"}, {"id": "optPbQWnfV97OdPdMr8Pc5Tr", "name": "Illustration", "color": "green4"}, {"id": "optAgsxShvnUkCgVSx8nwU8c", "name": "Publication", "color": "tangerine2"}, {"id": "opthox5LqYCaBte8teAsTwa7", "name": "Social media", "color": "teal4"}]}, "primary": false}, {"type": "ATTACHMENT", "templateId": "attachment", "privilege": "NAME_EDIT", "name": "Attachment", "primary": false}, {"type": "MEMBER", "templateId": "fldr1TlaRCxxBxP8YIvJI40h", "privilege": "NAME_EDIT", "name": "Clipped by", "property": {}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldwwx6ToDRK9bOlNmqdsiNw", "privilege": "NAME_EDIT", "name": "Notes", "primary": false}, {"type": "URL", "templateId": "flddvWGI2ciRBygH4XbIqTrL", "privilege": "NAME_EDIT", "name": "URL", "primary": false}, {"type": "CREATED_TIME", "templateId": "fldmd4qeXjSJj2NiK5yEkIz2", "privilege": "NAME_EDIT", "name": "Date added", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": true}, "primary": false}], "records": [{"templateId": "recfmimlNICZraGXcGCNU914", "data": {"attachment": [{"name": "image.png", "id": "tplatt2AnpMBNzXNwDmmjkMPHGu", "path": "template/tplatt2AnpMBNzXNwDmmjkMPHGu.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 552369}], "single_text": "a mcdonald's restaurant sign lit up at night", "multi_select": ["1", "2", "opthox5LqYCaBte8teAsTwa7"], "fldwwx6ToDRK9bOlNmqdsiNw": "Download this free HD photo of logo, fast food restaurant, brand, and mcdonalds by <PERSON> (@mark991130)", "flddvWGI2ciRBygH4XbIqTrL": "https://unsplash.com/photos/a-mcdonalds-restaurant-sign-lit-up-at-night-Luhg-tamGfA"}, "values": {"attachment": ["image.png"], "single_text": "a mcdonald's restaurant sign lit up at night", "multi_select": ["Logo", "Photo", "Social media"], "fldwwx6ToDRK9bOlNmqdsiNw": "Download this free HD photo of logo, fast food restaurant, brand, and mcdonalds by <PERSON> (@mark991130)", "flddvWGI2ciRBygH4XbIqTrL": "https://unsplash.com/photos/a-mcdonalds-restaurant-sign-lit-up-at-night-Luhg-tamGfA"}}, {"templateId": "reck3X5jlgULz5v3NhXMI63o", "data": {"attachment": [{"name": "image.png", "id": "tplattcPcw6y9aKTPvfUfU2yH3H", "path": "template/tplattcPcw6y9aKTPvfUfU2yH3H.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 14266505}], "single_text": "a drawing of a green plant with leaves", "multi_select": ["optPbQWnfV97OdPdMr8Pc5Tr"], "fldwwx6ToDRK9bOlNmqdsiNw": "Image taken from page 491 of 'Matabele Land and the Victoria Falls. A naturalist's wanderings in the interior of South Africa. From the letters and journals of the late <PERSON>, F.R.G.S. Edited by <PERSON><PERSON> <PERSON><PERSON>. (Memoir.)'", "flddvWGI2ciRBygH4XbIqTrL": "https://unsplash.com/photos/a-drawing-of-a-green-plant-with-leaves-FQph76jy1mo"}, "values": {"attachment": ["image.png"], "single_text": "a drawing of a green plant with leaves", "multi_select": ["Illustration"], "fldwwx6ToDRK9bOlNmqdsiNw": "Image taken from page 491 of 'Matabele Land and the Victoria Falls. A naturalist's wanderings in the interior of South Africa. From the letters and journals of the late <PERSON>, F.R.G.S. Edited by <PERSON><PERSON> <PERSON><PERSON>. (Memoir.)'", "flddvWGI2ciRBygH4XbIqTrL": "https://unsplash.com/photos/a-drawing-of-a-green-plant-with-leaves-FQph76jy1mo"}}, {"templateId": "recjLdNjDw3Az3tomAu68A1W", "data": {"attachment": [{"name": "image.png", "id": "tplattq22Qeymhw0KMt3M8D2vNG", "path": "template/tplattq22Qeymhw0KMt3M8D2vNG.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 14076340}], "single_text": "A group of boats sitting on top of a sandy beach", "multi_select": ["2"], "fldwwx6ToDRK9bOlNmqdsiNw": "Download this free HD photo of maldives, ocean, boat, and birdeye in Maldives by <PERSON> (@birdeye)", "flddvWGI2ciRBygH4XbIqTrL": "https://unsplash.com/photos/a-group-of-boats-sitting-on-top-of-a-sandy-beach-8U34Ic7v6mE"}, "values": {"attachment": ["image.png"], "single_text": "A group of boats sitting on top of a sandy beach", "multi_select": ["Photo"], "fldwwx6ToDRK9bOlNmqdsiNw": "Download this free HD photo of maldives, ocean, boat, and birdeye in Maldives by <PERSON> (@birdeye)", "flddvWGI2ciRBygH4XbIqTrL": "https://unsplash.com/photos/a-group-of-boats-sitting-on-top-of-a-sandy-beach-8U34Ic7v6mE"}}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}