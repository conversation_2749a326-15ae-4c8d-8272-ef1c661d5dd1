import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'product-feedback-analysis',
  name: 'Product Feedback Analysis',
  description:
    'This template is designed to help teams efficiently collect and analyze customer feedback. Users can upload reviews, feedback, or other relevant data to identify common issues. The customizable dashboard allows for easy viewing and analysis of data, ultimately enhancing the overall customer experience.',
  cover: '/assets/template/product-feedback-analysis/product-feedback-analysis.png',
  author: '<PERSON> <<EMAIL>>',
  category: ['project'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '0.1.4',
  resources: [
    {
      resourceType: 'AUTOMATION',
      templateId: 'review-notification',
      name: 'Review Notification',
      triggers: [
        {
          triggerType: 'RECORD_CREATED',
          templateId: 'trg9qg4lNiVNSM5bQ2IJUjTn',
          description: 'When a new review is submitted.',
          input: {
            type: 'DATABASE',
            databaseTemplateId: 'database-reviews',
          },
        },
      ],
      actions: [
        {
          templateId: 'actn5babUsqXdIwBiO84debH',
          description: 'An email notification will be sent to the relevant parties.',
          actionType: 'SEND_EMAIL',
          input: {
            subject: 'You Have New Comments to Review',
            body: {
              markdown: 'You have new comments to review. [Click here](your-link-here) to view them.',
              json: {
                type: 'doc',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'You have new comments to review. [Click here](your-link-here) to view them.',
                        type: 'text',
                      },
                    ],
                  },
                ],
              },
            },
            to: [
              {
                type: 'EMAIL_STRING',
                email: '<EMAIL>',
              },
            ],
            senderName: '',
            cc: [],
            bcc: [],
            replyTo: [],
            type: 'SERVICE',
          },
        },
      ],
    },
    {
      resourceType: 'DASHBOARD',
      templateId: 'dsbytz11dnDiYsXp8uOk9HyN',
      name: 'Dashboard',
      widgets: [
        {
          templateId: 'wdtTCWC0YvnM7iWNS1oJnUQg',
          type: 'CHART',
          name: 'Review',
          datasource: {
            databaseTemplateId: 'database-products',
            viewTemplateId: 'view-all-products',
            type: 'DATABASE',
            chartType: 'pie',
            metricsType: 'COUNT_RECORDS',
            dimensionTemplateId: 'field-product-type',
          },
        },
        {
          templateId: 'wdtIz3d1IHX2P6BoSJQtS0f8',
          type: 'NUMBER',
          name: 'Count',
          summaryDescription: 'Reviews',
          datasource: {
            databaseTemplateId: 'database-reviews',
            viewTemplateId: 'view-review-list',
            type: 'DATABASE',
            metricsType: 'COUNT_RECORDS',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'database-products',
      name: 'Products',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'view-all-products',
          name: 'All products',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldWzcFl6kbTffYXsjPvKRNT',
              hidden: false,
            },
            {
              templateId: 'fldDlqQ0BSBxp4ydaw8wt3hB',
              hidden: false,
            },
            {
              templateId: 'field-product-type',
              hidden: false,
            },
            {
              templateId: 'fldJjWpERZdj0k1qIjFKy6u0',
              hidden: false,
            },
            {
              templateId: 'fldAuRZqzllhZlnrwmW0NNFN',
              hidden: true,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldWzcFl6kbTffYXsjPvKRNT',
          privilege: 'TYPE_EDIT',
          name: 'Product name',
          primary: true,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldDlqQ0BSBxp4ydaw8wt3hB',
          privilege: 'NAME_EDIT',
          name: 'Product description',
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'field-product-type',
          privilege: 'NAME_EDIT',
          name: 'Product type',
          property: {
            options: [
              {
                id: 'optS037OgJN4t6vKaf4em8dG',
                name: 'Smart Home Device',
                color: 'indigo4',
              },
              {
                id: 'opt0cA0waAqw8rEGAQEXMAFo',
                name: 'Digital Accessories',
                color: 'green3',
              },
              {
                id: 'optJXEy5fqbfCq0tHwrbEXEH',
                name: 'Digital Product',
                color: 'pink4',
              },
              {
                id: 'opt1qKA964hq0bdF8zAanJqi',
                name: 'Wearable Device',
                color: 'brown5',
              },
              {
                id: 'optGo0Hvz2aWNc6MCArH9F0M',
                name: 'Computer Equipment',
                color: 'blue5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldJjWpERZdj0k1qIjFKy6u0',
          privilege: 'NAME_EDIT',
          name: 'Reviews',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'fldCal7LTvbiLOGfkIw1bWWv',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldAuRZqzllhZlnrwmW0NNFN',
          privilege: 'NAME_EDIT',
          name: 'Reviews 2',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'fldp9Xi2nyB9H9USHOBJA8kl',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'rece0TbB25W1Owr5qiETLHXq',
          data: {
            fldAuRZqzllhZlnrwmW0NNFN: ['rec25xD7f3F9Cm2kgPJNdoKb'],
            fldDlqQ0BSBxp4ydaw8wt3hB:
              'Lightweight and portable, with high configuration and fast running speed, suitable for daily office and professional design.',
            fldJjWpERZdj0k1qIjFKy6u0: ['recMNO7uAAZON7PpjwEohJvU'],
            'field-product-type': ['optGo0Hvz2aWNc6MCArH9F0M'],
            fldWzcFl6kbTffYXsjPvKRNT: 'Laptop',
          },
          values: {
            fldAuRZqzllhZlnrwmW0NNFN: ['Poor design issue'],
            fldDlqQ0BSBxp4ydaw8wt3hB:
              'Lightweight and portable, with high configuration and fast running speed, suitable for daily office and professional design.',
            fldJjWpERZdj0k1qIjFKy6u0: ['Needs improvement'],
            'field-product-type': ['Computer Equipment'],
            fldWzcFl6kbTffYXsjPvKRNT: 'Laptop',
          },
        },
        {
          templateId: 'recmIRSmuXLDmnzWhhAbxWDX',
          data: {
            fldAuRZqzllhZlnrwmW0NNFN: ['rec2og0ZOAcdhoAc7PSev9Uw'],
            fldDlqQ0BSBxp4ydaw8wt3hB:
              'Can monitor health data, receive notifications and support multiple sports modes.',
            fldJjWpERZdj0k1qIjFKy6u0: ['rec2og0ZOAcdhoAc7PSev9Uw'],
            'field-product-type': ['opt1qKA964hq0bdF8zAanJqi'],
            fldWzcFl6kbTffYXsjPvKRNT: 'Smart Watch',
          },
          values: {
            fldAuRZqzllhZlnrwmW0NNFN: ['Fantastic service'],
            fldDlqQ0BSBxp4ydaw8wt3hB:
              'Can monitor health data, receive notifications and support multiple sports modes.',
            fldJjWpERZdj0k1qIjFKy6u0: ['Fantastic service'],
            'field-product-type': ['Wearable Device'],
            fldWzcFl6kbTffYXsjPvKRNT: 'Smart Watch',
          },
        },
        {
          templateId: 'recN9Apw8gfusuHR25i8TXYY',
          data: {
            fldAuRZqzllhZlnrwmW0NNFN: ['recpBIOKmbArKEate4XisWCO'],
            fldDlqQ0BSBxp4ydaw8wt3hB:
              'With high-definition screen and powerful performance, suitable for learning, entertainment and office work.',
            fldJjWpERZdj0k1qIjFKy6u0: ['rec25xD7f3F9Cm2kgPJNdoKb'],
            'field-product-type': ['optJXEy5fqbfCq0tHwrbEXEH'],
            fldWzcFl6kbTffYXsjPvKRNT: 'Tablet Computer',
          },
          values: {
            fldAuRZqzllhZlnrwmW0NNFN: ['Average experience'],
            fldDlqQ0BSBxp4ydaw8wt3hB:
              'With high-definition screen and powerful performance, suitable for learning, entertainment and office work.',
            fldJjWpERZdj0k1qIjFKy6u0: ['Poor design issue'],
            'field-product-type': ['Digital Product'],
            fldWzcFl6kbTffYXsjPvKRNT: 'Tablet Computer',
          },
        },
        {
          templateId: 'rec4mcF5HAR2aORM5WyvmAgq',
          data: {
            fldAuRZqzllhZlnrwmW0NNFN: ['recMNO7uAAZON7PpjwEohJvU'],
            fldDlqQ0BSBxp4ydaw8wt3hB:
              'Bluetooth connection, excellent noise cancellation, long battery life and comfortable to wear.',
            fldJjWpERZdj0k1qIjFKy6u0: ['recpBIOKmbArKEate4XisWCO'],
            'field-product-type': ['opt0cA0waAqw8rEGAQEXMAFo'],
            fldWzcFl6kbTffYXsjPvKRNT: 'Wireless Earphones',
          },
          values: {
            fldAuRZqzllhZlnrwmW0NNFN: ['Needs improvement'],
            fldDlqQ0BSBxp4ydaw8wt3hB:
              'Bluetooth connection, excellent noise cancellation, long battery life and comfortable to wear.',
            fldJjWpERZdj0k1qIjFKy6u0: ['Average experience'],
            'field-product-type': ['Digital Accessories'],
            fldWzcFl6kbTffYXsjPvKRNT: 'Wireless Earphones',
          },
        },
        {
          templateId: 'recDnGu0Q02NKeWztE56cOlm',
          data: {
            fldAuRZqzllhZlnrwmW0NNFN: ['rechERrRh2UzoE8DAffYyjXP'],
            fldDlqQ0BSBxp4ydaw8wt3hB:
              'It has high-quality sound and can be controlled by voice to play music, query information and other functions.',
            fldJjWpERZdj0k1qIjFKy6u0: ['rechERrRh2UzoE8DAffYyjXP'],
            'field-product-type': ['optS037OgJN4t6vKaf4em8dG'],
            fldWzcFl6kbTffYXsjPvKRNT: 'Smart Speaker',
          },
          values: {
            fldAuRZqzllhZlnrwmW0NNFN: ['Great product!'],
            fldDlqQ0BSBxp4ydaw8wt3hB:
              'It has high-quality sound and can be controlled by voice to play music, query information and other functions.',
            fldJjWpERZdj0k1qIjFKy6u0: ['Great product!'],
            'field-product-type': ['Smart Home Device'],
            fldWzcFl6kbTffYXsjPvKRNT: 'Smart Speaker',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datjF87zWfwTY0A7A7GupuUL',
      name: 'Onboarding',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viw6hTqRumOUGd3TjTWqvH97',
          name: 'Onboarding content',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldGwMTl5h9kP7GhF3Rx2VM2',
            },
            {
              templateId: 'fldcV49OzL0XtIpnlhrM7kFK',
            },
            {
              templateId: 'fldinKXaV8usdsall980Qa6x',
              width: 150,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldGwMTl5h9kP7GhF3Rx2VM2',
          privilege: 'TYPE_EDIT',
          name: 'Name',
          primary: true,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldcV49OzL0XtIpnlhrM7kFK',
          privilege: 'NAME_EDIT',
          name: 'Notes',
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fldinKXaV8usdsall980Qa6x',
          privilege: 'NAME_EDIT',
          name: 'Attachments',
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recaQTL9bRA1B2uK6FetXqHN',
          data: {
            fldGwMTl5h9kP7GhF3Rx2VM2: 'Step 5: Final Report',
            fldcV49OzL0XtIpnlhrM7kFK:
              'Compile all the data and insights into a final report for stakeholders, ensuring clarity and actionable recommendations.',
            fldinKXaV8usdsall980Qa6x: [
              {
                name: 'image.png',
                id: 'tplattR3HHnaW00gyyrPEbWtIkD',
                path: 'template/tplattR3HHnaW00gyyrPEbWtIkD.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 1997441,
              },
            ],
          },
          values: {
            fldGwMTl5h9kP7GhF3Rx2VM2: 'Step 5: Final Report',
            fldcV49OzL0XtIpnlhrM7kFK:
              'Compile all the data and insights into a final report for stakeholders, ensuring clarity and actionable recommendations.',
            fldinKXaV8usdsall980Qa6x: ['image.png'],
          },
        },
        {
          templateId: 'rec2PlnmN0tH0j5Nvns029tT',
          data: {
            fldGwMTl5h9kP7GhF3Rx2VM2: 'Step 4: Create Summary',
            fldcV49OzL0XtIpnlhrM7kFK:
              'Summarize the key findings for each category, such as highlighting customer satisfaction or areas for improvement.',
            fldinKXaV8usdsall980Qa6x: [
              {
                name: 'image.png',
                id: 'tplattp0aarH0RYWcw5pwH0Z43b',
                path: 'template/tplattp0aarH0RYWcw5pwH0Z43b.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 167950,
              },
            ],
          },
          values: {
            fldGwMTl5h9kP7GhF3Rx2VM2: 'Step 4: Create Summary',
            fldcV49OzL0XtIpnlhrM7kFK:
              'Summarize the key findings for each category, such as highlighting customer satisfaction or areas for improvement.',
            fldinKXaV8usdsall980Qa6x: ['image.png'],
          },
        },
        {
          templateId: 'recOakMhBFoMSrXG1ibiwBOy',
          data: {
            fldGwMTl5h9kP7GhF3Rx2VM2: 'Step 3: Organize Data',
            fldcV49OzL0XtIpnlhrM7kFK:
              'Map insights to specific reviews and create a structured format for categories and sentiments.',
            fldinKXaV8usdsall980Qa6x: [
              {
                name: 'image.png',
                id: 'tplattzWtDIRQWPwMaJAPpGV9lp',
                path: 'template/tplattzWtDIRQWPwMaJAPpGV9lp.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 1531885,
              },
            ],
          },
          values: {
            fldGwMTl5h9kP7GhF3Rx2VM2: 'Step 3: Organize Data',
            fldcV49OzL0XtIpnlhrM7kFK:
              'Map insights to specific reviews and create a structured format for categories and sentiments.',
            fldinKXaV8usdsall980Qa6x: ['image.png'],
          },
        },
        {
          templateId: 'recyhnGeIhS0ZnmhcyrlQZXX',
          data: {
            fldGwMTl5h9kP7GhF3Rx2VM2: 'Step 2: Analyze Insights',
            fldcV49OzL0XtIpnlhrM7kFK:
              'Analyze the collected reviews to identify key insights, such as improving delivery speed or enhancing customer support.',
            fldinKXaV8usdsall980Qa6x: [
              {
                name: 'image.png',
                id: 'tplattsj6JUdlj2jCtRCffPiPVy',
                path: 'template/tplattsj6JUdlj2jCtRCffPiPVy.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 191050,
              },
            ],
          },
          values: {
            fldGwMTl5h9kP7GhF3Rx2VM2: 'Step 2: Analyze Insights',
            fldcV49OzL0XtIpnlhrM7kFK:
              'Analyze the collected reviews to identify key insights, such as improving delivery speed or enhancing customer support.',
            fldinKXaV8usdsall980Qa6x: ['image.png'],
          },
        },
        {
          templateId: 'recRlC6o3Lj6RrEtRVWX9Fq3',
          data: {
            fldGwMTl5h9kP7GhF3Rx2VM2: 'Step 1: Review Feedback',
            fldcV49OzL0XtIpnlhrM7kFK:
              'Collect reviews from various sources and categorize them into themes such as "Customer Service" or "Product Design."',
            fldinKXaV8usdsall980Qa6x: [
              {
                name: 'image.png',
                id: 'tplattNczzRxMBUCDCpq4tG0NFA',
                path: 'template/tplattNczzRxMBUCDCpq4tG0NFA.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 94892,
              },
            ],
          },
          values: {
            fldGwMTl5h9kP7GhF3Rx2VM2: 'Step 1: Review Feedback',
            fldcV49OzL0XtIpnlhrM7kFK:
              'Collect reviews from various sources and categorize them into themes such as "Customer Service" or "Product Design."',
            fldinKXaV8usdsall980Qa6x: ['image.png'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datcRMWsEOKXkxyWD4QUdQ7n',
      name: 'Sentiments',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwwUFndG62T75j1Ag1QXagd',
          name: 'Sentiment list',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fld3YWNlEFBDwEOsVw1fzJdg',
              hidden: false,
            },
            {
              templateId: 'fldTQlFlyBLIaSQ4tcI9AD81',
              hidden: false,
              width: 150,
            },
            {
              templateId: 'fldBTaZgzxB35MAAZLKgUDBk',
              hidden: false,
            },
            {
              templateId: 'fldoyNWVVjNNNtjBvR0JxlGA',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'fld3YWNlEFBDwEOsVw1fzJdg',
          privilege: 'TYPE_EDIT',
          name: 'Name',
          primary: true,
        },
        {
          type: 'LINK',
          templateId: 'fldTQlFlyBLIaSQ4tcI9AD81',
          privilege: 'NAME_EDIT',
          name: 'Reviews',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'flduYE8lg6lyTHMzGDYecW5t',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldBTaZgzxB35MAAZLKgUDBk',
          privilege: 'NAME_EDIT',
          name: 'Sentiment lookup',
          property: {
            databaseTemplateId: 'database-reviews',
            relatedLinkFieldTemplateId: 'fldTQlFlyBLIaSQ4tcI9AD81',
            lookupTargetFieldTemplateId: 'fldVsRmZgR9iQyMXbkO3PUBC',
            dataType: 'STRING',
            lookUpLimit: 'ALL',
            rollUpType: 'VALUES',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldoyNWVVjNNNtjBvR0JxlGA',
          privilege: 'NAME_EDIT',
          name: 'Reviews 2',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'fldIeYVxmhIC6vOpEs9jDZGH',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'rec9GXl6NQmaFMBguv7wOWxO',
          data: {
            fld3YWNlEFBDwEOsVw1fzJdg: 'Positive',
            fldBTaZgzxB35MAAZLKgUDBk: null,
            fldTQlFlyBLIaSQ4tcI9AD81: ['rec2og0ZOAcdhoAc7PSev9Uw', 'rechERrRh2UzoE8DAffYyjXP'],
            fldoyNWVVjNNNtjBvR0JxlGA: ['rechERrRh2UzoE8DAffYyjXP', 'rec2og0ZOAcdhoAc7PSev9Uw'],
          },
          values: {
            fld3YWNlEFBDwEOsVw1fzJdg: 'Positive',
            fldBTaZgzxB35MAAZLKgUDBk: ['Fantastic service', 'Great product!'],
            fldTQlFlyBLIaSQ4tcI9AD81: ['Fantastic service', 'Great product!'],
            fldoyNWVVjNNNtjBvR0JxlGA: ['Great product!', 'Fantastic service'],
          },
        },
        {
          templateId: 'rec1PwdGk5YZia8yozlCtS5y',
          data: {
            fld3YWNlEFBDwEOsVw1fzJdg: 'Negative',
            fldBTaZgzxB35MAAZLKgUDBk: null,
            fldTQlFlyBLIaSQ4tcI9AD81: ['recMNO7uAAZON7PpjwEohJvU', 'rec25xD7f3F9Cm2kgPJNdoKb'],
            fldoyNWVVjNNNtjBvR0JxlGA: ['recMNO7uAAZON7PpjwEohJvU', 'rec25xD7f3F9Cm2kgPJNdoKb'],
          },
          values: {
            fld3YWNlEFBDwEOsVw1fzJdg: 'Negative',
            fldBTaZgzxB35MAAZLKgUDBk: ['Needs improvement', 'Poor design issue'],
            fldTQlFlyBLIaSQ4tcI9AD81: ['Needs improvement', 'Poor design issue'],
            fldoyNWVVjNNNtjBvR0JxlGA: ['Needs improvement', 'Poor design issue'],
          },
        },
        {
          templateId: 'rec1OufAr7I8qBTLqxIeMLo0',
          data: {
            fld3YWNlEFBDwEOsVw1fzJdg: 'Neutral',
            fldBTaZgzxB35MAAZLKgUDBk: null,
            fldTQlFlyBLIaSQ4tcI9AD81: ['recMNO7uAAZON7PpjwEohJvU', 'recpBIOKmbArKEate4XisWCO'],
            fldoyNWVVjNNNtjBvR0JxlGA: ['recpBIOKmbArKEate4XisWCO', 'recMNO7uAAZON7PpjwEohJvU'],
          },
          values: {
            fld3YWNlEFBDwEOsVw1fzJdg: 'Neutral',
            fldBTaZgzxB35MAAZLKgUDBk: ['Average experience', 'Needs improvement'],
            fldTQlFlyBLIaSQ4tcI9AD81: ['Needs improvement', 'Average experience'],
            fldoyNWVVjNNNtjBvR0JxlGA: ['Average experience', 'Needs improvement'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datgpD7bWIYEyvZ8fZbmvYdi',
      name: 'Insights',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwUmxXdd6gsP7ZLvntrsodk',
          name: 'All',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fld2RZsx7S70JF1dxkZvWsme',
            },
            {
              templateId: 'fldABt5RoYSjI8pWDYpKnwNy',
              width: 150,
            },
            {
              templateId: 'fldxDZYtierHuO3K0g6bd2dk',
              hidden: true,
              width: 150,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'fld2RZsx7S70JF1dxkZvWsme',
          privilege: 'TYPE_EDIT',
          name: 'Insight',
          primary: true,
        },
        {
          type: 'LINK',
          templateId: 'fldABt5RoYSjI8pWDYpKnwNy',
          privilege: 'NAME_EDIT',
          name: 'Reviews',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'fld198ulzfQ0MwnSGknvTc3s',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldxDZYtierHuO3K0g6bd2dk',
          privilege: 'NAME_EDIT',
          name: 'Review lookup',
          property: {
            databaseTemplateId: 'database-reviews',
            relatedLinkFieldTemplateId: 'fldABt5RoYSjI8pWDYpKnwNy',
            lookupTargetFieldTemplateId: 'fldVsRmZgR9iQyMXbkO3PUBC',
            dataType: 'STRING',
            lookUpLimit: 'ALL',
            rollUpType: 'VALUES',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recSZB8VrOa89TXEVhS0MIeI',
          data: {
            fld2RZsx7S70JF1dxkZvWsme: 'Offer better pricing strategies',
            fldABt5RoYSjI8pWDYpKnwNy: ['recMNO7uAAZON7PpjwEohJvU'],
            fldxDZYtierHuO3K0g6bd2dk: null,
          },
          values: {
            fld2RZsx7S70JF1dxkZvWsme: 'Offer better pricing strategies',
            fldABt5RoYSjI8pWDYpKnwNy: ['Needs improvement'],
            fldxDZYtierHuO3K0g6bd2dk: ['Needs improvement'],
          },
        },
        {
          templateId: 'recZSEXHOae959bNWE6JObHJ',
          data: {
            fld2RZsx7S70JF1dxkZvWsme: 'Exceptional customer experience',
            fldABt5RoYSjI8pWDYpKnwNy: ['rec2og0ZOAcdhoAc7PSev9Uw'],
            fldxDZYtierHuO3K0g6bd2dk: null,
          },
          values: {
            fld2RZsx7S70JF1dxkZvWsme: 'Exceptional customer experience',
            fldABt5RoYSjI8pWDYpKnwNy: ['Fantastic service'],
            fldxDZYtierHuO3K0g6bd2dk: ['Fantastic service'],
          },
        },
        {
          templateId: 'rec9VdL9QhZzkGeVJ8PVSrkx',
          data: {
            fld2RZsx7S70JF1dxkZvWsme: 'Improve product design',
            fldABt5RoYSjI8pWDYpKnwNy: ['rec25xD7f3F9Cm2kgPJNdoKb'],
            fldxDZYtierHuO3K0g6bd2dk: null,
          },
          values: {
            fld2RZsx7S70JF1dxkZvWsme: 'Improve product design',
            fldABt5RoYSjI8pWDYpKnwNy: ['Poor design issue'],
            fldxDZYtierHuO3K0g6bd2dk: ['Poor design issue'],
          },
        },
        {
          templateId: 'recx4psPoaCDZvZwMzzBqzIq',
          data: {
            fld2RZsx7S70JF1dxkZvWsme: 'Improve delivery speed',
            fldABt5RoYSjI8pWDYpKnwNy: ['recpBIOKmbArKEate4XisWCO'],
            fldxDZYtierHuO3K0g6bd2dk: null,
          },
          values: {
            fld2RZsx7S70JF1dxkZvWsme: 'Improve delivery speed',
            fldABt5RoYSjI8pWDYpKnwNy: ['Average experience'],
            fldxDZYtierHuO3K0g6bd2dk: ['Average experience'],
          },
        },
        {
          templateId: 'rec2MqM3OokZvYirYyAP3fJ0',
          data: {
            fld2RZsx7S70JF1dxkZvWsme: 'Great customer support',
            fldABt5RoYSjI8pWDYpKnwNy: ['rechERrRh2UzoE8DAffYyjXP'],
            fldxDZYtierHuO3K0g6bd2dk: null,
          },
          values: {
            fld2RZsx7S70JF1dxkZvWsme: 'Great customer support',
            fldABt5RoYSjI8pWDYpKnwNy: ['Great product!'],
            fldxDZYtierHuO3K0g6bd2dk: ['Great product!'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datFzPquJlfpSUn4dM8ArzPw',
      name: 'Categories',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwG1VBBYDyKfTHWUKC8ECU8',
          name: 'Category list',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldvs403xW49E2grkXThHZiD',
              hidden: false,
            },
            {
              templateId: 'fldtf3ine5rOb7LSMvIOa84A',
              hidden: false,
            },
            {
              templateId: 'fldKIvwWw90Vgh8mTiNxzYw2',
              hidden: true,
            },
            {
              templateId: 'fldAyLgkLDhlZQr5Aqz4slTb',
              hidden: true,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldvs403xW49E2grkXThHZiD',
          privilege: 'TYPE_EDIT',
          name: 'Category Name',
          primary: true,
        },
        {
          type: 'LINK',
          templateId: 'fldtf3ine5rOb7LSMvIOa84A',
          privilege: 'NAME_EDIT',
          name: 'Reviews',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'fld4zvFc5s6rEjnDVRPoKoGQ',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldKIvwWw90Vgh8mTiNxzYw2',
          privilege: 'NAME_EDIT',
          name: 'Review lookup',
          property: {
            databaseTemplateId: 'database-reviews',
            relatedLinkFieldTemplateId: 'fldtf3ine5rOb7LSMvIOa84A',
            lookupTargetFieldTemplateId: 'fldQYq7ECtsWhqilBIaYovCX',
            dataType: 'STRING',
            lookUpLimit: 'ALL',
            rollUpType: 'VALUES',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldAyLgkLDhlZQr5Aqz4slTb',
          privilege: 'NAME_EDIT',
          name: 'Reviews 2',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'fldGeiWitB2ZEGHJDsBDobgB',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recyCDeJuTVYCLX9dTEz8klw',
          data: {
            fldAyLgkLDhlZQr5Aqz4slTb: ['recMNO7uAAZON7PpjwEohJvU'],
            fldKIvwWw90Vgh8mTiNxzYw2: null,
            fldtf3ine5rOb7LSMvIOa84A: ['recMNO7uAAZON7PpjwEohJvU'],
            fldvs403xW49E2grkXThHZiD: 'Overall Experience',
          },
          values: {
            fldAyLgkLDhlZQr5Aqz4slTb: ['Needs improvement'],
            fldKIvwWw90Vgh8mTiNxzYw2: [
              'The product is overpriced for its features. There are better alternatives in the market for the same price range.',
            ],
            fldtf3ine5rOb7LSMvIOa84A: ['Needs improvement'],
            fldvs403xW49E2grkXThHZiD: 'Overall Experience',
          },
        },
        {
          templateId: 'recZECEAU4wPmWioliQkvFNk',
          data: {
            fldAyLgkLDhlZQr5Aqz4slTb: ['rec2og0ZOAcdhoAc7PSev9Uw'],
            fldKIvwWw90Vgh8mTiNxzYw2: null,
            fldtf3ine5rOb7LSMvIOa84A: ['rec2og0ZOAcdhoAc7PSev9Uw'],
            fldvs403xW49E2grkXThHZiD: 'Pricing Strategy',
          },
          values: {
            fldAyLgkLDhlZQr5Aqz4slTb: ['Fantastic service'],
            fldKIvwWw90Vgh8mTiNxzYw2: [
              'The team went above and beyond to assist me with my issue. The product also works perfectly.',
            ],
            fldtf3ine5rOb7LSMvIOa84A: ['Fantastic service'],
            fldvs403xW49E2grkXThHZiD: 'Pricing Strategy',
          },
        },
        {
          templateId: 'recFpJ54q4aKrJTeBdVh3nqE',
          data: {
            fldAyLgkLDhlZQr5Aqz4slTb: ['rec25xD7f3F9Cm2kgPJNdoKb'],
            fldKIvwWw90Vgh8mTiNxzYw2: null,
            fldtf3ine5rOb7LSMvIOa84A: ['rec25xD7f3F9Cm2kgPJNdoKb'],
            fldvs403xW49E2grkXThHZiD: 'Product Design',
          },
          values: {
            fldAyLgkLDhlZQr5Aqz4slTb: ['Poor design issue'],
            fldKIvwWw90Vgh8mTiNxzYw2: [
              'The design is not user-friendly, and it feels flimsy. I regret purchasing this product.',
            ],
            fldtf3ine5rOb7LSMvIOa84A: ['Poor design issue'],
            fldvs403xW49E2grkXThHZiD: 'Product Design',
          },
        },
        {
          templateId: 'recQ1VX5rx7SdhpMwl7C8wnc',
          data: {
            fldAyLgkLDhlZQr5Aqz4slTb: ['recpBIOKmbArKEate4XisWCO'],
            fldKIvwWw90Vgh8mTiNxzYw2: null,
            fldtf3ine5rOb7LSMvIOa84A: ['recpBIOKmbArKEate4XisWCO'],
            fldvs403xW49E2grkXThHZiD: 'Delivery Experience',
          },
          values: {
            fldAyLgkLDhlZQr5Aqz4slTb: ['Average experience'],
            fldKIvwWw90Vgh8mTiNxzYw2: [
              'The product works fine, but the delivery took too long, and the packaging was damaged upon arrival.',
            ],
            fldtf3ine5rOb7LSMvIOa84A: ['Average experience'],
            fldvs403xW49E2grkXThHZiD: 'Delivery Experience',
          },
        },
        {
          templateId: 'recMcS0RwEkQaCNLHJIi95P5',
          data: {
            fldAyLgkLDhlZQr5Aqz4slTb: ['rechERrRh2UzoE8DAffYyjXP'],
            fldKIvwWw90Vgh8mTiNxzYw2: null,
            fldtf3ine5rOb7LSMvIOa84A: ['rechERrRh2UzoE8DAffYyjXP'],
            fldvs403xW49E2grkXThHZiD: 'Customer Service',
          },
          values: {
            fldAyLgkLDhlZQr5Aqz4slTb: ['Great product!'],
            fldKIvwWw90Vgh8mTiNxzYw2: [
              'The product quality is excellent, and customer service was very responsive. Highly recommend this to everyone!',
            ],
            fldtf3ine5rOb7LSMvIOa84A: ['Great product!'],
            fldvs403xW49E2grkXThHZiD: 'Customer Service',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'database-reviews',
      name: 'Reviews',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'view-review-list',
          name: 'Review list',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldVsRmZgR9iQyMXbkO3PUBC',
              hidden: false,
              width: 302,
            },
            {
              templateId: 'fldp9Xi2nyB9H9USHOBJA8kl',
              hidden: false,
              width: 350,
            },
            {
              templateId: 'fldQYq7ECtsWhqilBIaYovCX',
              hidden: false,
            },
            {
              templateId: 'fldplqUCAtUlczUAd18MiglB',
              hidden: false,
              width: 205,
            },
            {
              templateId: 'fldGeiWitB2ZEGHJDsBDobgB',
              hidden: false,
            },
            {
              templateId: 'fldFZNNAQ6az72H5HttdWJc3',
              hidden: true,
              width: 335,
            },
            {
              templateId: 'fldNpnWFDfMHcniHZ8cjV7OE',
              hidden: false,
            },
            {
              templateId: 'fldGZ9pZmKU4jb9BPfWBX8VB',
              hidden: false,
            },
            {
              templateId: 'fldIeYVxmhIC6vOpEs9jDZGH',
              hidden: false,
              width: 245,
            },
            {
              templateId: 'fldXHgxLyzmETekp69eIyrzs',
              hidden: false,
            },
            {
              templateId: 'fldZ2zOGztkym76rov37cJK0',
              hidden: true,
              width: 315,
            },
            {
              templateId: 'fld8AbTJf7kRGDwMp4SCICMD',
              hidden: false,
            },
            {
              templateId: 'fld5pmChjmj6ZgQzlTZ9S54X',
              hidden: true,
              width: 299,
            },
            {
              templateId: 'fldrS2y2KqRIqL3KAA09TfnB',
              hidden: false,
            },
            {
              templateId: 'fld8ACP0qVxoouQyOZHuw6qs',
              hidden: true,
            },
            {
              templateId: 'fldpNJjQMmpZq4XgxsRAEVpF',
              hidden: true,
            },
            {
              templateId: 'fldPFOV0p8rMtTCoatVk1Dsc',
              hidden: true,
            },
            {
              templateId: 'fldoV2XKHRPDMAae2FhJRfze',
              hidden: true,
            },
            {
              templateId: 'fld4zvFc5s6rEjnDVRPoKoGQ',
              hidden: true,
            },
            {
              templateId: 'fldqkrGlTfROqjwqhu3RbtMq',
              hidden: true,
            },
            {
              templateId: 'fld198ulzfQ0MwnSGknvTc3s',
              hidden: true,
            },
            {
              templateId: 'fldqGkkEWNmACiimoszOQbbW',
              hidden: true,
            },
            {
              templateId: 'fldrRoGUybFuu60fahhxSLo5',
              hidden: true,
            },
            {
              templateId: 'fldCal7LTvbiLOGfkIw1bWWv',
              hidden: true,
            },
            {
              templateId: 'flduYE8lg6lyTHMzGDYecW5t',
              hidden: true,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldVsRmZgR9iQyMXbkO3PUBC',
          privilege: 'TYPE_EDIT',
          name: 'Review title',
          primary: true,
        },
        {
          type: 'LINK',
          templateId: 'fldp9Xi2nyB9H9USHOBJA8kl',
          privilege: 'NAME_EDIT',
          name: 'Products',
          property: {
            foreignDatabaseTemplateId: 'database-products',
            brotherFieldTemplateId: 'fldAuRZqzllhZlnrwmW0NNFN',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldQYq7ECtsWhqilBIaYovCX',
          privilege: 'NAME_EDIT',
          name: 'Review',
          primary: false,
        },
        {
          type: 'RATING',
          templateId: 'fldplqUCAtUlczUAd18MiglB',
          privilege: 'NAME_EDIT',
          name: '5 star rating',
          property: {
            icon: {
              type: 'EMOJI',
              emoji: '⭐️',
            },
            max: 5,
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldGeiWitB2ZEGHJDsBDobgB',
          privilege: 'NAME_EDIT',
          name: 'Category',
          property: {
            foreignDatabaseTemplateId: 'datFzPquJlfpSUn4dM8ArzPw',
            brotherFieldTemplateId: 'fldAyLgkLDhlZQr5Aqz4slTb',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fld8ACP0qVxoouQyOZHuw6qs',
          privilege: 'NAME_EDIT',
          name: 'Other reviews in this category (lookup)',
          property: {
            databaseTemplateId: 'datFzPquJlfpSUn4dM8ArzPw',
            relatedLinkFieldTemplateId: 'fldGeiWitB2ZEGHJDsBDobgB',
            lookupTargetFieldTemplateId: 'fldvs403xW49E2grkXThHZiD',
            dataType: 'STRING',
            lookUpLimit: 'ALL',
            rollUpType: 'VALUES',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldFZNNAQ6az72H5HttdWJc3',
          privilege: 'NAME_EDIT',
          name: 'Other reviews in this category',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'fldqGkkEWNmACiimoszOQbbW',
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'fldNpnWFDfMHcniHZ8cjV7OE',
          privilege: 'NAME_EDIT',
          name: 'Positive?',
          property: {
            expressionTemplate: 'IF({fldIeYVxmhIC6vOpEs9jDZGH}=“positive”,"Yes","No")',
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'fldGZ9pZmKU4jb9BPfWBX8VB',
          privilege: 'NAME_EDIT',
          name: 'Negative',
          property: {
            expressionTemplate: 'IF({fldIeYVxmhIC6vOpEs9jDZGH}=“Negative”,"Yes","No")',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldIeYVxmhIC6vOpEs9jDZGH',
          privilege: 'NAME_EDIT',
          name: 'Sentiment',
          property: {
            foreignDatabaseTemplateId: 'datcRMWsEOKXkxyWD4QUdQ7n',
            brotherFieldTemplateId: 'fldoyNWVVjNNNtjBvR0JxlGA',
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldpNJjQMmpZq4XgxsRAEVpF',
          privilege: 'NAME_EDIT',
          name: 'Insights',
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldXHgxLyzmETekp69eIyrzs',
          privilege: 'NAME_EDIT',
          name: 'Sentiment tag',
          property: {
            options: [
              {
                id: 'optcQAWn0ZhZ83ESCMBGiLWO',
                name: 'Positive',
                color: 'deepPurple4',
              },
              {
                id: 'optW3ZokweidfqtVaWNAebVg',
                name: 'Neutral',
                color: 'blue4',
              },
              {
                id: 'opt9OOr8dXbK1ZhutDBIFTs9',
                name: 'Negative',
                color: 'green3',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldPFOV0p8rMtTCoatVk1Dsc',
          privilege: 'NAME_EDIT',
          name: 'Other reviews with this sentiment (lookup)',
          property: {
            databaseTemplateId: 'datcRMWsEOKXkxyWD4QUdQ7n',
            relatedLinkFieldTemplateId: 'fldIeYVxmhIC6vOpEs9jDZGH',
            lookupTargetFieldTemplateId: 'fldBTaZgzxB35MAAZLKgUDBk',
            dataType: 'STRING',
            lookUpLimit: 'ALL',
            rollUpType: 'VALUES',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldZ2zOGztkym76rov37cJK0',
          privilege: 'NAME_EDIT',
          name: 'Other reviews with this sentiment',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'fldrRoGUybFuu60fahhxSLo5',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fld8AbTJf7kRGDwMp4SCICMD',
          privilege: 'NAME_EDIT',
          name: 'Date of Review',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldoV2XKHRPDMAae2FhJRfze',
          privilege: 'NAME_EDIT',
          name: 'Other reviews with this insight (lookup)',
          property: {
            databaseTemplateId: 'database-products',
            relatedLinkFieldTemplateId: 'fldp9Xi2nyB9H9USHOBJA8kl',
            lookupTargetFieldTemplateId: 'fld2RZsx7S70JF1dxkZvWsme',
            dataType: 'STRING',
            lookUpLimit: 'ALL',
            rollUpType: 'VALUES',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fld5pmChjmj6ZgQzlTZ9S54X',
          privilege: 'NAME_EDIT',
          name: 'Other reviews with this insight',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'fldqkrGlTfROqjwqhu3RbtMq',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldrS2y2KqRIqL3KAA09TfnB',
          privilege: 'NAME_EDIT',
          name: 'Review source',
          property: {
            options: [
              {
                id: 'optRBViyHoXLPP6tfW9K4osG',
                name: 'Website',
                color: 'deepPurple',
              },
              {
                id: 'opt44a5VaRFU8ueEmSVYPE1Q',
                name: 'App',
                color: 'indigo',
              },
              {
                id: 'opt64tkStDLbyLMEEMOS1ybK',
                name: 'Social media',
                color: 'blue',
              },
              {
                id: 'optQ5vz5xq5ZTma75LEcxp67',
                name: 'Email',
                color: 'teal',
              },
              {
                id: 'optrhWAvVJ9dX01Nud012EAT',
                name: 'Survey',
                color: 'green',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fld4zvFc5s6rEjnDVRPoKoGQ',
          privilege: 'NAME_EDIT',
          name: 'Categories',
          property: {
            foreignDatabaseTemplateId: 'datFzPquJlfpSUn4dM8ArzPw',
            brotherFieldTemplateId: 'fldtf3ine5rOb7LSMvIOa84A',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldqkrGlTfROqjwqhu3RbtMq',
          privilege: 'NAME_EDIT',
          name: 'Reviews',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'fld5pmChjmj6ZgQzlTZ9S54X',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fld198ulzfQ0MwnSGknvTc3s',
          privilege: 'NAME_EDIT',
          name: 'Insights 2',
          property: {
            foreignDatabaseTemplateId: 'datgpD7bWIYEyvZ8fZbmvYdi',
            brotherFieldTemplateId: 'fldABt5RoYSjI8pWDYpKnwNy',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldqGkkEWNmACiimoszOQbbW',
          privilege: 'NAME_EDIT',
          name: 'Reviews 2',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'fldFZNNAQ6az72H5HttdWJc3',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldrRoGUybFuu60fahhxSLo5',
          privilege: 'NAME_EDIT',
          name: 'Reviews 3',
          property: {
            foreignDatabaseTemplateId: 'database-reviews',
            brotherFieldTemplateId: 'fldZ2zOGztkym76rov37cJK0',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldCal7LTvbiLOGfkIw1bWWv',
          privilege: 'NAME_EDIT',
          name: 'Products 2',
          property: {
            foreignDatabaseTemplateId: 'database-products',
            brotherFieldTemplateId: 'fldJjWpERZdj0k1qIjFKy6u0',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'flduYE8lg6lyTHMzGDYecW5t',
          privilege: 'NAME_EDIT',
          name: 'Sentiments',
          property: {
            foreignDatabaseTemplateId: 'datcRMWsEOKXkxyWD4QUdQ7n',
            brotherFieldTemplateId: 'fldTQlFlyBLIaSQ4tcI9AD81',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recMNO7uAAZON7PpjwEohJvU',
          data: {
            fld198ulzfQ0MwnSGknvTc3s: ['recSZB8VrOa89TXEVhS0MIeI'],
            fld4zvFc5s6rEjnDVRPoKoGQ: ['recyCDeJuTVYCLX9dTEz8klw'],
            fld5pmChjmj6ZgQzlTZ9S54X: ['recMNO7uAAZON7PpjwEohJvU'],
            fld8ACP0qVxoouQyOZHuw6qs: null,
            fld8AbTJf7kRGDwMp4SCICMD: '2024-12-08T16:00:00.000Z',
            fldCal7LTvbiLOGfkIw1bWWv: ['rece0TbB25W1Owr5qiETLHXq'],
            fldFZNNAQ6az72H5HttdWJc3: ['recMNO7uAAZON7PpjwEohJvU'],
            fldGZ9pZmKU4jb9BPfWBX8VB: null,
            fldGeiWitB2ZEGHJDsBDobgB: ['recyCDeJuTVYCLX9dTEz8klw'],
            fldIeYVxmhIC6vOpEs9jDZGH: ['rec1PwdGk5YZia8yozlCtS5y'],
            fldNpnWFDfMHcniHZ8cjV7OE: null,
            fldPFOV0p8rMtTCoatVk1Dsc: null,
            fldQYq7ECtsWhqilBIaYovCX:
              'The product is overpriced for its features. There are better alternatives in the market for the same price range.',
            fldVsRmZgR9iQyMXbkO3PUBC: 'Needs improvement',
            fldXHgxLyzmETekp69eIyrzs: ['opt9OOr8dXbK1ZhutDBIFTs9'],
            fldZ2zOGztkym76rov37cJK0: ['recMNO7uAAZON7PpjwEohJvU'],
            fldoV2XKHRPDMAae2FhJRfze: null,
            fldp9Xi2nyB9H9USHOBJA8kl: ['rec4mcF5HAR2aORM5WyvmAgq'],
            fldpNJjQMmpZq4XgxsRAEVpF: ['recSZB8VrOa89TXEVhS0MIeI'],
            fldplqUCAtUlczUAd18MiglB: 2,
            fldqGkkEWNmACiimoszOQbbW: ['recMNO7uAAZON7PpjwEohJvU'],
            fldqkrGlTfROqjwqhu3RbtMq: ['recMNO7uAAZON7PpjwEohJvU'],
            fldrRoGUybFuu60fahhxSLo5: ['recMNO7uAAZON7PpjwEohJvU'],
            fldrS2y2KqRIqL3KAA09TfnB: ['optrhWAvVJ9dX01Nud012EAT'],
            flduYE8lg6lyTHMzGDYecW5t: ['rec1PwdGk5YZia8yozlCtS5y', 'rec1OufAr7I8qBTLqxIeMLo0'],
          },
          values: {
            fld198ulzfQ0MwnSGknvTc3s: ['Offer better pricing strategies'],
            fld4zvFc5s6rEjnDVRPoKoGQ: ['Overall Experience'],
            fld5pmChjmj6ZgQzlTZ9S54X: ['Needs improvement'],
            fld8ACP0qVxoouQyOZHuw6qs: ['Overall Experience'],
            fld8AbTJf7kRGDwMp4SCICMD: '2024-12-08',
            fldCal7LTvbiLOGfkIw1bWWv: ['Laptop'],
            fldFZNNAQ6az72H5HttdWJc3: ['Needs improvement'],
            fldGZ9pZmKU4jb9BPfWBX8VB: 'Yes',
            fldGeiWitB2ZEGHJDsBDobgB: ['Overall Experience'],
            fldIeYVxmhIC6vOpEs9jDZGH: ['Negative'],
            fldNpnWFDfMHcniHZ8cjV7OE: 'No',
            fldPFOV0p8rMtTCoatVk1Dsc: [null],
            fldQYq7ECtsWhqilBIaYovCX:
              'The product is overpriced for its features. There are better alternatives in the market for the same price range.',
            fldVsRmZgR9iQyMXbkO3PUBC: 'Needs improvement',
            fldXHgxLyzmETekp69eIyrzs: ['Negative'],
            fldZ2zOGztkym76rov37cJK0: ['Needs improvement'],
            fldoV2XKHRPDMAae2FhJRfze: ['Offer better pricing strategies'],
            fldp9Xi2nyB9H9USHOBJA8kl: ['Wireless Earphones'],
            fldpNJjQMmpZq4XgxsRAEVpF: ['Offer better pricing strategies'],
            fldplqUCAtUlczUAd18MiglB: '2',
            fldqGkkEWNmACiimoszOQbbW: ['Needs improvement'],
            fldqkrGlTfROqjwqhu3RbtMq: ['Needs improvement'],
            fldrRoGUybFuu60fahhxSLo5: ['Needs improvement'],
            fldrS2y2KqRIqL3KAA09TfnB: ['Survey'],
            flduYE8lg6lyTHMzGDYecW5t: ['Negative', 'Neutral'],
          },
        },
        {
          templateId: 'rec2og0ZOAcdhoAc7PSev9Uw',
          data: {
            fld198ulzfQ0MwnSGknvTc3s: ['recZSEXHOae959bNWE6JObHJ'],
            fld4zvFc5s6rEjnDVRPoKoGQ: ['recZECEAU4wPmWioliQkvFNk'],
            fld5pmChjmj6ZgQzlTZ9S54X: ['rec2og0ZOAcdhoAc7PSev9Uw'],
            fld8ACP0qVxoouQyOZHuw6qs: null,
            fld8AbTJf7kRGDwMp4SCICMD: '2024-11-12T16:00:00.000Z',
            fldCal7LTvbiLOGfkIw1bWWv: ['recmIRSmuXLDmnzWhhAbxWDX'],
            fldFZNNAQ6az72H5HttdWJc3: ['rec2og0ZOAcdhoAc7PSev9Uw'],
            fldGeiWitB2ZEGHJDsBDobgB: ['recZECEAU4wPmWioliQkvFNk'],
            fldIeYVxmhIC6vOpEs9jDZGH: ['rec9GXl6NQmaFMBguv7wOWxO'],
            fldPFOV0p8rMtTCoatVk1Dsc: null,
            fldQYq7ECtsWhqilBIaYovCX:
              'The team went above and beyond to assist me with my issue. The product also works perfectly.',
            fldVsRmZgR9iQyMXbkO3PUBC: 'Fantastic service',
            fldXHgxLyzmETekp69eIyrzs: ['optcQAWn0ZhZ83ESCMBGiLWO'],
            fldZ2zOGztkym76rov37cJK0: ['rec2og0ZOAcdhoAc7PSev9Uw'],
            fldoV2XKHRPDMAae2FhJRfze: null,
            fldp9Xi2nyB9H9USHOBJA8kl: ['recmIRSmuXLDmnzWhhAbxWDX'],
            fldpNJjQMmpZq4XgxsRAEVpF: ['recZSEXHOae959bNWE6JObHJ'],
            fldplqUCAtUlczUAd18MiglB: 5,
            fldqGkkEWNmACiimoszOQbbW: ['rec2og0ZOAcdhoAc7PSev9Uw'],
            fldqkrGlTfROqjwqhu3RbtMq: ['rec2og0ZOAcdhoAc7PSev9Uw'],
            fldrRoGUybFuu60fahhxSLo5: ['rec2og0ZOAcdhoAc7PSev9Uw'],
            fldrS2y2KqRIqL3KAA09TfnB: ['optQ5vz5xq5ZTma75LEcxp67'],
            flduYE8lg6lyTHMzGDYecW5t: ['rec9GXl6NQmaFMBguv7wOWxO'],
          },
          values: {
            fld198ulzfQ0MwnSGknvTc3s: ['Exceptional customer experience'],
            fld4zvFc5s6rEjnDVRPoKoGQ: ['Pricing Strategy'],
            fld5pmChjmj6ZgQzlTZ9S54X: ['Fantastic service'],
            fld8ACP0qVxoouQyOZHuw6qs: ['Pricing Strategy'],
            fld8AbTJf7kRGDwMp4SCICMD: '2024-11-12',
            fldCal7LTvbiLOGfkIw1bWWv: ['Smart Watch'],
            fldFZNNAQ6az72H5HttdWJc3: ['Fantastic service'],
            fldGeiWitB2ZEGHJDsBDobgB: ['Pricing Strategy'],
            fldIeYVxmhIC6vOpEs9jDZGH: ['Positive'],
            fldPFOV0p8rMtTCoatVk1Dsc: [null],
            fldQYq7ECtsWhqilBIaYovCX:
              'The team went above and beyond to assist me with my issue. The product also works perfectly.',
            fldVsRmZgR9iQyMXbkO3PUBC: 'Fantastic service',
            fldXHgxLyzmETekp69eIyrzs: ['Positive'],
            fldZ2zOGztkym76rov37cJK0: ['Fantastic service'],
            fldoV2XKHRPDMAae2FhJRfze: ['Exceptional customer experience'],
            fldp9Xi2nyB9H9USHOBJA8kl: ['Smart Watch'],
            fldpNJjQMmpZq4XgxsRAEVpF: ['Exceptional customer experience'],
            fldplqUCAtUlczUAd18MiglB: '5',
            fldqGkkEWNmACiimoszOQbbW: ['Fantastic service'],
            fldqkrGlTfROqjwqhu3RbtMq: ['Fantastic service'],
            fldrRoGUybFuu60fahhxSLo5: ['Fantastic service'],
            fldrS2y2KqRIqL3KAA09TfnB: ['Email'],
            flduYE8lg6lyTHMzGDYecW5t: ['Positive'],
          },
        },
        {
          templateId: 'rec25xD7f3F9Cm2kgPJNdoKb',
          data: {
            fld198ulzfQ0MwnSGknvTc3s: ['rec9VdL9QhZzkGeVJ8PVSrkx'],
            fld4zvFc5s6rEjnDVRPoKoGQ: ['recFpJ54q4aKrJTeBdVh3nqE'],
            fld5pmChjmj6ZgQzlTZ9S54X: ['rec25xD7f3F9Cm2kgPJNdoKb'],
            fld8ACP0qVxoouQyOZHuw6qs: null,
            fld8AbTJf7kRGDwMp4SCICMD: '2024-10-09T16:00:00.000Z',
            fldCal7LTvbiLOGfkIw1bWWv: ['recN9Apw8gfusuHR25i8TXYY'],
            fldFZNNAQ6az72H5HttdWJc3: ['rec25xD7f3F9Cm2kgPJNdoKb'],
            fldGZ9pZmKU4jb9BPfWBX8VB: null,
            fldGeiWitB2ZEGHJDsBDobgB: ['recFpJ54q4aKrJTeBdVh3nqE'],
            fldIeYVxmhIC6vOpEs9jDZGH: ['rec1PwdGk5YZia8yozlCtS5y'],
            fldNpnWFDfMHcniHZ8cjV7OE: null,
            fldPFOV0p8rMtTCoatVk1Dsc: null,
            fldQYq7ECtsWhqilBIaYovCX:
              'The design is not user-friendly, and it feels flimsy. I regret purchasing this product.',
            fldVsRmZgR9iQyMXbkO3PUBC: 'Poor design issue',
            fldXHgxLyzmETekp69eIyrzs: ['opt9OOr8dXbK1ZhutDBIFTs9'],
            fldZ2zOGztkym76rov37cJK0: ['rec25xD7f3F9Cm2kgPJNdoKb'],
            fldoV2XKHRPDMAae2FhJRfze: null,
            fldp9Xi2nyB9H9USHOBJA8kl: ['rece0TbB25W1Owr5qiETLHXq'],
            fldpNJjQMmpZq4XgxsRAEVpF: ['rec9VdL9QhZzkGeVJ8PVSrkx'],
            fldplqUCAtUlczUAd18MiglB: 2,
            fldqGkkEWNmACiimoszOQbbW: ['rec25xD7f3F9Cm2kgPJNdoKb'],
            fldqkrGlTfROqjwqhu3RbtMq: ['rec25xD7f3F9Cm2kgPJNdoKb'],
            fldrRoGUybFuu60fahhxSLo5: ['rec25xD7f3F9Cm2kgPJNdoKb'],
            fldrS2y2KqRIqL3KAA09TfnB: ['opt64tkStDLbyLMEEMOS1ybK'],
            flduYE8lg6lyTHMzGDYecW5t: ['rec1PwdGk5YZia8yozlCtS5y'],
          },
          values: {
            fld198ulzfQ0MwnSGknvTc3s: ['Improve product design'],
            fld4zvFc5s6rEjnDVRPoKoGQ: ['Product Design'],
            fld5pmChjmj6ZgQzlTZ9S54X: ['Poor design issue'],
            fld8ACP0qVxoouQyOZHuw6qs: ['Product Design'],
            fld8AbTJf7kRGDwMp4SCICMD: '2024-10-09',
            fldCal7LTvbiLOGfkIw1bWWv: ['Tablet Computer'],
            fldFZNNAQ6az72H5HttdWJc3: ['Poor design issue'],
            fldGZ9pZmKU4jb9BPfWBX8VB: 'Yes',
            fldGeiWitB2ZEGHJDsBDobgB: ['Product Design'],
            fldIeYVxmhIC6vOpEs9jDZGH: ['Negative'],
            fldNpnWFDfMHcniHZ8cjV7OE: 'No',
            fldPFOV0p8rMtTCoatVk1Dsc: [null],
            fldQYq7ECtsWhqilBIaYovCX:
              'The design is not user-friendly, and it feels flimsy. I regret purchasing this product.',
            fldVsRmZgR9iQyMXbkO3PUBC: 'Poor design issue',
            fldXHgxLyzmETekp69eIyrzs: ['Negative'],
            fldZ2zOGztkym76rov37cJK0: ['Poor design issue'],
            fldoV2XKHRPDMAae2FhJRfze: ['Improve product design'],
            fldp9Xi2nyB9H9USHOBJA8kl: ['Laptop'],
            fldpNJjQMmpZq4XgxsRAEVpF: ['Improve product design'],
            fldplqUCAtUlczUAd18MiglB: '2',
            fldqGkkEWNmACiimoszOQbbW: ['Poor design issue'],
            fldqkrGlTfROqjwqhu3RbtMq: ['Poor design issue'],
            fldrRoGUybFuu60fahhxSLo5: ['Poor design issue'],
            fldrS2y2KqRIqL3KAA09TfnB: ['Social media'],
            flduYE8lg6lyTHMzGDYecW5t: ['Negative'],
          },
        },
        {
          templateId: 'recpBIOKmbArKEate4XisWCO',
          data: {
            fld198ulzfQ0MwnSGknvTc3s: ['recx4psPoaCDZvZwMzzBqzIq'],
            fld4zvFc5s6rEjnDVRPoKoGQ: ['recQ1VX5rx7SdhpMwl7C8wnc'],
            fld5pmChjmj6ZgQzlTZ9S54X: ['recpBIOKmbArKEate4XisWCO'],
            fld8ACP0qVxoouQyOZHuw6qs: null,
            fld8AbTJf7kRGDwMp4SCICMD: '2024-08-07T16:00:00.000Z',
            fldCal7LTvbiLOGfkIw1bWWv: ['rec4mcF5HAR2aORM5WyvmAgq'],
            fldFZNNAQ6az72H5HttdWJc3: ['recpBIOKmbArKEate4XisWCO'],
            fldGeiWitB2ZEGHJDsBDobgB: ['recQ1VX5rx7SdhpMwl7C8wnc'],
            fldIeYVxmhIC6vOpEs9jDZGH: ['rec1OufAr7I8qBTLqxIeMLo0'],
            fldPFOV0p8rMtTCoatVk1Dsc: null,
            fldQYq7ECtsWhqilBIaYovCX:
              'The product works fine, but the delivery took too long, and the packaging was damaged upon arrival.',
            fldVsRmZgR9iQyMXbkO3PUBC: 'Average experience',
            fldXHgxLyzmETekp69eIyrzs: ['optW3ZokweidfqtVaWNAebVg'],
            fldZ2zOGztkym76rov37cJK0: ['recpBIOKmbArKEate4XisWCO'],
            fldoV2XKHRPDMAae2FhJRfze: null,
            fldp9Xi2nyB9H9USHOBJA8kl: ['recN9Apw8gfusuHR25i8TXYY'],
            fldpNJjQMmpZq4XgxsRAEVpF: ['recx4psPoaCDZvZwMzzBqzIq'],
            fldplqUCAtUlczUAd18MiglB: 3,
            fldqGkkEWNmACiimoszOQbbW: ['recpBIOKmbArKEate4XisWCO'],
            fldqkrGlTfROqjwqhu3RbtMq: ['recpBIOKmbArKEate4XisWCO'],
            fldrRoGUybFuu60fahhxSLo5: ['recpBIOKmbArKEate4XisWCO'],
            fldrS2y2KqRIqL3KAA09TfnB: ['opt44a5VaRFU8ueEmSVYPE1Q'],
            flduYE8lg6lyTHMzGDYecW5t: ['rec1OufAr7I8qBTLqxIeMLo0'],
          },
          values: {
            fld198ulzfQ0MwnSGknvTc3s: ['Improve delivery speed'],
            fld4zvFc5s6rEjnDVRPoKoGQ: ['Delivery Experience'],
            fld5pmChjmj6ZgQzlTZ9S54X: ['Average experience'],
            fld8ACP0qVxoouQyOZHuw6qs: ['Delivery Experience'],
            fld8AbTJf7kRGDwMp4SCICMD: '2024-08-07',
            fldCal7LTvbiLOGfkIw1bWWv: ['Wireless Earphones'],
            fldFZNNAQ6az72H5HttdWJc3: ['Average experience'],
            fldGeiWitB2ZEGHJDsBDobgB: ['Delivery Experience'],
            fldIeYVxmhIC6vOpEs9jDZGH: ['Neutral'],
            fldPFOV0p8rMtTCoatVk1Dsc: [null],
            fldQYq7ECtsWhqilBIaYovCX:
              'The product works fine, but the delivery took too long, and the packaging was damaged upon arrival.',
            fldVsRmZgR9iQyMXbkO3PUBC: 'Average experience',
            fldXHgxLyzmETekp69eIyrzs: ['Neutral'],
            fldZ2zOGztkym76rov37cJK0: ['Average experience'],
            fldoV2XKHRPDMAae2FhJRfze: ['Improve delivery speed'],
            fldp9Xi2nyB9H9USHOBJA8kl: ['Tablet Computer'],
            fldpNJjQMmpZq4XgxsRAEVpF: ['Improve delivery speed'],
            fldplqUCAtUlczUAd18MiglB: '3',
            fldqGkkEWNmACiimoszOQbbW: ['Average experience'],
            fldqkrGlTfROqjwqhu3RbtMq: ['Average experience'],
            fldrRoGUybFuu60fahhxSLo5: ['Average experience'],
            fldrS2y2KqRIqL3KAA09TfnB: ['App'],
            flduYE8lg6lyTHMzGDYecW5t: ['Neutral'],
          },
        },
        {
          templateId: 'rechERrRh2UzoE8DAffYyjXP',
          data: {
            fld198ulzfQ0MwnSGknvTc3s: ['rec2MqM3OokZvYirYyAP3fJ0'],
            fld4zvFc5s6rEjnDVRPoKoGQ: ['recMcS0RwEkQaCNLHJIi95P5'],
            fld5pmChjmj6ZgQzlTZ9S54X: ['rechERrRh2UzoE8DAffYyjXP'],
            fld8ACP0qVxoouQyOZHuw6qs: null,
            fld8AbTJf7kRGDwMp4SCICMD: '2024-10-15T16:00:00.000Z',
            fldCal7LTvbiLOGfkIw1bWWv: ['recDnGu0Q02NKeWztE56cOlm'],
            fldFZNNAQ6az72H5HttdWJc3: ['rechERrRh2UzoE8DAffYyjXP'],
            fldGeiWitB2ZEGHJDsBDobgB: ['recMcS0RwEkQaCNLHJIi95P5'],
            fldIeYVxmhIC6vOpEs9jDZGH: ['rec9GXl6NQmaFMBguv7wOWxO'],
            fldNpnWFDfMHcniHZ8cjV7OE: 'es',
            fldPFOV0p8rMtTCoatVk1Dsc: null,
            fldQYq7ECtsWhqilBIaYovCX:
              'The product quality is excellent, and customer service was very responsive. Highly recommend this to everyone!',
            fldVsRmZgR9iQyMXbkO3PUBC: 'Great product!',
            fldXHgxLyzmETekp69eIyrzs: ['optcQAWn0ZhZ83ESCMBGiLWO'],
            fldZ2zOGztkym76rov37cJK0: ['rechERrRh2UzoE8DAffYyjXP'],
            fldoV2XKHRPDMAae2FhJRfze: null,
            fldp9Xi2nyB9H9USHOBJA8kl: ['recDnGu0Q02NKeWztE56cOlm'],
            fldpNJjQMmpZq4XgxsRAEVpF: ['rec2MqM3OokZvYirYyAP3fJ0'],
            fldplqUCAtUlczUAd18MiglB: 5,
            fldqGkkEWNmACiimoszOQbbW: ['rechERrRh2UzoE8DAffYyjXP'],
            fldqkrGlTfROqjwqhu3RbtMq: ['rechERrRh2UzoE8DAffYyjXP'],
            fldrRoGUybFuu60fahhxSLo5: ['rechERrRh2UzoE8DAffYyjXP'],
            fldrS2y2KqRIqL3KAA09TfnB: ['optRBViyHoXLPP6tfW9K4osG'],
            flduYE8lg6lyTHMzGDYecW5t: ['rec9GXl6NQmaFMBguv7wOWxO'],
          },
          values: {
            fld198ulzfQ0MwnSGknvTc3s: ['Great customer support'],
            fld4zvFc5s6rEjnDVRPoKoGQ: ['Customer Service'],
            fld5pmChjmj6ZgQzlTZ9S54X: ['Great product!'],
            fld8ACP0qVxoouQyOZHuw6qs: ['Customer Service'],
            fld8AbTJf7kRGDwMp4SCICMD: '2024-10-15',
            fldCal7LTvbiLOGfkIw1bWWv: ['Smart Speaker'],
            fldFZNNAQ6az72H5HttdWJc3: ['Great product!'],
            fldGeiWitB2ZEGHJDsBDobgB: ['Customer Service'],
            fldIeYVxmhIC6vOpEs9jDZGH: ['Positive'],
            fldNpnWFDfMHcniHZ8cjV7OE: 'No',
            fldPFOV0p8rMtTCoatVk1Dsc: [null],
            fldQYq7ECtsWhqilBIaYovCX:
              'The product quality is excellent, and customer service was very responsive. Highly recommend this to everyone!',
            fldVsRmZgR9iQyMXbkO3PUBC: 'Great product!',
            fldXHgxLyzmETekp69eIyrzs: ['Positive'],
            fldZ2zOGztkym76rov37cJK0: ['Great product!'],
            fldoV2XKHRPDMAae2FhJRfze: ['Great customer support'],
            fldp9Xi2nyB9H9USHOBJA8kl: ['Smart Speaker'],
            fldpNJjQMmpZq4XgxsRAEVpF: ['Great customer support'],
            fldplqUCAtUlczUAd18MiglB: '5',
            fldqGkkEWNmACiimoszOQbbW: ['Great product!'],
            fldqkrGlTfROqjwqhu3RbtMq: ['Great product!'],
            fldrRoGUybFuu60fahhxSLo5: ['Great product!'],
            fldrS2y2KqRIqL3KAA09TfnB: ['Website'],
            flduYE8lg6lyTHMzGDYecW5t: ['Positive'],
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
